#!/bin/bash
set -e

# Check if the template exists
if [ ! -f /data/homeserver.yaml.template ]; then
  echo "Template file not found. Generating default configuration..."
  python -m synapse.app.homeserver \
    --generate-config \
    --server-name=${MATRIX_DOMAIN:-nxtacre.local} \
    --config-path=/data/homeserver.yaml \
    --report-stats=no
  
  # Modify the generated config to use PostgreSQL
  sed -i 's/sqlite:\/\/\/data\/homeserver.db/postgresql:\/\/${DB_USER:-postgres}:${DB_PASSWORD:-postgres}@${DB_HOST:-postgres}:${DB_PORT:-5432}\/${DB_NAME:-farmbooks}?options=-c%20search_path=matrix/g' /data/homeserver.yaml
  
  echo "Default configuration generated."
  exit 0
fi

echo "Processing template file..."

# Create a temporary file
TEMP_FILE=$(mktemp)

# Process the template file and replace environment variables
envsubst < /data/homeserver.yaml.template > "$TEMP_FILE"

# Check if the processed file is valid YAML
if command -v python3 &> /dev/null; then
  echo "Validating YAML..."
  if ! python3 -c "import yaml; yaml.safe_load(open('$TEMP_FILE'))"; then
    echo "Error: Generated configuration is not valid YAML."
    exit 1
  fi
fi

# Move the processed file to the final location
mv "$TEMP_FILE" /data/homeserver.yaml
chmod 600 /data/homeserver.yaml

echo "Configuration generated successfully."

# Create log config if it doesn't exist
if [ ! -f /data/log.config ]; then
  echo "Creating default log configuration..."
  cat > /data/log.config << EOF
version: 1
formatters:
  precise:
    format: '%(asctime)s - %(name)s - %(lineno)d - %(levelname)s - %(request)s - %(message)s'
handlers:
  console:
    class: logging.StreamHandler
    formatter: precise
loggers:
  synapse:
    level: INFO
  synapse.storage.SQL:
    level: INFO
root:
  level: INFO
  handlers: [console]
EOF
  echo "Default log configuration created."
fi

# Create media store directory if it doesn't exist
if [ ! -d /data/media_store ]; then
  echo "Creating media store directory..."
  mkdir -p /data/media_store
  chmod 700 /data/media_store
  echo "Media store directory created."
fi

echo "Configuration setup complete."