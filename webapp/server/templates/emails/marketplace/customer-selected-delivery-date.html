<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Customer Selected Delivery Date</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #3498db;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #3498db;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .order-details {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .customer-info {
      margin-top: 20px;
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 15px;
      background-color: white;
    }
    .delivery-date {
      background-color: #e8f4fd;
      border-left: 4px solid #3498db;
      padding: 15px;
      margin: 20px 0;
      font-size: 18px;
      text-align: center;
    }
    .calendar-icon {
      font-size: 24px;
      margin-right: 10px;
      vertical-align: middle;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Customer Selected Delivery Date</h1>
    </div>
    <div class="content">
      <p>Hello {{farmAdminName}},</p>
      
      <p>A customer has selected a delivery date for their order.</p>
      
      <div class="delivery-date">
        <span class="calendar-icon">📅</span> <strong>Selected Delivery Date:</strong> {{deliveryDate}} {{deliveryTimeWindow}}
      </div>
      
      <p><strong>Customer Information:</strong></p>
      <div class="customer-info">
        <p><strong>Name:</strong> {{customerName}}</p>
        <p><strong>Email:</strong> {{customerEmail}}</p>
        <p><strong>Phone:</strong> {{customerPhone}}</p>
      </div>
      
      <p><strong>Order Details:</strong></p>
      <div class="order-details">
        <p><strong>Order ID:</strong> {{orderRequestId}}</p>
        <p><strong>Order Date:</strong> {{orderDate}}</p>
        
        <p><strong>Delivery Address:</strong><br>
          {{deliveryAddress}}<br>
          {{deliveryCity}}, {{deliveryState}} {{deliveryZipCode}}
        </p>
        
        {{#if deliveryInstructions}}
        <p><strong>Delivery Instructions:</strong> {{deliveryInstructions}}</p>
        {{/if}}
      </div>
      
      <p>Please ensure that this order is prepared and ready for delivery on the selected date.</p>
      
      <p>You can view the complete order details and manage delivery scheduling in your farm dashboard:</p>
      
      <a href="{{orderManagementUrl}}" class="button">View in Dashboard</a>
      
      <p>If you need to make any changes to the delivery schedule, please contact the customer directly.</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>