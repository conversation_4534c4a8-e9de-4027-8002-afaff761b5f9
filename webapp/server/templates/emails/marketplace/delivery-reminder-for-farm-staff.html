<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Delivery Reminder</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
    }
    .container {
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      background-color: #9b59b6;
      padding: 20px;
      text-align: center;
    }
    .header h1 {
      color: white;
      margin: 0;
      font-size: 24px;
    }
    .content {
      padding: 20px;
      background-color: #f9f9f9;
    }
    .footer {
      padding: 20px;
      text-align: center;
      font-size: 12px;
      color: #777;
    }
    .button {
      display: inline-block;
      background-color: #9b59b6;
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 4px;
      margin-top: 20px;
    }
    .reminder-box {
      background-color: #f8edff;
      border-left: 4px solid #9b59b6;
      padding: 15px;
      margin: 20px 0;
      font-size: 18px;
      text-align: center;
    }
    .alarm-icon {
      font-size: 24px;
      margin-right: 10px;
      vertical-align: middle;
    }
    .deliveries-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 20px;
    }
    .deliveries-table th {
      background-color: #f2f2f2;
      text-align: left;
      padding: 10px;
      border: 1px solid #ddd;
    }
    .deliveries-table td {
      padding: 10px;
      border: 1px solid #ddd;
    }
    .deliveries-table tr:nth-child(even) {
      background-color: #f9f9f9;
    }
    .summary-box {
      background-color: #f2f2f2;
      border-radius: 4px;
      padding: 15px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Delivery Reminder</h1>
    </div>
    <div class="content">
      <p>Hello {{farmStaffName}},</p>
      
      <div class="reminder-box">
        <span class="alarm-icon">⏰</span> <strong>Reminder:</strong> You have {{deliveryCount}} deliveries scheduled for {{deliveryDate}}
      </div>
      
      <p>Please ensure all orders are prepared and ready for delivery. Here's a summary of the scheduled deliveries:</p>
      
      <table class="deliveries-table">
        <thead>
          <tr>
            <th>Order ID</th>
            <th>Customer</th>
            <th>Delivery Time</th>
            <th>Items</th>
          </tr>
        </thead>
        <tbody>
          {{#each deliveries}}
          <tr>
            <td>{{orderId}}</td>
            <td>{{customerName}}</td>
            <td>{{deliveryTime}}</td>
            <td>{{itemCount}} items</td>
          </tr>
          {{/each}}
        </tbody>
      </table>
      
      <div class="summary-box">
        <p><strong>Summary:</strong></p>
        <ul>
          <p>Total orders: {{deliveryCount}}</p>
          <p>Total items to prepare: {{totalItems}}</p>
          <p>Delivery driver: {{driverName}}</p>
          <p>Estimated departure time: {{departureTime}}</p>
        </ul>
      </div>
      
      <p>You can view complete order details and manage deliveries in your farm dashboard:</p>
      
      <a href="{{deliveryManagementUrl}}" class="button">View Deliveries</a>
      
      <p>If you have any questions or need to make changes to the delivery schedule, please contact the farm manager.</p>
    </div>
    <div class="footer">
      <p>&copy; {{year}} NxtAcre. All rights reserved.</p>
      <p>This email was sent to {{email}}.</p>
    </div>
  </div>
</body>
</html>