import express from 'express';
import { authenticate } from '../middleware/index.js';
import {
  createFieldCollaboration,
  getFieldCollaborations,
  getCollaboratingFields,
  getSharedFields,
  updateFieldCollaboration,
  removeFieldCollaboration
} from '../controllers/farmFieldCollaborationController.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);

// Create a new field collaboration
router.post('/', createFieldCollaboration);

// Get all collaborations for a field
router.get('/field/:fieldId', getFieldCollaborations);

// Get all fields a farm is collaborating on (fields shared with this farm)
router.get('/farm/:farmId/collaborating', getCollaboratingFields);

// Get all fields a farm is sharing with others
router.get('/farm/:farmId/shared', getSharedFields);

// Update a collaboration's permission level
router.put('/:collaborationId', updateFieldCollaboration);

// Remove a collaboration
router.delete('/:collaborationId', removeFieldCollaboration);

export default router;