import express from 'express';
import {
  getAllMaintenanceSchedules,
  getMaintenanceSchedulesByType,
  getOverdueMaintenanceSchedules,
  getEquipmentMaintenanceSchedules,
  getMaintenanceScheduleById,
  createMaintenanceSchedule,
  updateMaintenanceSchedule,
  deleteMaintenanceSchedule,
  getEquipmentMaintenanceLogs,
  getMaintenanceLogById,
  createMaintenanceLog,
  updateMaintenanceLog,
  deleteMaintenanceLog
} from '../controllers/maintenanceController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// Maintenance Schedule Routes
// Get all maintenance schedules (across all equipment)
router.get('/schedules', authenticate, getAllMaintenanceSchedules);

// Get maintenance schedules by maintenance type
router.get('/schedules/type/:maintenanceType', authenticate, getMaintenanceSchedulesByType);

// Get overdue maintenance schedules
router.get('/schedules/overdue', authenticate, getOverdueMaintenanceSchedules);

// Get all maintenance schedules for a piece of equipment
router.get('/schedules/equipment/:equipmentId', authenticate, getEquipmentMaintenanceSchedules);

// Get a single maintenance schedule by ID
router.get('/schedules/:scheduleId', authenticate, getMaintenanceScheduleById);

// Create a new maintenance schedule
router.post('/schedules', authenticate, createMaintenanceSchedule);

// Update a maintenance schedule
router.put('/schedules/:scheduleId', authenticate, updateMaintenanceSchedule);

// Delete a maintenance schedule
router.delete('/schedules/:scheduleId', authenticate, deleteMaintenanceSchedule);

// Maintenance Log Routes
// Get all maintenance logs for a piece of equipment
router.get('/logs/equipment/:equipmentId', authenticate, getEquipmentMaintenanceLogs);

// Get a single maintenance log by ID
router.get('/logs/:logId', authenticate, getMaintenanceLogById);

// Create a new maintenance log
router.post('/logs', authenticate, createMaintenanceLog);

// Update a maintenance log
router.put('/logs/:logId', authenticate, updateMaintenanceLog);

// Delete a maintenance log
router.delete('/logs/:logId', authenticate, deleteMaintenanceLog);

export default router;
