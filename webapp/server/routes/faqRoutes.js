import express from 'express';
import {
  createFAQ,
  getFAQs,
  getFAQ,
  updateFAQ,
  deleteFAQ,
  getFAQCategories
} from '../controllers/faqController.js';
import { authenticate, isAdmin } from '../middleware/index.js';

const router = express.Router();

// Public routes
router.get('/', getFAQs);
router.get('/categories', getFAQCategories);
router.get('/:faqId', getFAQ);

// Admin-only routes (require authentication and admin privileges)
router.post('/', authenticate, isAdmin, createFAQ);
router.put('/:faqId', authenticate, isAdmin, updateFAQ);
router.delete('/:faqId', authenticate, isAdmin, deleteFAQ);

export default router;