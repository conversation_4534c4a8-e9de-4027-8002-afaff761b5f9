import express from 'express';
import {
  createUserFarm,
  updateUserFarm,
  deleteUserFarm,
  getUserFarmsByUserId,
  getUserFarmsByFarmId,
  getUserFarmById,
  leaveFarm,
  transferFarmOwnership
} from '../controllers/userFarmController.js';
import { authenticate, isAdmin } from '../middleware/authMiddleware.js';

const router = express.Router();

// Create a new UserFarm association
router.post('/', authenticate, createUserFarm);

// Update a UserFarm association
router.put('/:userFarmId', authenticate, updateUserFarm);

// Delete a UserFarm association
router.delete('/:userFarmId', authenticate, deleteUserFarm);

// Get UserFarm associations by user ID
router.get('/user/:userId', authenticate, getUserFarmsByUserId);

// Get UserFarm associations by farm ID
router.get('/farm/:farmId', authenticate, isAdmin, getUserFarmsByFarmId);

// Get a specific UserFarm by ID
router.get('/:id', authenticate, getUserFarmById);

// Allow a non-farm owner to leave a farm
router.delete('/leave/:farmId', authenticate, leaveFarm);

// Transfer farm ownership to another user
router.post('/transfer-ownership/:farmId', authenticate, transferFarmOwnership);

export default router;
