import express from 'express';
import {
  createDelivery,
  getDeliveries,
  getDeliveryById,
  updateDelivery,
  deleteDelivery,
  updateDeliveryStatus
} from '../controllers/deliveryController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

// All routes require authentication
router.post('/', authenticate, createDelivery);
router.get('/', authenticate, getDeliveries);
router.get('/:deliveryId', authenticate, getDeliveryById);
router.put('/:deliveryId', authenticate, updateDelivery);
router.delete('/:deliveryId', authenticate, deleteDelivery);
router.patch('/:deliveryId/status', authenticate, updateDeliveryStatus);

export default router;