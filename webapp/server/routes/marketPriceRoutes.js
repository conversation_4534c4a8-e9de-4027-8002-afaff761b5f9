import express from 'express';
import axios from 'axios';
import dotenv from 'dotenv';
import { Op } from 'sequelize';
import MarketPrice from '../models/MarketPrice.js';
import FuturePrice from '../models/FuturePrice.js';
import HistoricalPrice from '../models/HistoricalPrice.js';
import PriceAlert from '../models/PriceAlert.js';

dotenv.config();

const router = express.Router();

// USDA Agricultural Marketing Service (AMS) API base URL
const AMS_API_BASE = process.env.AMS_API_URL || 'https://marsapi.ams.usda.gov/services/v1.2';
const AMS_API_KEY = process.env.AMS_API_KEY || 'DEMO_KEY'; // Use DEMO_KEY for testing or get a key from USDA

// Data.gov USDA Agricultural Marketing Service API
const DATA_GOV_API_BASE = process.env.DATA_GOV_API_URL || 'https://api.data.gov/USDA/USDA_AMS';
const DATA_GOV_API_KEY = process.env.DATA_GOV_API_KEY || 'DEMO_KEY'; // Use DEMO_KEY for testing or get a key from data.gov

// USDA Economic Research Service (ERS) API
const ERS_API_BASE = process.env.ERS_API_URL || 'https://api.ers.usda.gov/data';
const ERS_API_KEY = process.env.ERS_API_KEY || process.env.DATA_GOV_API_KEY || ''; // Use DATA_GOV_API_KEY as fallback

// USDA National Agricultural Statistics Service (NASS) API
const NASS_API_BASE = process.env.NASS_API_URL || 'https://quickstats.nass.usda.gov/api';
const NASS_API_KEY = process.env.NASS_API_KEY || process.env.DATA_GOV_API_KEY || ''; // Use DATA_GOV_API_KEY as fallback

// Configure AMS API client
const amsClient = axios.create({
  baseURL: AMS_API_BASE,
  params: {
    api_key: AMS_API_KEY
  },
  headers: {
    'Content-Type': 'application/json'
  }
});

// Configure Data.gov API client
const dataGovClient = axios.create({
  baseURL: DATA_GOV_API_BASE,
  params: {
    api_key: DATA_GOV_API_KEY
  },
  headers: {
    'Content-Type': 'application/json'
  }
});

// Configure ERS API client
const ersClient = axios.create({
  baseURL: ERS_API_BASE,
  params: {
    api_key: ERS_API_KEY
  },
  headers: {
    'Content-Type': 'application/json'
  }
});

// Configure NASS API client
const nassClient = axios.create({
  baseURL: NASS_API_BASE,
  params: {
    key: NASS_API_KEY
  },
  headers: {
    'Content-Type': 'application/json'
  }
});

// Helper functions for caching
const CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

// Check if we have valid cached data for current prices
const getMarketPriceFromCache = async (commodity) => {
  try {
    const now = new Date();
    const cachedPrice = await MarketPrice.findOne({
      where: {
        commodity: commodity,
        is_cached: true,
        cache_expiry: {
          [Op.gt]: now
        }
      },
      order: [['updated_at', 'DESC']]
    });

    return cachedPrice;
  } catch (error) {
    console.error(`Error checking cache for ${commodity}:`, error);
    return null;
  }
};

// Save market price to cache
const saveMarketPriceToCache = async (priceData) => {
  try {
    const cacheExpiry = new Date();
    cacheExpiry.setTime(cacheExpiry.getTime() + CACHE_DURATION);

    await MarketPrice.create({
      commodity: priceData.commodity,
      price: priceData.price,
      unit: priceData.unit,
      date: priceData.date,
      location: priceData.location,
      trend: priceData.trend,
      percent_change: priceData.percentChange,
      source: priceData.source || 'API',
      api_source: priceData.api_source,
      is_cached: true,
      cache_expiry: cacheExpiry
    });

    console.log(`Cached market price for ${priceData.commodity}: $${priceData.price} per ${priceData.unit}`);
  } catch (error) {
    console.error(`Error saving to cache for ${priceData.commodity}:`, error);
  }
};

// Check if we have valid cached data for future prices
const getFuturePriceFromCache = async (commodity, month, year) => {
  try {
    const now = new Date();
    const cachedPrice = await FuturePrice.findOne({
      where: {
        commodity: commodity,
        month: month,
        year: year,
        is_cached: true,
        cache_expiry: {
          [Op.gt]: now
        }
      },
      order: [['updated_at', 'DESC']]
    });

    return cachedPrice;
  } catch (error) {
    console.error(`Error checking future price cache for ${commodity} ${month} ${year}:`, error);
    return null;
  }
};

// Save future price to cache
const saveFuturePriceToCache = async (futureData) => {
  try {
    const cacheExpiry = new Date();
    cacheExpiry.setTime(cacheExpiry.getTime() + CACHE_DURATION);

    await FuturePrice.create({
      commodity: futureData.commodity,
      price: futureData.price,
      unit: futureData.unit,
      month: futureData.month,
      year: futureData.year,
      exchange: futureData.exchange,
      source: futureData.source || 'API',
      api_source: futureData.api_source,
      is_cached: true,
      cache_expiry: cacheExpiry
    });

    console.log(`Cached future price for ${futureData.commodity} ${futureData.month} ${futureData.year}: $${futureData.price}`);
  } catch (error) {
    console.error(`Error saving future price to cache for ${futureData.commodity}:`, error);
  }
};

// Check if we have valid cached data for historical prices
const getHistoricalPriceFromCache = async (commodity, date) => {
  try {
    const now = new Date();
    const cachedPrice = await HistoricalPrice.findOne({
      where: {
        commodity: commodity,
        date: date,
        is_cached: true,
        cache_expiry: {
          [Op.gt]: now
        }
      },
      order: [['updated_at', 'DESC']]
    });

    return cachedPrice;
  } catch (error) {
    console.error(`Error checking historical price cache for ${commodity} on ${date}:`, error);
    return null;
  }
};

// Save historical price to cache
const saveHistoricalPriceToCache = async (priceData) => {
  try {
    const cacheExpiry = new Date();
    cacheExpiry.setTime(cacheExpiry.getTime() + CACHE_DURATION);

    await HistoricalPrice.create({
      commodity: priceData.commodity,
      price: priceData.price,
      unit: priceData.unit,
      date: priceData.date,
      location: priceData.location,
      trend: priceData.trend,
      percent_change: priceData.percentChange,
      source: priceData.source || 'API',
      api_source: priceData.api_source,
      is_cached: true,
      cache_expiry: cacheExpiry
    });

    console.log(`Cached historical price for ${priceData.commodity} on ${priceData.date}: $${priceData.price}`);
  } catch (error) {
    console.error(`Error saving historical price to cache for ${priceData.commodity}:`, error);
  }
};

// Helper function to get market prices from data.gov
const getDataGovMarketPrices = async (commodityType) => {
  try {
    console.log(`Fetching ${commodityType} prices from data.gov USDA AMS API`);

    // Map our commodity types to data.gov commodity codes
    const commodityCode = mapCommodityToDataGovCode(commodityType);

    if (!commodityCode) {
      console.log(`No data.gov commodity code mapping for ${commodityType}, skipping data.gov API call`);
      return null;
    }

    // Fetch the latest market report for this commodity
    const response = await dataGovClient.get('/LPSMarketReports', {
      params: {
        commodity: commodityCode,
        limit: 1,
        sort: 'report_date desc' // Get the most recent report
      }
    });

    if (response.data && response.data.results && response.data.results.length > 0) {
      const report = response.data.results[0];

      // Extract price information from the report
      // The structure of the data may vary by commodity, so we need to handle different formats
      let price = null;
      let previousPrice = null;
      let unit = getCommodityUnit(commodityType);
      let location = 'National';

      // Extract price data based on the report structure
      if (report.weighted_avg) {
        price = parseFloat(report.weighted_avg);
      } else if (report.price) {
        price = parseFloat(report.price);
      } else if (report.avg_price) {
        price = parseFloat(report.avg_price);
      }

      // Try to get previous price for comparison
      if (report.prev_weighted_avg) {
        previousPrice = parseFloat(report.prev_weighted_avg);
      } else if (report.prev_price) {
        previousPrice = parseFloat(report.prev_price);
      } else if (report.prev_avg_price) {
        previousPrice = parseFloat(report.prev_avg_price);
      }

      // If we have location information, use it
      if (report.market_location) {
        location = report.market_location;
      } else if (report.market_name) {
        location = report.market_name;
      }

      // If we couldn't extract a price, return null
      if (!price) {
        console.log(`Could not extract price from data.gov report for ${commodityType}`);
        return null;
      }

      // Calculate percent change if we have previous price
      let percentChange = 0;
      let trend = 'stable';

      if (previousPrice && price) {
        percentChange = ((price - previousPrice) / previousPrice) * 100;
        trend = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable';
      }

      return {
        commodity: commodityType,
        price: price,
        unit: unit,
        date: report.report_date || new Date().toISOString().split('T')[0],
        location: location,
        trend: trend,
        percentChange: parseFloat(percentChange.toFixed(2))
      };
    }

    console.log(`No data.gov market reports found for ${commodityType}`);
    return null;
  } catch (error) {
    console.error(`Error fetching ${commodityType} prices from data.gov:`, error.message);
    return null;
  }
};

// Helper function to map our commodity types to data.gov commodity codes
const mapCommodityToDataGovCode = (commodity) => {
  const mapping = {
    'beef': 'BEEF',
    'corn': 'CORN',
    'hay': 'HAY',
    'wheat': 'WHEAT',
    'soybeans': 'SOYBEANS',
    'cotton': 'COTTON',
    'rice': 'RICE',
    'oats': 'OATS',
    'barley': 'BARLEY'
  };

  return mapping[commodity.toLowerCase()] || null;
};

// Helper function to map our commodity types to ERS commodity codes
const mapCommodityToERSCode = (commodity) => {
  const mapping = {
    'beef': 'BEEF',
    'corn': 'CORN',
    'hay': 'HAY',
    'wheat': 'WHEAT',
    'soybeans': 'SOYB',
    'cotton': 'COTN',
    'rice': 'RICE',
    'oats': 'OATS',
    'barley': 'BARL'
  };

  return mapping[commodity.toLowerCase()] || null;
};

// Helper function to map our commodity types to NASS commodity codes
const mapCommodityToNASSCode = (commodity) => {
  const mapping = {
    'beef': 'CATTLE',
    'corn': 'CORN',
    'hay': 'HAY',
    'wheat': 'WHEAT',
    'soybeans': 'SOYBEANS',
    'cotton': 'COTTON',
    'rice': 'RICE',
    'oats': 'OATS',
    'barley': 'BARLEY'
  };

  return mapping[commodity.toLowerCase()] || null;
};

// Helper function to get the closest market to a location
const getClosestMarket = async (lat, lon, commodityType) => {
  try {
    // Try to fetch markets from the AMS API
    const response = await amsClient.get('/markets', {
      params: {
        lat: lat,
        lng: lon,
        radius: 100, // Search within 100 miles
        commodity: commodityType
      }
    });

    // If markets are found, return the closest one
    if (response.data && response.data.results && response.data.results.length > 0) {
      const closestMarket = response.data.results[0]; // Assuming results are sorted by distance
      return {
        marketId: closestMarket.id,
        name: closestMarket.name,
        distance: closestMarket.distance,
        location: closestMarket.city ? `${closestMarket.city}, ${closestMarket.state}` : closestMarket.location || 'Regional Market'
      };
    }

    // If no markets are found within 100 miles, try a wider radius
    console.log(`No markets found for ${commodityType} within 100 miles of lat=${lat}, lon=${lon}. Trying wider radius.`);

    const widerResponse = await amsClient.get('/markets', {
      params: {
        lat: lat,
        lng: lon,
        radius: 250, // Search within 250 miles
        commodity: commodityType
      }
    });

    if (widerResponse.data && widerResponse.data.results && widerResponse.data.results.length > 0) {
      const closestMarket = widerResponse.data.results[0];
      return {
        marketId: closestMarket.id,
        name: closestMarket.name,
        distance: closestMarket.distance,
        location: closestMarket.city ? `${closestMarket.city}, ${closestMarket.state}` : closestMarket.location || 'Regional Market'
      };
    }

    // If still no markets found, try to get a national market for this commodity
    console.log(`No markets found for ${commodityType} within 250 miles. Trying to find a national market.`);

    const nationalResponse = await amsClient.get('/markets', {
      params: {
        commodity: commodityType,
        national: true
      }
    });

    if (nationalResponse.data && nationalResponse.data.results && nationalResponse.data.results.length > 0) {
      const nationalMarket = nationalResponse.data.results[0];
      return {
        marketId: nationalMarket.id,
        name: nationalMarket.name,
        distance: null, // Distance not applicable for national market
        location: 'National Market'
      };
    }

    // If no markets are found at all, use a default national market ID
    console.log(`No markets found for ${commodityType} at all. Using default national market.`);
    return {
      marketId: `NAT_${commodityType.toUpperCase()}`,
      name: `National ${commodityType.charAt(0).toUpperCase() + commodityType.slice(1)} Market`,
      distance: null,
      location: 'National Market'
    };
  } catch (error) {
    console.error('Error finding closest market:', error);

    // If API call fails, try to use a cached market if available
    try {
      const cachedPrice = await getMarketPriceFromCache(commodityType);
      if (cachedPrice && cachedPrice.market_id) {
        console.log(`Using cached market ID for ${commodityType}: ${cachedPrice.market_id}`);
        return {
          marketId: cachedPrice.market_id,
          name: cachedPrice.market_name || `${commodityType.charAt(0).toUpperCase() + commodityType.slice(1)} Market`,
          distance: null,
          location: cachedPrice.location || 'National Market'
        };
      }
    } catch (cacheError) {
      console.error('Error checking cache for market:', cacheError);
    }

    // If all else fails, use a default national market ID
    console.log(`API call failed for ${commodityType}. Using default national market.`);
    return {
      marketId: `NAT_${commodityType.toUpperCase()}`,
      name: `National ${commodityType.charAt(0).toUpperCase() + commodityType.slice(1)} Market`,
      distance: null,
      location: 'National Market'
    };
  }
};

// Helper function to get current prices for a commodity
const getCurrentPrices = async (commodityType, marketId) => {
  try {
    // First, check if we have valid cached data
    const cachedPrice = await getMarketPriceFromCache(commodityType);

    if (cachedPrice) {
      console.log(`Using cached price for ${commodityType}: $${cachedPrice.price} per ${cachedPrice.unit} (expires: ${cachedPrice.cache_expiry})`);
      return {
        commodity: cachedPrice.commodity,
        price: parseFloat(cachedPrice.price),
        unit: cachedPrice.unit,
        date: cachedPrice.date,
        location: cachedPrice.location,
        trend: cachedPrice.trend,
        percentChange: parseFloat(cachedPrice.percent_change),
        source: cachedPrice.source,
        api_source: cachedPrice.api_source,
        market_id: cachedPrice.market_id || marketId
      };
    }

    console.log(`No cached data found for ${commodityType}, fetching from APIs`);

    // Try to fetch prices from data.gov
    let dataGovPrices = null;
    try {
      console.log(`Attempting to fetch ${commodityType} prices from data.gov with API key: ${DATA_GOV_API_KEY.substring(0, 5)}...`);
      dataGovPrices = await getDataGovMarketPrices(commodityType);
    } catch (dataGovError) {
      console.error(`Error fetching ${commodityType} prices from data.gov:`, dataGovError.message);
      console.error(`Data.gov API details - Base URL: ${DATA_GOV_API_BASE}, API Key prefix: ${DATA_GOV_API_KEY.substring(0, 5)}...`);
      // Continue to next data source
    }

    if (dataGovPrices) {
      console.log(`Using data.gov prices for ${commodityType}: $${dataGovPrices.price} per ${dataGovPrices.unit}`);

      // Add API source information
      dataGovPrices.api_source = 'DATA_GOV';
      dataGovPrices.market_id = marketId;

      // Save to cache
      await saveMarketPriceToCache(dataGovPrices);

      return dataGovPrices;
    }

    console.log(`No data.gov prices found for ${commodityType}, trying AMS API`);

    // Try to fetch current prices from the AMS API
    let amsResponse = null;
    try {
      console.log(`Attempting to fetch ${commodityType} prices from AMS API with API key: ${AMS_API_KEY.substring(0, 5)}...`);
      amsResponse = await amsClient.get('/commodities', {
        params: {
          commodity: commodityType,
          market: marketId
        }
      });
    } catch (amsError) {
      console.error(`Error fetching ${commodityType} prices from AMS API:`, amsError.message);
      console.error(`AMS API details - Base URL: ${AMS_API_BASE}, API Key prefix: ${AMS_API_KEY.substring(0, 5)}...`);
      // Continue to next data source
    }

    // If price data is found from AMS API, return it
    if (amsResponse && amsResponse.data && amsResponse.data.results && amsResponse.data.results.length > 0) {
      const priceData = amsResponse.data.results[0];

      // Calculate percent change if previous price is available
      let percentChange = 0;
      let trend = 'stable';

      if (priceData.previous_price && priceData.price) {
        percentChange = ((priceData.price - priceData.previous_price) / priceData.previous_price) * 100;
        trend = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable';
      }

      const result = {
        commodity: commodityType,
        price: parseFloat(priceData.price),
        unit: priceData.unit || getCommodityUnit(commodityType),
        date: priceData.date || new Date().toISOString().split('T')[0],
        location: priceData.location || 'Regional Market',
        trend: trend,
        percentChange: parseFloat(percentChange.toFixed(2)),
        api_source: 'AMS',
        market_id: marketId
      };

      // Save to cache
      await saveMarketPriceToCache(result);

      return result;
    }

    console.log(`No AMS API prices found for ${commodityType}, trying USDA ERS API`);

    // Try to fetch from USDA Economic Research Service (ERS) API as a third option
    let ersResponse = null;
    try {
      // Check if we have a valid ERS API key and commodity code
      const ersCode = mapCommodityToERSCode(commodityType);
      if (ERS_API_KEY && ersCode) {
        console.log(`Attempting to fetch ${commodityType} prices from USDA ERS API with API key: ${ERS_API_KEY.substring(0, 5)}...`);

        // Get current date in YYYY-MM-DD format
        const today = new Date().toISOString().split('T')[0];

        // Calculate date 30 days ago for recent price data
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const startDate = thirtyDaysAgo.toISOString().split('T')[0];

        // Fetch commodity price data from ERS API
        ersResponse = await ersClient.get('/commodities/prices', {
          params: {
            commodity_code: ersCode,
            start_date: startDate,
            end_date: today,
            format: 'json'
          }
        });

        // Process the response if we have data
        if (ersResponse.data && ersResponse.data.data && ersResponse.data.data.length > 0) {
          // Sort by date descending to get the most recent price
          const sortedData = [...ersResponse.data.data].sort((a, b) => 
            new Date(b.date) - new Date(a.date)
          );

          const mostRecentPrice = sortedData[0];

          // Calculate percent change if we have at least two data points
          let percentChange = 0;
          let trend = 'stable';

          if (sortedData.length > 1) {
            const previousPrice = sortedData[1].price;
            percentChange = ((mostRecentPrice.price - previousPrice) / previousPrice) * 100;
            trend = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable';
          }

          const result = {
            commodity: commodityType,
            price: parseFloat(mostRecentPrice.price),
            unit: mostRecentPrice.unit || getCommodityUnit(commodityType),
            date: mostRecentPrice.date,
            location: 'National Average',
            trend: trend,
            percentChange: parseFloat(percentChange.toFixed(2)),
            api_source: 'ERS',
            market_id: marketId
          };

          // Save to cache
          await saveMarketPriceToCache(result);

          return result;
        } else {
          console.log(`No price data found from ERS API for ${commodityType}`);
        }
      } else {
        console.log(`Skipping ERS API call for ${commodityType}: ${!ERS_API_KEY ? 'No API key' : 'No commodity code mapping'}`);
      }
    } catch (ersError) {
      console.error(`Error fetching ${commodityType} prices from USDA ERS API:`, ersError?.message);
      console.error(`ERS API details - Base URL: ${ERS_API_BASE}, API Key prefix: ${ERS_API_KEY ? ERS_API_KEY.substring(0, 5) : 'not set'}...`);
      // Continue to next data source
    }

    // Try to fetch from USDA National Agricultural Statistics Service (NASS) API as a fourth option
    let nassResponse = null;
    try {
      // Check if we have a valid NASS API key and commodity code
      const nassCode = mapCommodityToNASSCode(commodityType);
      if (NASS_API_KEY && nassCode) {
        console.log(`Attempting to fetch ${commodityType} prices from USDA NASS API with API key: ${NASS_API_KEY.substring(0, 5)}...`);

        // Get current year and previous year
        const currentYear = new Date().getFullYear();
        const previousYear = currentYear - 1;

        // Fetch commodity price data from NASS API
        nassResponse = await nassClient.get('/quickstats', {
          params: {
            commodity_desc: nassCode,
            statisticcat_desc: 'PRICE RECEIVED',
            year: `${previousYear},${currentYear}`,
            format: 'JSON'
          }
        });

        // Process the response if we have data
        if (nassResponse.data && nassResponse.data.data && nassResponse.data.data.length > 0) {
          // Sort by year and month descending to get the most recent price
          const sortedData = [...nassResponse.data.data].sort((a, b) => {
            if (a.year !== b.year) {
              return parseInt(b.year) - parseInt(a.year);
            }
            return parseInt(b.reference_period_desc) - parseInt(a.reference_period_desc);
          });

          const mostRecentPrice = sortedData[0];

          // Calculate percent change if we have at least two data points
          let percentChange = 0;
          let trend = 'stable';

          if (sortedData.length > 1) {
            const previousPrice = parseFloat(sortedData[1].Value);
            const currentPrice = parseFloat(mostRecentPrice.Value);
            percentChange = ((currentPrice - previousPrice) / previousPrice) * 100;
            trend = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable';
          }

          const result = {
            commodity: commodityType,
            price: parseFloat(mostRecentPrice.Value),
            unit: mostRecentPrice.unit_desc || getCommodityUnit(commodityType),
            date: `${mostRecentPrice.year}-${mostRecentPrice.reference_period_desc.padStart(2, '0')}-01`,
            location: mostRecentPrice.geo_level_desc === 'NATIONAL' ? 'National Average' : mostRecentPrice.state_name || 'Regional Market',
            trend: trend,
            percentChange: parseFloat(percentChange.toFixed(2)),
            api_source: 'NASS',
            market_id: marketId
          };

          // Save to cache
          await saveMarketPriceToCache(result);

          return result;
        } else {
          console.log(`No price data found from NASS API for ${commodityType}`);
        }
      } else {
        console.log(`Skipping NASS API call for ${commodityType}: ${!NASS_API_KEY ? 'No API key' : 'No commodity code mapping'}`);
      }
    } catch (nassError) {
      console.error(`Error fetching ${commodityType} prices from USDA NASS API:`, nassError?.message);
      console.error(`NASS API details - Base URL: ${NASS_API_BASE}, API Key prefix: ${NASS_API_KEY ? NASS_API_KEY.substring(0, 5) : 'not set'}...`);
      // Continue to next data source
    }

    // If we've tried all APIs and still don't have data, check if we have any historical data
    console.log(`No current API data found for ${commodityType}, checking for historical data`);

    try {
      // Get the most recent historical price from our database
      const historicalPrice = await HistoricalPrice.findOne({
        where: {
          commodity: commodityType
        },
        order: [['date', 'DESC']]
      });

      if (historicalPrice) {
        console.log(`Using most recent historical price for ${commodityType}: $${historicalPrice.price} per ${historicalPrice.unit} from ${historicalPrice.date}`);

        // Use historical data but mark it as such
        const result = {
          commodity: commodityType,
          price: parseFloat(historicalPrice.price),
          unit: historicalPrice.unit,
          date: historicalPrice.date,
          location: historicalPrice.location || 'National Average',
          trend: 'unknown', // Can't determine trend without current data
          percentChange: 0,
          api_source: 'HISTORICAL',
          market_id: marketId,
          isHistorical: true
        };

        // Don't cache this as current price data
        return result;
      }
    } catch (historyError) {
      console.error(`Error fetching historical price for ${commodityType}:`, historyError.message);
    }

    // If we still don't have data, use the base price as a last resort
    console.log(`No price data found for ${commodityType} from any source, using fallback base price`);
    console.log(`API Keys status - AMS: ${AMS_API_KEY ? 'Set' : 'Not set'}, Data.gov: ${DATA_GOV_API_KEY ? 'Set' : 'Not set'}, ERS: ${ERS_API_KEY ? 'Set' : 'Not set'}, NASS: ${NASS_API_KEY ? 'Set' : 'Not set'}`);

    const basePrice = getBasePriceForCommodity(commodityType);
    const unit = getCommodityUnit(commodityType);

    const fallbackResult = {
      commodity: commodityType,
      price: basePrice,
      unit: unit,
      date: new Date().toISOString().split('T')[0],
      location: 'National Average (Fallback Data)',
      trend: 'stable',
      percentChange: 0,
      api_source: 'FALLBACK',
      market_id: marketId,
      isFallback: true
    };

    // Don't cache fallback data
    return fallbackResult;
  } catch (error) {
    console.error(`Error getting current prices for ${commodityType}:`, error);

    // Instead of propagating the error, return a fallback price
    console.log(`Returning fallback price for ${commodityType} due to error: ${error.message}`);
    console.log(`API Keys status - AMS: ${AMS_API_KEY ? 'Set' : 'Not set'}, Data.gov: ${DATA_GOV_API_KEY ? 'Set' : 'Not set'}, ERS: ${ERS_API_KEY ? 'Set' : 'Not set'}, NASS: ${NASS_API_KEY ? 'Set' : 'Not set'}`);

    const basePrice = getBasePriceForCommodity(commodityType);
    const unit = getCommodityUnit(commodityType);

    return {
      commodity: commodityType,
      price: basePrice,
      unit: unit,
      date: new Date().toISOString().split('T')[0],
      location: 'National Average (Error Fallback)',
      trend: 'stable',
      percentChange: 0,
      api_source: 'ERROR_FALLBACK',
      market_id: marketId,
      isFallback: true,
      error: error.message
    };
  }
};

// Helper function to get future prices for a commodity
const getFuturePrices = async (commodityType) => {
  try {
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    // Array to store all futures
    const futures = [];

    // Check if we have cached futures for this commodity
    for (let i = 1; i <= 6; i++) {
      const futureMonth = (currentMonth + i) % 12;
      const futureYear = currentYear + Math.floor((currentMonth + i) / 12);
      const monthCode = months[futureMonth];
      const yearStr = futureYear.toString();

      // Check cache for this specific future
      const cachedFuture = await getFuturePriceFromCache(commodityType, monthCode, yearStr);

      if (cachedFuture) {
        console.log(`Using cached future price for ${commodityType} ${monthCode} ${yearStr}: $${cachedFuture.price}`);
        futures.push({
          commodity: cachedFuture.commodity,
          price: parseFloat(cachedFuture.price),
          unit: cachedFuture.unit,
          month: cachedFuture.month,
          year: cachedFuture.year,
          exchange: cachedFuture.exchange,
          source: cachedFuture.source,
          api_source: cachedFuture.api_source
        });
      } else {
        // No cache for this future, we'll need to fetch it
        console.log(`No cached future price for ${commodityType} ${monthCode} ${yearStr}`);
      }
    }

    // If we have all 6 futures from cache, return them
    if (futures.length === 6) {
      console.log(`Using all cached future prices for ${commodityType}`);
      return futures;
    }

    // If we're here, we need to fetch some or all futures from the API
    console.log(`Fetching futures data for ${commodityType} from API`);

    // Try to fetch futures data from the AMS API
    let apiData = [];
    try {
      const response = await amsClient.get('/commodities/futures', {
        params: {
          commodity: commodityType
        }
      });

      // If futures data is found, use it
      if (response.data && response.data.results && response.data.results.length > 0) {
        apiData = response.data.results.map(futureData => ({
          commodity: commodityType,
          price: parseFloat(futureData.price),
          unit: futureData.unit || getCommodityUnit(commodityType),
          month: futureData.month,
          year: futureData.year,
          exchange: futureData.exchange || 'CME',
          api_source: 'AMS'
        }));

        console.log(`Retrieved ${apiData.length} futures from API for ${commodityType}`);

        // Cache each future from the API
        for (const future of apiData) {
          await saveFuturePriceToCache(future);
        }

        // If we got some futures from the API but not all 6, we need to fill in the missing ones
        if (apiData.length > 0 && apiData.length < 6) {
          console.log(`Only got ${apiData.length} futures from API for ${commodityType}, filling in missing ones`);

          // Create a map of month/year combinations we already have
          const existingFutures = new Map();
          apiData.forEach(future => {
            existingFutures.set(`${future.month}_${future.year}`, true);
          });

          // Fill in missing futures based on the pattern of existing ones
          for (let i = 1; i <= 6; i++) {
            const futureMonth = (currentMonth + i) % 12;
            const futureYear = currentYear + Math.floor((currentMonth + i) / 12);
            const monthCode = months[futureMonth];
            const yearStr = futureYear.toString();

            // Check if we already have this future
            if (!existingFutures.has(`${monthCode}_${yearStr}`)) {
              // If not, create a derived future based on the base price and existing futures
              const basePrice = getBasePriceForCommodity(commodityType);
              let derivedPrice = basePrice;

              // If we have at least one future, use it to derive a more realistic price
              if (apiData.length > 0) {
                // Calculate average price change between months in existing futures
                let avgMonthlyChange = 0;
                if (apiData.length > 1) {
                  let totalChange = 0;
                  let changeCount = 0;

                  for (let j = 1; j < apiData.length; j++) {
                    const prevPrice = apiData[j-1].price;
                    const currPrice = apiData[j].price;
                    totalChange += (currPrice - prevPrice) / prevPrice;
                    changeCount++;
                  }

                  if (changeCount > 0) {
                    avgMonthlyChange = totalChange / changeCount;
                  }
                }

                // Use the most recent future price as a base
                const mostRecentFuture = apiData[apiData.length - 1];
                const monthDiff = (futureMonth - months.indexOf(mostRecentFuture.month) + 12) % 12;

                // Apply the average monthly change for each month difference
                derivedPrice = mostRecentFuture.price * Math.pow(1 + avgMonthlyChange, monthDiff);
              }

              // Create the derived future
              const derivedFuture = {
                commodity: commodityType,
                price: parseFloat(derivedPrice.toFixed(2)),
                unit: getCommodityUnit(commodityType),
                month: monthCode,
                year: yearStr,
                exchange: apiData.length > 0 ? apiData[0].exchange : 'CME',
                api_source: 'DERIVED',
                isDerived: true
              };

              console.log(`Created derived future for ${commodityType} ${monthCode} ${yearStr}: $${derivedFuture.price}`);

              // Add to our list of futures
              apiData.push(derivedFuture);

              // Don't cache derived futures
            }
          }

          // Sort futures by date
          apiData.sort((a, b) => {
            if (a.year !== b.year) {
              return parseInt(a.year) - parseInt(b.year);
            }
            return months.indexOf(a.month) - months.indexOf(b.month);
          });
        }

        return apiData;
      }
    } catch (apiError) {
      console.error(`API call failed for futures data for ${commodityType}:`, apiError.message);
      // Continue to fallback
    }

    // Try to fetch futures data from the ERS API
    try {
      // Check if we have a valid ERS API key and commodity code
      const ersCode = mapCommodityToERSCode(commodityType);
      if (ERS_API_KEY && ersCode) {
        console.log(`Fetching futures data for ${commodityType} from USDA ERS API`);

        // Get current date in YYYY-MM-DD format
        const today = new Date().toISOString().split('T')[0];

        // Fetch futures data from ERS API
        const ersResponse = await ersClient.get('/commodities/futures', {
          params: {
            commodity_code: ersCode,
            date: today,
            format: 'json'
          }
        });

        // Process the response if we have data
        if (ersResponse.data && ersResponse.data.data && ersResponse.data.data.length > 0) {
          const futuresData = ersResponse.data.data;

          // Map the futures data to our format
          const ersFutures = futuresData.map(future => {
            const futureDate = new Date(future.contract_date);
            const monthIndex = futureDate.getMonth();
            const year = futureDate.getFullYear().toString();
            const monthCode = months[monthIndex];

            return {
              commodity: commodityType,
              price: parseFloat(future.price),
              unit: future.unit || getCommodityUnit(commodityType),
              month: monthCode,
              year: year,
              exchange: future.exchange || 'CME',
              api_source: 'ERS'
            };
          });

          // Cache each future from the ERS API
          for (const future of ersFutures) {
            await saveFuturePriceToCache(future);
          }

          // If we got some futures but not all 6, we need to fill in the missing ones
          if (ersFutures.length > 0 && ersFutures.length < 6) {
            console.log(`Only got ${ersFutures.length} futures from ERS API for ${commodityType}, filling in missing ones`);

            // Create a map of month/year combinations we already have
            const existingFutures = new Map();
            ersFutures.forEach(future => {
              existingFutures.set(`${future.month}_${future.year}`, true);
            });

            // Fill in missing futures based on the pattern of existing ones
            const currentDate = new Date();
            const currentMonth = currentDate.getMonth();
            const currentYear = currentDate.getFullYear();

            for (let i = 1; i <= 6; i++) {
              const futureMonth = (currentMonth + i) % 12;
              const futureYear = currentYear + Math.floor((currentMonth + i) / 12);
              const monthCode = months[futureMonth];
              const yearStr = futureYear.toString();

              // Check if we already have this future
              if (!existingFutures.has(`${monthCode}_${yearStr}`)) {
                // If not, create a derived future based on existing futures
                const basePrice = getBasePriceForCommodity(commodityType);
                let derivedPrice = basePrice;

                // If we have at least one future, use it to derive a more realistic price
                if (ersFutures.length > 0) {
                  // Calculate average price change between months in existing futures
                  let avgMonthlyChange = 0;
                  if (ersFutures.length > 1) {
                    let totalChange = 0;
                    let changeCount = 0;

                    for (let j = 1; j < ersFutures.length; j++) {
                      const prevPrice = ersFutures[j-1].price;
                      const currPrice = ersFutures[j].price;
                      totalChange += (currPrice - prevPrice) / prevPrice;
                      changeCount++;
                    }

                    if (changeCount > 0) {
                      avgMonthlyChange = totalChange / changeCount;
                    }
                  }

                  // Use the most recent future price as a base
                  const mostRecentFuture = ersFutures[ersFutures.length - 1];
                  const monthDiff = (futureMonth - months.indexOf(mostRecentFuture.month) + 12) % 12;

                  // Apply the average monthly change for each month difference
                  derivedPrice = mostRecentFuture.price * Math.pow(1 + avgMonthlyChange, monthDiff);
                }

                // Create the derived future
                const derivedFuture = {
                  commodity: commodityType,
                  price: parseFloat(derivedPrice.toFixed(2)),
                  unit: getCommodityUnit(commodityType),
                  month: monthCode,
                  year: yearStr,
                  exchange: ersFutures.length > 0 ? ersFutures[0].exchange : 'CME',
                  api_source: 'ERS_DERIVED',
                  isDerived: true
                };

                console.log(`Created derived future for ${commodityType} ${monthCode} ${yearStr}: $${derivedFuture.price}`);

                // Add to our list of futures
                ersFutures.push(derivedFuture);

                // Don't cache derived futures
              }
            }

            // Sort futures by date
            ersFutures.sort((a, b) => {
              if (a.year !== b.year) {
                return parseInt(a.year) - parseInt(b.year);
              }
              return months.indexOf(a.month) - months.indexOf(b.month);
            });
          }

          return ersFutures;
        } else {
          console.log(`No futures data found from ERS API for ${commodityType}`);
        }
      } else {
        console.log(`Skipping ERS API futures call for ${commodityType}: ${!ERS_API_KEY ? 'No API key' : 'No commodity code mapping'}`);
      }
    } catch (ersError) {
      console.error(`Error fetching futures data for ${commodityType} from ERS API:`, ersError?.message);
      // Continue to fallback
    }

    // If we reach here, no futures data was found from any API or there was an error
    console.log(`No futures data found for ${commodityType} from any API, generating derived futures`);

    // Get the current price to use as a base for derived futures
    let currentPrice;
    try {
      // Try to get the current price from our getCurrentPrices function
      const priceData = await getCurrentPrices(commodityType, `NAT_${commodityType.toUpperCase()}`);
      currentPrice = priceData.price;
    } catch (priceError) {
      console.error(`Error getting current price for ${commodityType}:`, priceError.message);
      // Use base price if current price can't be fetched
      currentPrice = getBasePriceForCommodity(commodityType);
    }

    // Generate derived futures based on the current price
    const derivedFutures = [];

    for (let i = 1; i <= 6; i++) {
      const futureMonth = (currentMonth + i) % 12;
      const futureYear = currentYear + Math.floor((currentMonth + i) / 12);
      const monthCode = months[futureMonth];
      const yearStr = futureYear.toString();

      // Apply a small random adjustment for each month into the future
      // This creates a more realistic pattern than completely random prices
      // The further into the future, the more potential variance
      const adjustment = 1 + (Math.random() * 0.04 - 0.02) * i; // +/- 2% per month, increasing with time
      const derivedPrice = currentPrice * adjustment;

      derivedFutures.push({
        commodity: commodityType,
        price: parseFloat(derivedPrice.toFixed(2)),
        unit: getCommodityUnit(commodityType),
        month: monthCode,
        year: yearStr,
        exchange: 'CME',
        api_source: 'DERIVED',
        isDerived: true
      });
    }

    console.log(`Generated ${derivedFutures.length} derived futures for ${commodityType}`);
    return derivedFutures;
  } catch (error) {
    console.error(`Error getting future prices for ${commodityType}:`, error);

    // Instead of propagating the error, return derived futures
    console.log(`Returning derived futures for ${commodityType} due to error`);

    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();

    const basePrice = getBasePriceForCommodity(commodityType);
    const unit = getCommodityUnit(commodityType);

    const fallbackFutures = [];

    for (let i = 1; i <= 6; i++) {
      const futureMonth = (currentMonth + i) % 12;
      const futureYear = currentYear + Math.floor((currentMonth + i) / 12);
      const monthCode = months[futureMonth];
      const yearStr = futureYear.toString();

      // Create a simple pattern of slightly increasing prices
      const fallbackPrice = basePrice * (1 + i * 0.01); // 1% increase per month

      fallbackFutures.push({
        commodity: commodityType,
        price: parseFloat(fallbackPrice.toFixed(2)),
        unit: unit,
        month: monthCode,
        year: yearStr,
        exchange: 'CME',
        api_source: 'ERROR_FALLBACK',
        isFallback: true,
        error: error.message
      });
    }

    return fallbackFutures;
  }
};

// Helper function to get base price for a commodity
const getBasePriceForCommodity = (commodity) => {
  switch (commodity.toLowerCase()) {
    case 'beef':
      return 175.00; // $/cwt (hundredweight)
    case 'corn':
      return 5.75; // $/bushel
    case 'hay':
      return 200.00; // $/ton
    case 'wheat':
      return 7.25; // $/bushel
    case 'soybeans':
      return 13.50; // $/bushel
    case 'cotton':
      return 0.85; // $/lb
    case 'rice':
      return 18.50; // $/cwt
    case 'oats':
      return 4.25; // $/bushel
    case 'barley':
      return 5.50; // $/bushel
    default:
      return 10.00; // Default price
  }
};

// Helper function to get unit for a commodity
const getCommodityUnit = (commodity) => {
  switch (commodity.toLowerCase()) {
    case 'beef':
      return 'cwt'; // hundredweight (100 lbs)
    case 'corn':
    case 'wheat':
    case 'soybeans':
    case 'oats':
    case 'barley':
      return 'bushel';
    case 'hay':
      return 'ton';
    case 'cotton':
      return 'lb';
    case 'rice':
      return 'cwt';
    default:
      return 'unit';
  }
};

/**
 * @api {get} /api/market-prices Get market prices for commodities
 * @apiName GetMarketPrices
 * @apiGroup MarketPrices
 * @apiDescription Get current and future prices for agricultural commodities based on location
 *
 * @apiParam {Number} lat Latitude of the location
 * @apiParam {Number} lon Longitude of the location
 * @apiParam {String} [commodities] Comma-separated list of commodities (e.g., 'beef,corn,hay')
 *
 * @apiSuccess {Object[]} currentPrices List of current prices for each commodity
 * @apiSuccess {Object[]} futurePrices List of future prices for each commodity
 * @apiSuccess {String} lastUpdated Timestamp of when the data was last updated
 */
router.get('/', async (req, res) => {
  try {
    const { lat, lon, commodities } = req.query;

    // Validate required parameters
    if (!lat || !lon) {
      return res.status(400).json({ error: 'Missing required parameters: lat and lon' });
    }

    const commodityList = commodities ? commodities.split(',') : ['beef', 'corn', 'hay', 'wheat', 'soybeans'];
    console.log(`Fetching market prices for lat=${lat}, lon=${lon}, commodities=${commodityList.join(',')}`);

    // Process each commodity
    const currentPrices = [];
    const futurePrices = [];

    for (const commodity of commodityList) {
      try {
        // Get the closest market for this commodity
        const market = await getClosestMarket(lat, lon, commodity);
        console.log(`Found market for ${commodity}: ${market.name} (${market.marketId}), distance: ${market.distance} miles`);

        // Get current prices
        const currentPrice = await getCurrentPrices(commodity, market.marketId);
        currentPrices.push(currentPrice);
        console.log(`Current price for ${commodity}: $${currentPrice.price} per ${currentPrice.unit}`);

        // Get future prices
        const futures = await getFuturePrices(commodity);
        futurePrices.push(...futures);
        console.log(`Retrieved ${futures.length} future price entries for ${commodity}`);
      } catch (commodityError) {
        console.error(`Error processing commodity ${commodity}:`, commodityError);
        // Continue with other commodities even if one fails
      }
    }

    // If we couldn't get any price data, return an error
    if (currentPrices.length === 0) {
      return res.status(500).json({ 
        error: 'Failed to fetch market price data for any commodity',
        message: 'No price data available'
      });
    }

    res.json({
      currentPrices,
      futurePrices,
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Market price API error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch market price data',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

/**
 * @api {get} /api/market-prices/futures Get futures data for commodities
 * @apiName GetFuturesData
 * @apiGroup MarketPrices
 * @apiDescription Get futures contract prices for agricultural commodities
 *
 * @apiParam {Number} lat Latitude of the location
 * @apiParam {Number} lon Longitude of the location
 * @apiParam {String} [commodities] Comma-separated list of commodities (e.g., 'beef,corn,hay')
 *
 * @apiSuccess {Object[]} futures List of futures contracts for each commodity
 */
router.get('/futures', async (req, res) => {
  try {
    const { lat, lon, commodities } = req.query;

    // Validate required parameters
    if (!lat || !lon) {
      return res.status(400).json({ error: 'Missing required parameters: lat and lon' });
    }

    const commodityList = commodities ? commodities.split(',') : ['beef', 'corn', 'hay', 'wheat', 'soybeans'];
    console.log(`Fetching futures data for lat=${lat}, lon=${lon}, commodities=${commodityList.join(',')}`);

    // Process each commodity
    const allFutures = [];

    for (const commodity of commodityList) {
      try {
        const futures = await getFuturePrices(commodity);
        allFutures.push(...futures);
        console.log(`Retrieved ${futures.length} future price entries for ${commodity}`);
      } catch (commodityError) {
        console.error(`Error processing futures for commodity ${commodity}:`, commodityError);
        // Continue with other commodities even if one fails
      }
    }

    // If we couldn't get any futures data, return an error
    if (allFutures.length === 0) {
      return res.status(500).json({ 
        error: 'Failed to fetch futures data for any commodity',
        message: 'No futures data available'
      });
    }

    res.json(allFutures);
  } catch (error) {
    console.error('Futures API error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch futures data',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

/**
 * @api {get} /api/market-prices/historical Get historical price data
 * @apiName GetHistoricalPrices
 * @apiGroup MarketPrices
 * @apiDescription Get historical price data for a specific commodity over a date range
 *
 * @apiParam {String} commodity The commodity to fetch historical data for
 * @apiParam {String} startDate The start date for historical data (YYYY-MM-DD)
 * @apiParam {String} endDate The end date for historical data (YYYY-MM-DD)
 *
 * @apiSuccess {Object[]} historicalPrices List of historical prices for the commodity
 */
router.get('/historical', async (req, res) => {
  try {
    const { commodity, startDate, endDate } = req.query;

    // Validate required parameters
    if (!commodity || !startDate || !endDate) {
      return res.status(400).json({ 
        error: 'Missing required parameters: commodity, startDate, and endDate' 
      });
    }

    console.log(`Fetching historical data for commodity=${commodity}, startDate=${startDate}, endDate=${endDate}`);

    // Check if we have cached historical data for this date range
    try {
      const start = new Date(startDate);
      const end = new Date(endDate);
      const now = new Date();

      // Query the database for cached historical prices
      const cachedPrices = await HistoricalPrice.findAll({
        where: {
          commodity: commodity,
          date: {
            [Op.between]: [start, end]
          },
          is_cached: true,
          cache_expiry: {
            [Op.gt]: now
          }
        },
        order: [['date', 'ASC']]
      });

      // Calculate the number of days in the range
      const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));

      // If we have all days in the cache, return them
      if (cachedPrices.length === daysDiff + 1) {
        console.log(`Using ${cachedPrices.length} cached historical prices for ${commodity} from ${startDate} to ${endDate}`);

        const historicalPrices = cachedPrices.map(price => ({
          commodity: price.commodity,
          price: parseFloat(price.price),
          unit: price.unit,
          date: price.date,
          location: price.location,
          trend: price.trend,
          percentChange: parseFloat(price.percent_change),
          source: price.source,
          api_source: price.api_source
        }));

        return res.json(historicalPrices);
      }

      console.log(`Found ${cachedPrices.length} cached prices out of ${daysDiff + 1} days needed. Fetching missing data.`);

      // Create a map of dates we already have in the cache
      const cachedDates = new Map();
      cachedPrices.forEach(price => {
        cachedDates.set(price.date.toISOString().split('T')[0], price);
      });

      // Try to fetch historical price data from the AMS API
      let apiPrices = [];
      let usingMockData = false;

      try {
        const response = await amsClient.get('/commodities/history', {
          params: {
            commodity: commodity,
            startDate: startDate,
            endDate: endDate
          }
        });

        // If historical price data is found, use it
        if (response.data && response.data.results && response.data.results.length > 0) {
          apiPrices = response.data.results.map(priceData => {
            // Calculate percent change if previous price is available
            let percentChange = 0;
            let trend = 'stable';

            if (priceData.previous_price && priceData.price) {
              percentChange = ((priceData.price - priceData.previous_price) / priceData.previous_price) * 100;
              trend = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable';
            } else {
              // If no previous price, use a random percent change
              percentChange = parseFloat((Math.random() * 2 - 1).toFixed(2));
              trend = percentChange > 0 ? 'up' : 'down';
            }

            return {
              commodity: commodity,
              price: parseFloat(priceData.price) || getBasePriceForCommodity(commodity),
              unit: priceData.unit || getCommodityUnit(commodity),
              date: priceData.date || new Date().toISOString().split('T')[0],
              location: priceData.location || 'National Average',
              trend: trend,
              percentChange: parseFloat(percentChange.toFixed(2)),
              api_source: 'AMS'
            };
          });

          console.log(`Retrieved ${apiPrices.length} historical price entries from API for ${commodity}`);
        } else {
          // If no historical price data is found from AMS API, try ERS API
          console.log(`No historical price data found from AMS API for ${commodity} from ${startDate} to ${endDate}. Trying ERS API.`);

          // Try to fetch from USDA Economic Research Service (ERS) API
          try {
            // Check if we have a valid ERS API key and commodity code
            const ersCode = mapCommodityToERSCode(commodity);
            if (ERS_API_KEY && ersCode) {
              console.log(`Fetching historical data for ${commodity} from USDA ERS API`);

              // Fetch historical price data from ERS API
              const ersResponse = await ersClient.get('/commodities/prices/historical', {
                params: {
                  commodity_code: ersCode,
                  start_date: startDate,
                  end_date: endDate,
                  format: 'json'
                }
              });

              // Process the response if we have data
              if (ersResponse.data && ersResponse.data.data && ersResponse.data.data.length > 0) {
                const ersData = ersResponse.data.data;

                // Map the historical data to our format
                apiPrices = ersData.map(priceData => {
                  // Calculate percent change if we have previous data
                  let percentChange = 0;
                  let trend = 'stable';

                  return {
                    commodity: commodity,
                    price: parseFloat(priceData.price),
                    unit: priceData.unit || getCommodityUnit(commodity),
                    date: priceData.date,
                    location: priceData.location || 'National Average',
                    trend: trend,
                    percentChange: 0, // Can't calculate without previous data
                    api_source: 'ERS'
                  };
                });

                console.log(`Retrieved ${apiPrices.length} historical price entries from ERS API for ${commodity}`);
              } else {
                console.log(`No historical price data found from ERS API for ${commodity}`);
              }
            } else {
              console.log(`Skipping ERS API call for ${commodity}: ${!ERS_API_KEY ? 'No API key' : 'No commodity code mapping'}`);
            }
          } catch (ersError) {
            console.error(`Error fetching historical data for ${commodity} from ERS API:`, ersError?.message);
          }

          // If we still don't have data, try NASS API
          if (!apiPrices || apiPrices.length === 0) {
            console.log(`No historical price data found from ERS API for ${commodity}. Trying NASS API.`);

            try {
              // Check if we have a valid NASS API key and commodity code
              const nassCode = mapCommodityToNASSCode(commodity);
              if (NASS_API_KEY && nassCode) {
                console.log(`Fetching historical data for ${commodity} from USDA NASS API`);

                // Parse start and end dates to get years
                const startYear = new Date(startDate).getFullYear();
                const endYear = new Date(endDate).getFullYear();
                const years = [];

                // Build array of years to query
                for (let year = startYear; year <= endYear; year++) {
                  years.push(year.toString());
                }

                // Fetch historical price data from NASS API
                const nassResponse = await nassClient.get('/quickstats', {
                  params: {
                    commodity_desc: nassCode,
                    statisticcat_desc: 'PRICE RECEIVED',
                    year: years.join(','),
                    format: 'JSON'
                  }
                });

                // Process the response if we have data
                if (nassResponse.data && nassResponse.data.data && nassResponse.data.data.length > 0) {
                  const nassData = nassResponse.data.data;

                  // Map the historical data to our format
                  apiPrices = nassData.map(priceData => {
                    return {
                      commodity: commodity,
                      price: parseFloat(priceData.Value),
                      unit: priceData.unit_desc || getCommodityUnit(commodity),
                      date: `${priceData.year}-${priceData.reference_period_desc.padStart(2, '0')}-01`,
                      location: priceData.geo_level_desc === 'NATIONAL' ? 'National Average' : priceData.state_name || 'Regional Market',
                      trend: 'unknown', // Can't determine trend without more context
                      percentChange: 0, // Can't calculate without previous data
                      api_source: 'NASS'
                    };
                  });

                  console.log(`Retrieved ${apiPrices.length} historical price entries from NASS API for ${commodity}`);
                } else {
                  console.log(`No historical price data found from NASS API for ${commodity}`);
                }
              } else {
                console.log(`Skipping NASS API call for ${commodity}: ${!NASS_API_KEY ? 'No API key' : 'No commodity code mapping'}`);
              }
            } catch (nassError) {
              console.error(`Error fetching historical data for ${commodity} from NASS API:`, nassError?.message);
            }
          }

          // If we still don't have any data from any API, generate synthetic historical data
          if (!apiPrices || apiPrices.length === 0) {
            console.log(`No historical price data found from any API for ${commodity}. Generating synthetic data.`);

            // Get base price for this commodity
            const basePrice = getBasePriceForCommodity(commodity);
            const unit = getCommodityUnit(commodity);

            // Generate synthetic data for each day in the range
            apiPrices = [];
            const start = new Date(startDate);
            const end = new Date(endDate);
            const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 3600 * 24));

            // Use a random walk algorithm to generate realistic price movements
            let currentPrice = basePrice;
            let previousPrice = basePrice;

            for (let i = 0; i <= daysDiff; i++) {
              const currentDate = new Date(start);
              currentDate.setDate(currentDate.getDate() + i);
              const dateStr = currentDate.toISOString().split('T')[0];

              // Random walk with mean reversion
              const volatility = 0.01; // 1% daily volatility
              const meanReversionStrength = 0.05; // 5% mean reversion strength
              const randomChange = (Math.random() - 0.5) * 2 * volatility;
              const meanReversion = (basePrice - currentPrice) / basePrice * meanReversionStrength;
              const priceChange = randomChange + meanReversion;

              // Update price
              previousPrice = currentPrice;
              currentPrice = currentPrice * (1 + priceChange);

              // Calculate percent change
              const percentChange = ((currentPrice - previousPrice) / previousPrice) * 100;
              const trend = percentChange > 0 ? 'up' : percentChange < 0 ? 'down' : 'stable';

              apiPrices.push({
                commodity: commodity,
                price: parseFloat(currentPrice.toFixed(2)),
                unit: unit,
                date: dateStr,
                location: 'National Average',
                trend: trend,
                percentChange: parseFloat(percentChange.toFixed(2)),
                api_source: 'SYNTHETIC',
                isSynthetic: true
              });
            }

            console.log(`Generated ${apiPrices.length} synthetic historical price entries for ${commodity}`);
          }
        }
      } catch (apiError) {
        console.error(`API call failed for historical price data for ${commodity} from ${startDate} to ${endDate}:`, apiError);
        throw apiError;
      }

      // Combine cached prices with API prices (no mock data for missing dates)
      const allPrices = [];

      for (let i = 0; i <= daysDiff; i++) {
        const currentDate = new Date(start);
        currentDate.setDate(currentDate.getDate() + i);
        const dateStr = currentDate.toISOString().split('T')[0];

        // Check if we have this date in the cache
        if (cachedDates.has(dateStr)) {
          const cachedPrice = cachedDates.get(dateStr);
          allPrices.push({
            commodity: cachedPrice.commodity,
            price: parseFloat(cachedPrice.price),
            unit: cachedPrice.unit,
            date: cachedPrice.date,
            location: cachedPrice.location,
            trend: cachedPrice.trend,
            percentChange: parseFloat(cachedPrice.percent_change),
            source: cachedPrice.source,
            api_source: cachedPrice.api_source
          });
          continue;
        }

        // Check if we have this date from the API
        const apiPrice = apiPrices.find(p => p.date === dateStr);
        if (apiPrice) {
          allPrices.push(apiPrice);

          // Cache this price
          await saveHistoricalPriceToCache(apiPrice);
          continue;
        }

        // If we don't have this date in cache or from API, skip it
        // No mock data will be generated
      }

      console.log(`Returning ${allPrices.length} historical prices for ${commodity} from ${startDate} to ${endDate}`);
      return res.json(allPrices);

    } catch (dbError) {
      console.error(`Database error when fetching historical prices:`, dbError);

      // Return error if there's a database error
      console.error(`Database error when fetching historical prices for ${commodity}`);
      return res.status(500).json({ 
        error: 'Database error', 
        message: 'Could not retrieve historical price data due to a database error'
      });
    }
  } catch (error) {
    console.error('Historical price API error:', error.message);
    res.status(500).json({ 
      error: 'Failed to fetch historical price data',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

/**
 * @api {post} /api/market-prices/alerts Set price alert
 * @apiName SetPriceAlert
 * @apiGroup MarketPrices
 * @apiDescription Set up an alert for when a commodity price reaches a target value
 *
 * @apiParam {String} commodity The commodity to set an alert for
 * @apiParam {Number} targetPrice The target price to trigger the alert
 * @apiParam {String} condition The condition for the alert ('above' or 'below')
 * @apiParam {String} [email] Email address to send the alert to (optional)
 * @apiParam {String} [phone] Phone number to send the alert to (optional)
 * @apiParam {String} [userId] User ID to associate the alert with (optional)
 * @apiParam {String} [farmId] Farm ID to associate the alert with (optional)
 *
 * @apiSuccess {Boolean} success Indicates if the alert was successfully set
 * @apiSuccess {String} message Success message
 * @apiSuccess {String} alertId Unique identifier for the alert
 */
router.post('/alerts', async (req, res) => {
  try {
    const { commodity, targetPrice, condition, email, phone, userId, farmId } = req.body;

    // Validate required parameters
    if (!commodity || !targetPrice || !condition) {
      return res.status(400).json({ 
        error: 'Missing required parameters: commodity, targetPrice, and condition' 
      });
    }

    if (condition !== 'above' && condition !== 'below') {
      return res.status(400).json({ 
        error: 'Invalid condition. Must be "above" or "below"' 
      });
    }

    // Validate that at least one notification method is provided
    if (!email && !phone) {
      console.log('No notification method provided, alert will only be visible in the app');
    }

    console.log(`Setting price alert for commodity=${commodity}, targetPrice=${targetPrice}, condition=${condition}`);

    // Get current price for comparison
    try {
      // Get the current price to check if the alert is already triggered
      let currentPrice;
      try {
        const marketId = `NAT_${commodity.toUpperCase()}`;
        const priceData = await getCurrentPrices(commodity, marketId);
        currentPrice = priceData.price;
        console.log(`Current price for ${commodity}: $${currentPrice} per ${priceData.unit}`);
      } catch (priceError) {
        console.error(`Error getting current price for ${commodity}:`, priceError.message);
        // Continue without current price
        currentPrice = null;
      }

      // Check if the alert is already triggered
      let isAlreadyTriggered = false;
      if (currentPrice !== null) {
        if (condition === 'above' && currentPrice > targetPrice) {
          isAlreadyTriggered = true;
        } else if (condition === 'below' && currentPrice < targetPrice) {
          isAlreadyTriggered = true;
        }
      }

      // Store the alert in the database
      const now = new Date();
      const alert = await PriceAlert.create({
        commodity: commodity,
        target_price: targetPrice,
        condition: condition,
        email: email || null,
        phone: phone || null,
        user_id: userId || null,
        farm_id: farmId || null,
        is_active: true,
        is_triggered: isAlreadyTriggered,
        last_checked: now,
        created_at: now,
        updated_at: now
      });

      console.log(`Alert created: ID=${alert.id}, commodity=${commodity}, targetPrice=${targetPrice}, condition=${condition}, email=${email || 'none'}, phone=${phone || 'none'}`);

      // If the alert is already triggered, send notification immediately
      if (isAlreadyTriggered) {
        console.log(`Alert ${alert.id} is already triggered! Current price: $${currentPrice}, Target: $${targetPrice}, Condition: ${condition}`);

        // In a production environment, you would send an email or SMS here
        // For now, we'll just log it
        if (email) {
          console.log(`Would send email to ${email}: Alert for ${commodity} triggered! Current price: $${currentPrice} is ${condition} your target of $${targetPrice}`);
        }

        if (phone) {
          console.log(`Would send SMS to ${phone}: Alert for ${commodity} triggered! Current price: $${currentPrice} is ${condition} your target of $${targetPrice}`);
        }
      }

      res.json({
        success: true,
        message: `Price alert set for ${commodity} when price goes ${condition} $${targetPrice}`,
        alertId: alert.id,
        isAlreadyTriggered: isAlreadyTriggered
      });
    } catch (alertError) {
      console.error('Error setting price alert:', alertError);
      res.status(500).json({ 
        error: 'Failed to set price alert',
        message: alertError.message,
        details: process.env.NODE_ENV === 'development' ? alertError.toString() : undefined
      });
    }
  } catch (error) {
    console.error('Price alert API error:', error.message);
    res.status(500).json({ 
      error: 'Failed to set price alert',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

/**
 * @api {get} /api/market-prices/alerts Get price alerts
 * @apiName GetPriceAlerts
 * @apiGroup MarketPrices
 * @apiDescription Get all price alerts for a user or farm
 *
 * @apiParam {String} [userId] User ID to get alerts for (optional)
 * @apiParam {String} [farmId] Farm ID to get alerts for (optional)
 *
 * @apiSuccess {Object[]} alerts List of price alerts
 */
router.get('/alerts', async (req, res) => {
  try {
    const { userId, farmId } = req.query;

    // Build query conditions
    const whereConditions = { is_active: true };

    if (userId) {
      whereConditions.user_id = userId;
    }

    if (farmId) {
      whereConditions.farm_id = farmId;
    }

    // If neither userId nor farmId is provided, return an error
    if (!userId && !farmId) {
      return res.status(400).json({ 
        error: 'Missing required parameters: userId or farmId' 
      });
    }

    // Get alerts from the database
    const alerts = await PriceAlert.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']]
    });

    res.json(alerts);
  } catch (error) {
    console.error('Error getting price alerts:', error.message);
    res.status(500).json({ 
      error: 'Failed to get price alerts',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

/**
 * @api {delete} /api/market-prices/alerts/:alertId Delete price alert
 * @apiName DeletePriceAlert
 * @apiGroup MarketPrices
 * @apiDescription Delete a price alert
 *
 * @apiParam {String} alertId The ID of the alert to delete
 *
 * @apiSuccess {Boolean} success Indicates if the alert was successfully deleted
 * @apiSuccess {String} message Success message
 */
router.delete('/alerts/:alertId', async (req, res) => {
  try {
    const { alertId } = req.params;

    // Find the alert
    const alert = await PriceAlert.findByPk(alertId);

    if (!alert) {
      return res.status(404).json({ 
        error: 'Alert not found',
        message: `No alert found with ID ${alertId}`
      });
    }

    // Soft delete by setting is_active to false
    alert.is_active = false;
    alert.updated_at = new Date();
    await alert.save();

    res.json({
      success: true,
      message: `Price alert ${alertId} has been deleted`
    });
  } catch (error) {
    console.error('Error deleting price alert:', error.message);
    res.status(500).json({ 
      error: 'Failed to delete price alert',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

/**
 * @api {post} /api/market-prices/alerts/check Check price alerts
 * @apiName CheckPriceAlerts
 * @apiGroup MarketPrices
 * @apiDescription Check all active price alerts and trigger notifications if conditions are met
 *
 * @apiSuccess {Boolean} success Indicates if the check was successful
 * @apiSuccess {Number} triggeredCount Number of alerts that were triggered
 * @apiSuccess {Number} checkedCount Number of alerts that were checked
 */
router.post('/alerts/check', async (req, res) => {
  try {
    // Get all active alerts
    const activeAlerts = await PriceAlert.findAll({
      where: { is_active: true }
    });

    console.log(`Checking ${activeAlerts.length} active price alerts`);

    let triggeredCount = 0;
    const now = new Date();

    // Check each alert
    for (const alert of activeAlerts) {
      try {
        // Get current price for the commodity
        const marketId = `NAT_${alert.commodity.toUpperCase()}`;
        const priceData = await getCurrentPrices(alert.commodity, marketId);
        const currentPrice = priceData.price;

        console.log(`Checking alert ${alert.id}: ${alert.commodity} ${alert.condition} $${alert.target_price}, Current price: $${currentPrice}`);

        // Check if the alert condition is met
        let isTriggered = false;
        if (alert.condition === 'above' && currentPrice > alert.target_price) {
          isTriggered = true;
        } else if (alert.condition === 'below' && currentPrice < alert.target_price) {
          isTriggered = true;
        }

        // Update the alert
        alert.last_checked = now;

        // If the alert is triggered and wasn't previously triggered, send notification
        if (isTriggered && !alert.is_triggered) {
          console.log(`Alert ${alert.id} triggered! Current price: $${currentPrice}, Target: $${alert.target_price}, Condition: ${alert.condition}`);

          // In a production environment, you would send an email or SMS here
          // For now, we'll just log it
          if (alert.email) {
            console.log(`Would send email to ${alert.email}: Alert for ${alert.commodity} triggered! Current price: $${currentPrice} is ${alert.condition} your target of $${alert.target_price}`);
          }

          if (alert.phone) {
            console.log(`Would send SMS to ${alert.phone}: Alert for ${alert.commodity} triggered! Current price: $${currentPrice} is ${alert.condition} your target of $${alert.target_price}`);
          }

          alert.is_triggered = true;
          triggeredCount++;
        }

        await alert.save();
      } catch (alertError) {
        console.error(`Error checking alert ${alert.id}:`, alertError.message);
        // Continue with next alert
      }
    }

    res.json({
      success: true,
      triggeredCount: triggeredCount,
      checkedCount: activeAlerts.length
    });
  } catch (error) {
    console.error('Error checking price alerts:', error.message);
    res.status(500).json({ 
      error: 'Failed to check price alerts',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.toString() : undefined
    });
  }
});

export default router;
