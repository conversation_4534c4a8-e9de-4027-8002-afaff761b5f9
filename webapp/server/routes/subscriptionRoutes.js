import express from 'express';
import {
  getAllSubscriptionPlans,
  getSubscriptionPlanById,
  createSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  getFarmSubscriptionTransactions,
  createSubscriptionTransaction,
  createFarmTrialSubscription,
  updateFarmTrialPeriod,
  upgradeFarmPlan
} from '../controllers/subscriptionController.js';
import {
  getAllPromoCodes,
  getPromoCodeById,
  createPromoCode,
  updatePromoCode,
  deletePromoCode,
  validatePromoCode,
  applyPromoCode
} from '../controllers/promoCodeController.js';
import { authenticate, isAdmin, isGlobalAdmin } from '../middleware/index.js';
import { setFarmSchema, ensureFarmSchema } from '../middleware/farmMiddleware.js';

const router = express.Router();

// Get all subscription plans
router.get('/plans', authenticate, getAllSubscriptionPlans);

// Get a single subscription plan by ID
router.get('/plans/:planId', authenticate, getSubscriptionPlanById);

// Create a new subscription plan (global admin only)
router.post('/plans', authenticate, isGlobalAdmin, createSubscriptionPlan);

// Update a subscription plan (global admin only)
router.put('/plans/:planId', authenticate, isGlobalAdmin, updateSubscriptionPlan);

// Delete a subscription plan (global admin only)
router.delete('/plans/:planId', authenticate, isGlobalAdmin, deleteSubscriptionPlan);

// Get subscription transactions for a farm
router.get('/transactions/farm/:farmId', authenticate, setFarmSchema, ensureFarmSchema, getFarmSubscriptionTransactions);

// Create a subscription transaction
router.post('/transactions', authenticate, setFarmSchema, ensureFarmSchema, createSubscriptionTransaction);

// Create a free trial subscription for a farm (global admin only)
router.post('/farms/trial', authenticate, isGlobalAdmin, createFarmTrialSubscription);

// Update a farm's trial period (global admin only)
router.put('/farms/:farmId/trial', authenticate, isGlobalAdmin, updateFarmTrialPeriod);

// Upgrade a farm to a full access plan
router.put('/farms/:farmId/upgrade-plan', authenticate, upgradeFarmPlan);

// Removed legacy routes for business users (suppliers/vets) as they no longer require subscriptions

// Promo Code Routes
// Get all promo codes (global admin only)
router.get('/promo-codes', authenticate, isGlobalAdmin, getAllPromoCodes);

// Get a single promo code by ID (global admin only)
router.get('/promo-codes/:promoCodeId', authenticate, isGlobalAdmin, getPromoCodeById);

// Create a new promo code (global admin only)
router.post('/promo-codes', authenticate, isGlobalAdmin, createPromoCode);

// Update a promo code (global admin only)
router.put('/promo-codes/:promoCodeId', authenticate, isGlobalAdmin, updatePromoCode);

// Delete a promo code (global admin only)
router.delete('/promo-codes/:promoCodeId', authenticate, isGlobalAdmin, deletePromoCode);

// Validate a promo code (for users applying a code)
router.post('/promo-codes/validate', authenticate, validatePromoCode);

// Apply a promo code (increment usage counter)
router.post('/promo-codes/apply', authenticate, applyPromoCode);

export default router;
