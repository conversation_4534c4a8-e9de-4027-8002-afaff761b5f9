import express from 'express';
import notificationController from '../controllers/notificationController.js';
import { authenticate } from '../middleware/authMiddleware.js';

const router = express.Router();

/**
 * @api {get} /api/notifications/preferences Get notification preferences
 * @apiName GetNotificationPreferences
 * @apiGroup Notifications
 * @apiDescription Get notification preferences for the current user
 * 
 * @apiHeader {String} Authorization Bearer token
 * 
 * @apiSuccess {Boolean} enableInApp Whether in-app notifications are enabled
 * @apiSuccess {Boolean} enableEmail Whether email notifications are enabled
 * @apiSuccess {Boolean} chatMessageNotifications Whether chat message notifications are enabled
 * @apiSuccess {Boolean} taskNotifications Whether task notifications are enabled
 * @apiSuccess {Boolean} documentNotifications Whether document notifications are enabled
 * @apiSuccess {Boolean} systemNotifications Whether system notifications are enabled
 * 
 * @apiError (401) {String} Unauthorized Authentication failed
 * @apiError (500) {String} InternalServerError Failed to get notification preferences
 */
router.get('/preferences', authenticate, notificationController.getPreferences);

/**
 * @api {post} /api/notifications/preferences Update notification preferences
 * @apiName UpdateNotificationPreferences
 * @apiGroup Notifications
 * @apiDescription Update notification preferences for the current user
 * 
 * @apiHeader {String} Authorization Bearer token
 * 
 * @apiParam {Boolean} [enableInApp] Whether in-app notifications are enabled
 * @apiParam {Boolean} [enableEmail] Whether email notifications are enabled
 * @apiParam {Boolean} [chatMessageNotifications] Whether chat message notifications are enabled
 * @apiParam {Boolean} [taskNotifications] Whether task notifications are enabled
 * @apiParam {Boolean} [documentNotifications] Whether document notifications are enabled
 * @apiParam {Boolean} [systemNotifications] Whether system notifications are enabled
 * 
 * @apiSuccess {Boolean} enableInApp Whether in-app notifications are enabled
 * @apiSuccess {Boolean} enableEmail Whether email notifications are enabled
 * @apiSuccess {Boolean} chatMessageNotifications Whether chat message notifications are enabled
 * @apiSuccess {Boolean} taskNotifications Whether task notifications are enabled
 * @apiSuccess {Boolean} documentNotifications Whether document notifications are enabled
 * @apiSuccess {Boolean} systemNotifications Whether system notifications are enabled
 * 
 * @apiError (401) {String} Unauthorized Authentication failed
 * @apiError (500) {String} InternalServerError Failed to update notification preferences
 */
router.post('/preferences', authenticate, notificationController.updatePreferences);

export default router;