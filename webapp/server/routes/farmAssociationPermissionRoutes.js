import express from 'express';
import { 
  getPermissionsByAssociation, 
  getPendingPermissionsByFarm, 
  updatePermissions, 
  updatePermissionStatus,
  getPermissionsByFarm
} from '../controllers/farmAssociationPermissionController.js';
import { authenticateToken } from '../middleware/authMiddleware.js';
import { checkFarmAccess } from '../middleware/farmAccessMiddleware.js';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticateToken);

// Get all permissions for a farm association
router.get('/association/:associationId', getPermissionsByAssociation);

// Get all pending permissions for a farm
router.get('/farm/:farmId/pending', checkFarmAccess, getPendingPermissionsByFarm);

// Get all permissions for a farm (across all associations)
router.get('/farm/:farmId', checkFarmAccess, getPermissionsByFarm);

// Create or update permissions for a farm association
router.post('/association/:associationId', updatePermissions);

// Update a permission status (accept or reject)
router.put('/:permissionId/status', updatePermissionStatus);

export default router;