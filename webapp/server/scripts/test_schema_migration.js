import { sequelize } from '../config/database.js';

// Test script to verify schema migration
const testSchemaMigration = async () => {
  try {
    console.log('Testing schema migration...');
    
    // Get the current schema
    const currentSchema = process.env.DB_SCHEMA || 'site';
    console.log(`Current schema from environment: ${currentSchema}`);
    
    // Check if the site schema exists
    const schemaCheck = await sequelize.query(
      `SELECT EXISTS(SELECT 1 FROM pg_namespace WHERE nspname = 'site');`,
      { type: sequelize.QueryTypes.SELECT }
    );
    
    const siteSchemaExists = schemaCheck[0].exists;
    console.log(`Site schema exists: ${siteSchemaExists}`);
    
    if (!siteSchemaExists) {
      console.error('Site schema does not exist. Migration may not have been run.');
      return;
    }
    
    // Check if tables exist in the site schema
    try {
      // Test a few key tables
      const tables = ['users', 'farms', 'transactions', 'crops'];
      
      for (const table of tables) {
        try {
          const result = await sequelize.query(
            `SELECT COUNT(*) FROM site.${table}`,
            { type: sequelize.QueryTypes.SELECT }
          );
          console.log(`Table site.${table} exists and has ${result[0].count} rows.`);
        } catch (error) {
          console.error(`Error checking table site.${table}: ${error.message}`);
        }
      }
      
      // Check if any tables still exist in the public schema
      const publicTables = await sequelize.query(
        `SELECT tablename FROM pg_tables WHERE schemaname = 'public' AND tablename NOT IN ('pg_stat_statements')`,
        { type: sequelize.QueryTypes.SELECT }
      );
      
      if (publicTables.length > 0) {
        console.warn('The following tables still exist in the public schema:');
        publicTables.forEach(table => console.warn(`- ${table.tablename}`));
        console.warn('Consider dropping these tables or re-running the migration script.');
      } else {
        console.log('No application tables found in the public schema. Migration successful!');
      }
      
    } catch (error) {
      console.error('Error checking tables:', error);
    }
    
    console.log('Schema migration test completed.');
  } catch (error) {
    console.error('Error testing schema migration:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
  }
};

// Run the test
testSchemaMigration();