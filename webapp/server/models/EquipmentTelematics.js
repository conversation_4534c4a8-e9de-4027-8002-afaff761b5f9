import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Equipment from './Equipment.js';
import dotenv from 'dotenv';

dotenv.config();

const EquipmentTelematics = defineModel('EquipmentTelematics', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Equipment,
      key: 'id'
    }
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  // Location data
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  altitude: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  // Operational data
  engine_hours: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  odometer: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: true
  },
  fuel_level: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  fuel_consumption_rate: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  engine_rpm: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  engine_load: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  engine_temperature: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true
  },
  // Diagnostic data
  diagnostic_codes: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  // Status data
  operational_status: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  // Raw telematics data for future processing
  raw_data: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'equipment_telematics',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Associations are defined in associations.js

export default EquipmentTelematics;
