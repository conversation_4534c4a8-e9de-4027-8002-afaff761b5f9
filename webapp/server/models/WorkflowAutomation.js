import { DataTypes } from 'sequelize';
import {sequelize} from '../config/database.js';
import Farm from './Farm.js';
import { defineModel } from '../utils/modelUtils.js';

const WorkflowAutomation = defineModel('WorkflowAutomation', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true,
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id',
    },
    comment: 'The farm this workflow belongs to',
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Name of the workflow automation',
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of what this workflow does',
  },
  trigger_type: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Type of trigger (task_status_change, task_creation, scheduled, iot_event, etc.)',
  },
  trigger_config: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Configuration for the trigger (e.g., specific status, schedule details, IoT thresholds)',
  },
  action_type: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Type of action to perform (create_task, update_task, send_notification, etc.)',
  },
  action_config: {
    type: DataTypes.JSONB,
    allowNull: false,
    comment: 'Configuration for the action (e.g., task template, notification details)',
  },
  conditions: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Optional conditions that must be met for the workflow to execute',
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether this workflow automation is currently active',
  },
  last_executed: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'When this workflow was last executed',
  },
  execution_count: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0,
    comment: 'Number of times this workflow has been executed',
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
  },
}, {
  tableName: 'workflow_automations',
  timestamps: true,
  underscored: true,
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default WorkflowAutomation;
