import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';

const AmbrookLoan = defineModel('AmbrookLoan', {
  id: {
    type: DataTypes.STRING,
    primaryKey: true,
    allowNull: false
  },
  name: {
    type: DataTypes.STRING,
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  provider: {
    type: DataTypes.STRING,
    allowNull: true
  },
  interestRate: {
    type: DataTypes.STRING(50),
    field: 'interest_rate',
    allowNull: true
  },
  term: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  eligibility: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.STRING(50),
    allowNull: true
  },
  category: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  url: {
    type: DataTypes.TEXT,
    allowNull: true
  }
}, {
  tableName: 'ambrook_loans',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

export default AmbrookLoan;