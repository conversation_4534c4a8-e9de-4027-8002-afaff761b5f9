import { DataTypes } from 'sequelize';
import { defineModel } from '../utils/modelUtils.js';
import Invoice from './Invoice.js';
import User from './User.js';
import Farm from './Farm.js';

const InvoiceAuditLog = defineModel('InvoiceAuditLog', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  invoice_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Invoice,
      key: 'id'
    },
    comment: 'The invoice this audit log entry relates to'
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: User,
      key: 'id'
    },
    comment: 'User who performed the action (null for system actions)'
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'Farm context for the action'
  },
  action: {
    type: DataTypes.ENUM(
      'created',
      'viewed',
      'updated',
      'deleted',
      'sent',
      'paid',
      'cancelled',
      'disputed',
      'document_uploaded',
      'document_deleted',
      'reminder_sent',
      'status_changed',
      'access_denied',
      'permission_checked'
    ),
    allowNull: false,
    comment: 'Type of action performed'
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Human-readable description of the action'
  },
  changes: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'JSON object containing before/after values for updates'
  },
  metadata: {
    type: DataTypes.JSON,
    allowNull: true,
    comment: 'Additional metadata about the action'
  },
  ip_address: {
    type: DataTypes.STRING(45),
    allowNull: true,
    comment: 'IP address of the user who performed the action'
  },
  user_agent: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'User agent string of the client'
  },
  session_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Session ID for tracking user sessions'
  },
  result: {
    type: DataTypes.ENUM('success', 'failure', 'denied'),
    allowNull: false,
    defaultValue: 'success',
    comment: 'Result of the action'
  },
  error_message: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Error message if action failed'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'invoice_audit_logs',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false, // Audit logs should not be updated
  indexes: [
    {
      name: 'invoice_audit_logs_invoice_id_idx',
      fields: ['invoice_id']
    },
    {
      name: 'invoice_audit_logs_user_id_idx',
      fields: ['user_id']
    },
    {
      name: 'invoice_audit_logs_farm_id_idx',
      fields: ['farm_id']
    },
    {
      name: 'invoice_audit_logs_action_idx',
      fields: ['action']
    },
    {
      name: 'invoice_audit_logs_result_idx',
      fields: ['result']
    },
    {
      name: 'invoice_audit_logs_created_at_idx',
      fields: ['created_at']
    }
  ]
});

export default InvoiceAuditLog;
