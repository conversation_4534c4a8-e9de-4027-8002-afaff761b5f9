import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';

const PasswordGroupPermission = defineModel('PasswordGroupPermission', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  group_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'password_groups',
      key: 'id'
    }
  },
  role_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'roles',
      key: 'id'
    }
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    }
  },
  permission_type: {
    type: DataTypes.STRING(20),
    allowNull: false,
    validate: {
      isIn: [['view', 'edit', 'manage']]
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'password_group_permissions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  validate: {
    roleOrUserNotNull() {
      if ((this.role_id === null && this.user_id === null) || 
          (this.role_id !== null && this.user_id !== null)) {
        throw new Error('Either role_id or user_id must be set, but not both');
      }
    }
  }
});

// Instance methods
PasswordGroupPermission.prototype.toJSON = function() {
  const values = { ...this.get() };
  return values;
};

// Static methods
PasswordGroupPermission.findByGroup = async function(groupId) {
  return await this.findAll({
    where: { group_id: groupId }
  });
};

PasswordGroupPermission.findByRole = async function(roleId) {
  return await this.findAll({
    where: { role_id: roleId }
  });
};

PasswordGroupPermission.findByUser = async function(userId) {
  return await this.findAll({
    where: { user_id: userId }
  });
};

PasswordGroupPermission.findPermission = async function(groupId, roleId, userId) {
  const where = { group_id: groupId };
  
  if (roleId) {
    where.role_id = roleId;
  } else if (userId) {
    where.user_id = userId;
  }
  
  return await this.findOne({ where });
};

export default PasswordGroupPermission;