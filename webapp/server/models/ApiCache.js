import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import ApiEndpoint from './ApiEndpoint.js';
import dotenv from 'dotenv';

dotenv.config();

const ApiCache = defineModel('ApiCache', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  endpoint_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: ApiEndpoint,
      key: 'id'
    }
  },
  cache_key: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  request_params: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  response_body: {
    type: DataTypes.JSONB,
    allowNull: false
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  expires_at: {
    type: DataTypes.DATE,
    allowNull: false
  },
  last_accessed_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'api_cache',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false // No updated_at column for this table
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default ApiCache;