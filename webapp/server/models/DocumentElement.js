import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import SignableDocument from './SignableDocument.js';
import DocumentField from './DocumentField.js';

const DocumentElement = defineModel('DocumentElement', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  document_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: SignableDocument,
      key: 'id'
    }
  },
  element_type: {
    type: DataTypes.STRING(50),
    allowNull: false
  },
  content: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  image_path: {
    type: DataTypes.STRING(1024),
    allowNull: true
  },
  field_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: DocumentField,
      key: 'id'
    }
  },
  page_number: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 1
  },
  x_position: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  y_position: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  width: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  height: {
    type: DataTypes.FLOAT,
    allowNull: false
  },
  z_index: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  font_family: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  font_size: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  font_color: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  background_color: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  border_style: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  border_width: {
    type: DataTypes.INTEGER,
    allowNull: true
  },
  border_color: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'document_elements',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'document_elements_document_id_idx',
      fields: ['document_id']
    },
    {
      name: 'document_elements_element_type_idx',
      fields: ['element_type']
    }
  ]
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default DocumentElement;