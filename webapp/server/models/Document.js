import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import User from './User.js';
import Farm from './Farm.js';
import Equipment from './Equipment.js';
import Livestock from './Livestock.js';
import ChemicalProduct from './ChemicalProduct.js';
import SeedProduct from './SeedProduct.js';

dotenv.config();

const Document = defineModel('Document', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  file_path: {
    type: DataTypes.STRING(1024),
    allowNull: false
  },
  file_size: {
    type: DataTypes.BIGINT,
    allowNull: false
  },
  file_type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  mime_type: {
    type: DataTypes.STRING(100),
    allowNull: false
  },
  is_external: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  external_source: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      isIn: [['', null, 'google_drive', 'dropbox']]
    }
  },
  external_id: {
    type: DataTypes.STRING(255),
    allowNull: true
  },
  folder_id: {
    type: DataTypes.UUID,
    allowNull: true
  },
  // tenant_id field removed as part of migration from tenant to farm
  uploaded_by: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: User,
      key: 'id'
    }
  },
  // References to farm
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    }
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Equipment,
      key: 'id'
    }
  },
  livestock_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: Livestock,
      key: 'id'
    }
  },
  chemical_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: ChemicalProduct,
      key: 'id'
    }
  },
  seed_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: SeedProduct,
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'documents',

  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  indexes: [
    {
      name: 'documents_name_idx',
      fields: ['name']
    },
    // tenant_id index removed as part of migration from tenant to farm
    {
      name: 'documents_folder_id_idx',
      fields: ['folder_id']
    },
    {
      name: 'documents_farm_id_idx',
      fields: ['farm_id']
    },
    {
      name: 'documents_equipment_id_idx',
      fields: ['equipment_id']
    },
    {
      name: 'documents_livestock_id_idx',
      fields: ['livestock_id']
    },
    {
      name: 'documents_chemical_id_idx',
      fields: ['chemical_id']
    },
    {
      name: 'documents_seed_id_idx',
      fields: ['seed_id']
    }
  ]
});

// Associations are defined in associations.js

export default Document;
