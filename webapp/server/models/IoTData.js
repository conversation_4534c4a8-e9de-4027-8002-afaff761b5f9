import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import IoTDevice from './IoTDevice.js';
import dotenv from 'dotenv';

dotenv.config();

const IoTData = defineModel('IoTData', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  device_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: IoTDevice,
      key: 'id'
    }
  },
  timestamp: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  // Location data
  latitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  longitude: {
    type: DataTypes.DECIMAL(10, 7),
    allowNull: true
  },
  altitude: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true
  },
  // Sensor data
  temperature: {
    type: DataTypes.DECIMAL(6, 2),
    allowNull: true,
    comment: 'Temperature in degrees Celsius'
  },
  humidity: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Relative humidity in percentage'
  },
  pressure: {
    type: DataTypes.DECIMAL(8, 2),
    allowNull: true,
    comment: 'Atmospheric pressure in hPa'
  },
  soil_moisture: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    comment: 'Soil moisture in percentage'
  },
  soil_temperature: {
    type: DataTypes.DECIMAL(6, 2),
    allowNull: true,
    comment: 'Soil temperature in degrees Celsius'
  },
  light_level: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Light level in lux'
  },
  battery_level: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Battery level in percentage (0-100)'
  },
  signal_strength: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Signal strength in percentage (0-100)'
  },
  // Status data
  status: {
    type: DataTypes.STRING(50),
    allowNull: true,
    comment: 'Status of the device at the time of data collection'
  },
  // Raw data for future processing
  raw_data: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Raw data from the device in JSON format'
  },
  // Additional custom fields
  custom_fields: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional custom fields specific to the device type'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'iot_data',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default IoTData;