import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';

dotenv.config();

const FuelRecord = defineModel('FuelRecord', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: 'farms',
      key: 'id'
    }
  },
  date: {
    type: DataTypes.DATE,
    allowNull: false
  },
  amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Amount of fuel in liters/gallons'
  },
  cost: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Total cost of fuel'
  },
  odometer: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'Odometer reading at time of fueling'
  },
  fuel_type: {
    type: DataTypes.STRING,
    defaultValue: 'diesel',
    allowNull: false
  },
  location: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Location where fuel was purchased'
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  driver_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'drivers',
      key: 'id'
    }
  },
  equipment_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'equipment',
      key: 'id'
    }
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'fuel_records',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default FuelRecord;