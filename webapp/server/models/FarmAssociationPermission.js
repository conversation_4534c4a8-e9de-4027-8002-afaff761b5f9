import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import Farm from './Farm.js';
import FarmAssociation from './FarmAssociation.js';

const FarmAssociationPermission = defineModel('FarmAssociationPermission', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  farm_association_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: FarmAssociation,
      key: 'id'
    }
  },
  permission_type: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Type of permission (invoices, chat, customers, products, equipment, tasks, etc.)'
  },
  status: {
    type: DataTypes.STRING(20),
    allowNull: false,
    defaultValue: 'pending',
    validate: {
      isIn: [['pending', 'active', 'rejected']]
    },
    comment: 'Status of the permission: pending (waiting for approval), active (approved), or rejected'
  },
  initiator_farm_id: {
    type: DataTypes.UUID,
    allowNull: false,
    references: {
      model: Farm,
      key: 'id'
    },
    comment: 'The farm that initiated the permission change'
  },
  last_updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW,
    comment: 'When the permission was last updated'
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'farm_association_permissions',
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at'
});

// Define associations
FarmAssociationPermission.belongsTo(FarmAssociation, {
  foreignKey: 'farm_association_id',
  as: 'farmAssociation'
});

FarmAssociationPermission.belongsTo(Farm, {
  foreignKey: 'initiator_farm_id',
  as: 'initiatorFarm'
});

// Static methods
FarmAssociationPermission.getPermissionsByAssociation = async function(farmAssociationId) {
  try {
    return await FarmAssociationPermission.findAll({
      where: { farm_association_id: farmAssociationId },
      include: [
        {
          model: Farm,
          as: 'initiatorFarm',
          attributes: ['id', 'name']
        }
      ]
    });
  } catch (error) {
    console.error('Error getting permissions by association:', error);
    throw error;
  }
};

FarmAssociationPermission.getPendingPermissionsByFarm = async function(farmId) {
  try {
    const query = `
      SELECT fap.*, fa.initiator_farm_id, fa.associated_farm_id, 
             i_farm.name as initiator_farm_name, a_farm.name as associated_farm_name
      FROM farm_association_permissions fap
      JOIN farm_associations fa ON fap.farm_association_id = fa.id
      JOIN farms i_farm ON fa.initiator_farm_id = i_farm.id
      JOIN farms a_farm ON fa.associated_farm_id = a_farm.id
      WHERE fap.status = 'pending'
      AND fa.status = 'active'
      AND (fa.initiator_farm_id = :farmId OR fa.associated_farm_id = :farmId)
      AND fap.initiator_farm_id != :farmId
    `;

    const [results] = await sequelize.query(query, {
      replacements: { farmId },
      type: sequelize.QueryTypes.SELECT
    });

    return results;
  } catch (error) {
    console.error('Error getting pending permissions by farm:', error);
    throw error;
  }
};

export default FarmAssociationPermission;