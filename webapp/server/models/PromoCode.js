import { DataTypes } from 'sequelize';
import { sequelize } from '../config/database.js';
import { defineModel } from '../utils/modelUtils.js';
import dotenv from 'dotenv';
import SubscriptionPlan from './SubscriptionPlan.js';
import User from './User.js';
import Farm from './Farm.js';

dotenv.config();

const PromoCode = defineModel('PromoCode', {
  id: {
    type: DataTypes.UUID,
    defaultValue: DataTypes.UUIDV4,
    primaryKey: true
  },
  code: {
    type: DataTypes.STRING(50),
    allowNull: false,
    unique: true,
    validate: {
      notEmpty: true
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  subscription_plan_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'subscription_plans',
      key: 'id'
    },
    comment: 'If null, applies to all plans'
  },
  valid_from: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  valid_to: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'If null, no expiration date'
  },
  max_uses: {
    type: DataTypes.INTEGER,
    allowNull: true,
    comment: 'If null, unlimited uses'
  },
  current_uses: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  user_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'users',
      key: 'id'
    },
    comment: 'If set, promo code is only valid for this user'
  },
  farm_id: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'farms',
      key: 'id'
    },
    comment: 'If set, promo code is only valid for this farm'
  },
  discount_percent: {
    type: DataTypes.DECIMAL(5, 2),
    allowNull: true,
    validate: {
      min: 0,
      max: 100
    },
    comment: 'Percentage discount (0-100)'
  },
  discount_amount: {
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    validate: {
      min: 0
    },
    comment: 'Fixed amount discount'
  },
  discount_months: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1
    },
    comment: 'Number of months the discount applies for. If null, applies perpetually'
  },
  applies_to_monthly: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether the promo code applies to monthly billing plans'
  },
  applies_to_yearly: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true,
    comment: 'Whether the promo code applies to yearly billing plans'
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  created_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'promo_codes',
  
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: 'updated_at',
  validate: {
    eitherDiscountPercentOrAmount() {
      if ((this.discount_percent === null && this.discount_amount === null) || 
          (this.discount_percent !== null && this.discount_amount !== null)) {
        throw new Error('Either discount_percent or discount_amount must be set, but not both');
      }
    }
  }
});

// Define associations
// Note: Associations are now defined in associations.js to avoid duplicates

export default PromoCode;