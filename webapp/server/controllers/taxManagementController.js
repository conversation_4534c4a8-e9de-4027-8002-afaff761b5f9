import { Op } from 'sequelize';
import Farm from '../models/Farm.js';
import Transaction from '../models/Transaction.js';
import TaxCategory from '../models/TaxCategory.js';
import TaxDeduction from '../models/TaxDeduction.js';
import TaxDocument from '../models/TaxDocument.js';
import EmployeeTaxInfo from '../models/EmployeeTaxInfo.js';
import ContractorTaxInfo from '../models/ContractorTaxInfo.js';
import TaxPayment from '../models/TaxPayment.js';
import TaxFiling from '../models/TaxFiling.js';
import Employee from '../models/Employee.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import PayStub from '../models/PayStub.js';
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';

/**
 * Helper function to calculate potential tax savings (mock implementation)
 */
const calculatePotentialTaxSavings = (farmId, totalIncome, totalExpenses, totalDeductions) => {
  // In a real implementation, this would analyze the farm's financial data
  // and identify potential tax deductions and credits
  // For now, return a simplified calculation

  // Assume potential additional deductions of 10% of current expenses
  const potentialAdditionalDeductions = totalExpenses * 0.1;

  // Simplified tax rate of 30%
  const potentialSavings = potentialAdditionalDeductions * 0.3;

  return potentialSavings;
};

/**
 * Get tax summary for a farm
 */
export const getTaxSummary = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get transactions for the farm for the specified year
    const transactions = await Transaction.findAll({
      where: {
        farm_id: farmId,
        transaction_date: {
          [Op.between]: [
            new Date(`${year}-01-01`),
            new Date(`${year}-12-31`)
          ]
        }
      }
    });

    // Calculate total income and expenses
    let totalIncome = 0;
    let totalExpenses = 0;

    transactions.forEach(transaction => {
      if (transaction.amount > 0) {
        totalIncome += transaction.amount;
      } else {
        totalExpenses += Math.abs(transaction.amount);
      }
    });

    // Get tax deductions for the farm for the specified year
    const taxDeductions = await TaxDeduction.findAll({
      where: {
        farm_id: farmId,
        tax_year: year
      }
    });

    // Calculate total deductions
    const totalDeductions = taxDeductions.reduce((sum, deduction) => {
      return sum + (deduction.status === 'approved' ? deduction.amount : 0);
    }, 0);

    // Calculate estimated taxable income and tax liability
    const estimatedTaxableIncome = Math.max(0, totalIncome - totalExpenses - totalDeductions);

    // Simple tax calculation (in a real app, this would be more complex)
    let estimatedTaxLiability = 0;
    if (estimatedTaxableIncome > 0) {
      // Progressive tax rate example (simplified)
      if (estimatedTaxableIncome <= 50000) {
        estimatedTaxLiability = estimatedTaxableIncome * 0.15;
      } else if (estimatedTaxableIncome <= 100000) {
        estimatedTaxLiability = 7500 + (estimatedTaxableIncome - 50000) * 0.25;
      } else {
        estimatedTaxLiability = 20000 + (estimatedTaxableIncome - 100000) * 0.35;
      }
    }

    // Calculate potential tax savings (simplified)
    const potentialSavings = calculatePotentialTaxSavings(farmId, totalIncome, totalExpenses, totalDeductions);

    // Return the tax summary
    return res.status(200).json({
      taxSummary: {
        totalIncome,
        totalExpenses,
        totalDeductions,
        estimatedTaxableIncome,
        estimatedTaxLiability,
        potentialSavings
      }
    });
  } catch (error) {
    console.error('Error getting tax summary:', error);
    return res.status(500).json({ error: 'Failed to get tax summary' });
  }
};

/**
 * Get tax categories for a farm
 */
export const getTaxCategories = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get tax categories for the farm
    const taxCategories = await TaxCategory.findAll({
      where: {
        farm_id: farmId
      }
    });

    // Get transactions for each category
    const categoriesWithAmounts = await Promise.all(
      taxCategories.map(async (category) => {
        const transactions = await Transaction.findAll({
          where: {
            farm_id: farmId,
            category_id: category.id,
            transaction_date: {
              [Op.between]: [
                new Date(`${year}-01-01`),
                new Date(`${year}-12-31`)
              ]
            }
          }
        });

        const amount = transactions.reduce((sum, transaction) => {
          return sum + Math.abs(transaction.amount);
        }, 0);

        return {
          id: category.id,
          name: category.name,
          description: category.description,
          amount,
          deductible: category.deductible
        };
      })
    );

    return res.status(200).json({
      taxCategories: categoriesWithAmounts
    });
  } catch (error) {
    console.error('Error getting tax categories:', error);
    return res.status(500).json({ error: 'Failed to get tax categories' });
  }
};

/**
 * Get tax deductions for a farm
 */
export const getTaxDeductions = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear(), status } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId,
      tax_year: year
    };

    if (status) {
      whereConditions.status = status;
    }

    // Get tax deductions for the farm
    const taxDeductions = await TaxDeduction.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']]
    });

    // Format the deductions
    const formattedDeductions = taxDeductions.map(deduction => ({
      id: deduction.id,
      name: deduction.name,
      description: deduction.description,
      amount: deduction.amount,
      category: deduction.category,
      status: deduction.status,
      createdAt: deduction.created_at,
      updatedAt: deduction.updated_at
    }));

    return res.status(200).json({
      taxDeductions: formattedDeductions
    });
  } catch (error) {
    console.error('Error getting tax deductions:', error);
    return res.status(500).json({ error: 'Failed to get tax deductions' });
  }
};

/**
 * Create a new tax deduction
 */
export const createTaxDeduction = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { name, description, amount, category, taxYear = new Date().getFullYear() } = req.body;

    // Validate required fields
    if (!name || !amount || !category) {
      return res.status(400).json({ error: 'Name, amount, and category are required' });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create the tax deduction
    const taxDeduction = await TaxDeduction.create({
      farm_id: farmId,
      name,
      description,
      amount,
      category,
      tax_year: taxYear,
      status: 'pending' // New deductions start as pending
    });

    return res.status(201).json({
      taxDeduction: {
        id: taxDeduction.id,
        name: taxDeduction.name,
        description: taxDeduction.description,
        amount: taxDeduction.amount,
        category: taxDeduction.category,
        taxYear: taxDeduction.tax_year,
        status: taxDeduction.status,
        createdAt: taxDeduction.created_at,
        updatedAt: taxDeduction.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating tax deduction:', error);
    return res.status(500).json({ error: 'Failed to create tax deduction' });
  }
};

/**
 * Update a tax deduction
 */
export const updateTaxDeduction = async (req, res) => {
  try {
    const { deductionId } = req.params;
    const { name, description, amount, category, status } = req.body;

    // Find the tax deduction
    const taxDeduction = await TaxDeduction.findByPk(deductionId);
    if (!taxDeduction) {
      return res.status(404).json({ error: 'Tax deduction not found' });
    }

    // Update the tax deduction
    if (name) taxDeduction.name = name;
    if (description !== undefined) taxDeduction.description = description;
    if (amount) taxDeduction.amount = amount;
    if (category) taxDeduction.category = category;
    if (status) taxDeduction.status = status;

    await taxDeduction.save();

    return res.status(200).json({
      taxDeduction: {
        id: taxDeduction.id,
        name: taxDeduction.name,
        description: taxDeduction.description,
        amount: taxDeduction.amount,
        category: taxDeduction.category,
        taxYear: taxDeduction.tax_year,
        status: taxDeduction.status,
        createdAt: taxDeduction.created_at,
        updatedAt: taxDeduction.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating tax deduction:', error);
    return res.status(500).json({ error: 'Failed to update tax deduction' });
  }
};

/**
 * Delete a tax deduction
 */
export const deleteTaxDeduction = async (req, res) => {
  try {
    const { deductionId } = req.params;

    // Find the tax deduction
    const taxDeduction = await TaxDeduction.findByPk(deductionId);
    if (!taxDeduction) {
      return res.status(404).json({ error: 'Tax deduction not found' });
    }

    // Delete the tax deduction
    await taxDeduction.destroy();

    return res.status(200).json({
      message: 'Tax deduction deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tax deduction:', error);
    return res.status(500).json({ error: 'Failed to delete tax deduction' });
  }
};

/**
 * Get tax planning recommendations
 */
export const getTaxPlanningRecommendations = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // In a real implementation, this would analyze the farm's financial data
    // and generate personalized tax planning recommendations
    // For now, return some generic recommendations

    const recommendations = [
      {
        id: '1',
        title: 'Consider Section 179 Deduction',
        description: 'You have equipment purchases that may qualify for Section 179 deduction. This could allow you to deduct up to $1,080,000 in 2023.',
        potentialSavings: 25000,
        priority: 'high',
        category: 'equipment'
      },
      {
        id: '2',
        title: 'Income Deferral Opportunity',
        description: 'Consider deferring income to next year by delaying crop sales until January. This could potentially reduce your current year tax liability.',
        potentialSavings: 15000,
        priority: 'medium',
        category: 'income'
      },
      {
        id: '3',
        title: 'Retirement Plan Contributions',
        description: 'Maximize contributions to retirement plans to reduce taxable income.',
        potentialSavings: 8000,
        priority: 'medium',
        category: 'retirement'
      }
    ];

    return res.status(200).json({
      recommendations
    });
  } catch (error) {
    console.error('Error getting tax planning recommendations:', error);
    return res.status(500).json({ error: 'Failed to get tax planning recommendations' });
  }
};

// ==================== TAX DOCUMENT ENDPOINTS ====================

/**
 * Get tax documents for a farm
 */
export const getTaxDocuments = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear(), documentType, status } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId,
      tax_year: year
    };

    if (documentType) {
      whereConditions.document_type = documentType;
    }

    if (status) {
      whereConditions.status = status;
    }

    // Get tax documents for the farm
    const taxDocuments = await TaxDocument.findAll({
      where: whereConditions,
      order: [['created_at', 'DESC']],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    // Format the documents
    const formattedDocuments = taxDocuments.map(doc => ({
      id: doc.id,
      title: doc.title,
      description: doc.description,
      documentType: doc.document_type,
      taxYear: doc.tax_year,
      fileName: doc.file_name,
      fileSize: doc.file_size,
      fileType: doc.file_type,
      status: doc.status,
      submittedDate: doc.submitted_date,
      createdBy: doc.creator ? {
        id: doc.creator.id,
        name: `${doc.creator.first_name} ${doc.creator.last_name}`,
        email: doc.creator.email
      } : null,
      createdAt: doc.created_at,
      updatedAt: doc.updated_at
    }));

    return res.status(200).json({
      taxDocuments: formattedDocuments
    });
  } catch (error) {
    console.error('Error getting tax documents:', error);
    return res.status(500).json({ error: 'Failed to get tax documents' });
  }
};

/**
 * Get a specific tax document
 */
export const getTaxDocument = async (req, res) => {
  try {
    const { documentId } = req.params;

    // Find the tax document
    const taxDocument = await TaxDocument.findByPk(documentId, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        }
      ]
    });

    if (!taxDocument) {
      return res.status(404).json({ error: 'Tax document not found' });
    }

    // Format the document
    const formattedDocument = {
      id: taxDocument.id,
      farmId: taxDocument.farm_id,
      title: taxDocument.title,
      description: taxDocument.description,
      documentType: taxDocument.document_type,
      taxYear: taxDocument.tax_year,
      filePath: taxDocument.file_path,
      fileName: taxDocument.file_name,
      fileSize: taxDocument.file_size,
      fileType: taxDocument.file_type,
      status: taxDocument.status,
      submittedDate: taxDocument.submitted_date,
      createdBy: taxDocument.creator ? {
        id: taxDocument.creator.id,
        name: `${taxDocument.creator.first_name} ${taxDocument.creator.last_name}`,
        email: taxDocument.creator.email
      } : null,
      createdAt: taxDocument.created_at,
      updatedAt: taxDocument.updated_at
    };

    return res.status(200).json({
      taxDocument: formattedDocument
    });
  } catch (error) {
    console.error('Error getting tax document:', error);
    return res.status(500).json({ error: 'Failed to get tax document' });
  }
};

/**
 * Create a new tax document
 */
export const createTaxDocument = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      title, 
      description, 
      documentType, 
      taxYear = new Date().getFullYear(),
      status = 'draft'
    } = req.body;

    // Validate required fields
    if (!title || !documentType) {
      return res.status(400).json({ error: 'Title and document type are required' });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create the tax document
    const taxDocument = await TaxDocument.create({
      farm_id: farmId,
      title,
      description,
      document_type: documentType,
      tax_year: taxYear,
      status,
      created_by: req.user.id
    });

    return res.status(201).json({
      taxDocument: {
        id: taxDocument.id,
        farmId: taxDocument.farm_id,
        title: taxDocument.title,
        description: taxDocument.description,
        documentType: taxDocument.document_type,
        taxYear: taxDocument.tax_year,
        status: taxDocument.status,
        createdBy: req.user.id,
        createdAt: taxDocument.created_at,
        updatedAt: taxDocument.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating tax document:', error);
    return res.status(500).json({ error: 'Failed to create tax document' });
  }
};

/**
 * Update a tax document
 */
export const updateTaxDocument = async (req, res) => {
  try {
    const { documentId } = req.params;
    const { 
      title, 
      description, 
      documentType, 
      taxYear,
      status,
      submittedDate
    } = req.body;

    // Find the tax document
    const taxDocument = await TaxDocument.findByPk(documentId);
    if (!taxDocument) {
      return res.status(404).json({ error: 'Tax document not found' });
    }

    // Update the tax document
    if (title) taxDocument.title = title;
    if (description !== undefined) taxDocument.description = description;
    if (documentType) taxDocument.document_type = documentType;
    if (taxYear) taxDocument.tax_year = taxYear;
    if (status) taxDocument.status = status;
    if (submittedDate) taxDocument.submitted_date = submittedDate;

    await taxDocument.save();

    return res.status(200).json({
      taxDocument: {
        id: taxDocument.id,
        farmId: taxDocument.farm_id,
        title: taxDocument.title,
        description: taxDocument.description,
        documentType: taxDocument.document_type,
        taxYear: taxDocument.tax_year,
        filePath: taxDocument.file_path,
        fileName: taxDocument.file_name,
        fileSize: taxDocument.file_size,
        fileType: taxDocument.file_type,
        status: taxDocument.status,
        submittedDate: taxDocument.submitted_date,
        createdBy: taxDocument.created_by,
        createdAt: taxDocument.created_at,
        updatedAt: taxDocument.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating tax document:', error);
    return res.status(500).json({ error: 'Failed to update tax document' });
  }
};

/**
 * Delete a tax document
 */
export const deleteTaxDocument = async (req, res) => {
  try {
    const { documentId } = req.params;

    // Find the tax document
    const taxDocument = await TaxDocument.findByPk(documentId);
    if (!taxDocument) {
      return res.status(404).json({ error: 'Tax document not found' });
    }

    // If there's a file associated with the document, delete it
    if (taxDocument.file_path) {
      const unlinkAsync = promisify(fs.unlink);
      try {
        await unlinkAsync(taxDocument.file_path);
      } catch (fileError) {
        console.error('Error deleting file:', fileError);
        // Continue with document deletion even if file deletion fails
      }
    }

    // Delete the tax document
    await taxDocument.destroy();

    return res.status(200).json({
      message: 'Tax document deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tax document:', error);
    return res.status(500).json({ error: 'Failed to delete tax document' });
  }
};

/**
 * Upload a file for a tax document
 */
export const uploadTaxDocumentFile = async (req, res) => {
  try {
    const { documentId } = req.params;

    // Check if file was uploaded
    if (!req.files || !req.files.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const file = req.files.file;

    // Find the tax document
    const taxDocument = await TaxDocument.findByPk(documentId);
    if (!taxDocument) {
      return res.status(404).json({ error: 'Tax document not found' });
    }

    // Import necessary utilities
    const { 
      validateFileType, 
      generateStoragePath, 
      saveFile, 
      deleteFile, 
      checkStorageQuota, 
      updateStorageUsage 
    } = await import('../utils/fileUtils.js');
    const Document = (await import('../models/Document.js')).default;

    // Validate file type
    const fileBuffer = Buffer.from(file.data);
    const fileTypeValidation = await validateFileType(file.name, fileBuffer);

    if (!fileTypeValidation.valid) {
      return res.status(400).json({ 
        error: 'Invalid file type', 
        reason: fileTypeValidation.reason 
      });
    }

    // Check storage quota - pass global admin status if available
    const isGlobalAdmin = req.user && req.user.is_global_admin ? true : false;
    const quotaCheck = await checkStorageQuota(taxDocument.farm_id, file.size, isGlobalAdmin);

    if (!quotaCheck.allowed) {
      return res.status(400).json({ 
        error: quotaCheck.reason,
        currentUsage: quotaCheck.currentUsage,
        quota: quotaCheck.quota
      });
    }

    // Generate storage path
    const storagePath = generateStoragePath(taxDocument.farm_id, taxDocument.created_by, file.name);

    // Save file using document system
    const fullPath = await saveFile(file.data, storagePath);

    // If there's already a file associated with the document, delete it
    if (taxDocument.file_path) {
      try {
        await deleteFile(taxDocument.file_path);
      } catch (fileError) {
        console.error('Error deleting existing file:', fileError);
        // Continue with file update even if existing file deletion fails
      }
    }

    // Create a document record in the document system
    const document = await Document.create({
      name: file.name,
      description: `Tax document: ${taxDocument.title}`,
      file_path: storagePath,
      file_size: file.size,
      file_type: path.extname(file.name).substring(1) || 'unknown',
      mime_type: fileTypeValidation.detectedType || file.mimetype,
      is_external: false,
      folder_id: null, // No specific folder for tax documents
      farm_id: taxDocument.farm_id, // Changed from tenant_id to farm_id
      tenant_id: taxDocument.farm_id, // Using farm_id as tenant_id for compatibility
      uploaded_by: taxDocument.created_by
    });

    // Update the tax document with the file information
    taxDocument.file_path = storagePath;
    taxDocument.file_name = file.name;
    taxDocument.file_size = file.size;
    taxDocument.file_type = path.extname(file.name).substring(1) || 'unknown';

    await taxDocument.save();

    // Update storage usage
    await updateStorageUsage(taxDocument.farm_id, file.size, false, 1);

    return res.status(200).json({
      message: 'File uploaded successfully',
      taxDocument: {
        id: taxDocument.id,
        fileName: taxDocument.file_name,
        fileSize: taxDocument.file_size,
        fileType: taxDocument.file_type,
        updatedAt: taxDocument.updated_at
      },
      document: {
        id: document.id,
        name: document.name,
        filePath: document.file_path
      }
    });
  } catch (error) {
    console.error('Error uploading file:', error);
    return res.status(500).json({ error: 'Failed to upload file' });
  }
};

// ==================== EMPLOYEE TAX INFO ENDPOINTS ====================

/**
 * Get employee tax info for a farm
 */
export const getEmployeeTaxInfo = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get employee tax info for the farm
    const employeeTaxInfo = await EmployeeTaxInfo.findAll({
      where: {
        farm_id: farmId,
        tax_year: year
      },
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'first_name', 'last_name', 'email', 'role']
        },
        {
          model: TaxDocument,
          as: 'w2Document',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Format the employee tax info
    const formattedTaxInfo = employeeTaxInfo.map(info => ({
      id: info.id,
      employeeId: info.employee_id,
      employee: info.employee ? {
        id: info.employee.id,
        name: `${info.employee.first_name} ${info.employee.last_name}`,
        email: info.employee.email,
        position: info.employee.role
      } : null,
      taxYear: info.tax_year,
      filingStatus: info.filing_status,
      withholdingAllowances: info.withholding_allowances,
      additionalWithholding: info.additional_withholding,
      isExempt: info.is_exempt,
      w2Generated: info.w2_generated,
      w2Document: info.w2Document ? {
        id: info.w2Document.id,
        title: info.w2Document.title,
        fileName: info.w2Document.file_name,
        status: info.w2Document.status
      } : null,
      createdAt: info.created_at,
      updatedAt: info.updated_at
    }));

    return res.status(200).json({
      employeeTaxInfo: formattedTaxInfo
    });
  } catch (error) {
    console.error('Error getting employee tax info:', error);
    return res.status(500).json({ error: 'Failed to get employee tax info' });
  }
};

/**
 * Get a specific employee tax info
 */
export const getEmployeeTaxInfoById = async (req, res) => {
  try {
    const { taxInfoId } = req.params;

    // Find the employee tax info
    const employeeTaxInfo = await EmployeeTaxInfo.findByPk(taxInfoId, {
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'first_name', 'last_name', 'email', 'role']
        },
        {
          model: TaxDocument,
          as: 'w2Document',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ]
    });

    if (!employeeTaxInfo) {
      return res.status(404).json({ error: 'Employee tax info not found' });
    }

    // Format the employee tax info
    const formattedTaxInfo = {
      id: employeeTaxInfo.id,
      farmId: employeeTaxInfo.farm_id,
      employeeId: employeeTaxInfo.employee_id,
      employee: employeeTaxInfo.employee ? {
        id: employeeTaxInfo.employee.id,
        name: `${employeeTaxInfo.employee.first_name} ${employeeTaxInfo.employee.last_name}`,
        email: employeeTaxInfo.employee.email,
        position: employeeTaxInfo.employee.role
      } : null,
      taxYear: employeeTaxInfo.tax_year,
      ssn: employeeTaxInfo.ssn ? '***-**-' + employeeTaxInfo.ssn.slice(-4) : null, // Mask SSN for security
      filingStatus: employeeTaxInfo.filing_status,
      withholdingAllowances: employeeTaxInfo.withholding_allowances,
      additionalWithholding: employeeTaxInfo.additional_withholding,
      isExempt: employeeTaxInfo.is_exempt,
      w2Generated: employeeTaxInfo.w2_generated,
      w2Document: employeeTaxInfo.w2Document ? {
        id: employeeTaxInfo.w2Document.id,
        title: employeeTaxInfo.w2Document.title,
        fileName: employeeTaxInfo.w2Document.file_name,
        status: employeeTaxInfo.w2Document.status
      } : null,
      createdAt: employeeTaxInfo.created_at,
      updatedAt: employeeTaxInfo.updated_at
    };

    return res.status(200).json({
      employeeTaxInfo: formattedTaxInfo
    });
  } catch (error) {
    console.error('Error getting employee tax info:', error);
    return res.status(500).json({ error: 'Failed to get employee tax info' });
  }
};

/**
 * Create employee tax info
 */
export const createEmployeeTaxInfo = async (req, res) => {
  try {
    const { farmId, employeeId } = req.params;
    const { 
      taxYear = new Date().getFullYear(),
      ssn,
      filingStatus,
      withholdingAllowances = 0,
      additionalWithholding = 0,
      isExempt = false
    } = req.body;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Validate employee exists
    const employee = await Employee.findOne({
      where: {
        id: employeeId,
        farm_id: farmId
      }
    });
    if (!employee) {
      return res.status(404).json({ error: 'Employee not found or not associated with this farm' });
    }

    // Check if tax info already exists for this employee and year
    const existingTaxInfo = await EmployeeTaxInfo.findOne({
      where: {
        employee_id: employeeId,
        farm_id: farmId,
        tax_year: taxYear
      }
    });

    if (existingTaxInfo) {
      return res.status(400).json({ error: 'Tax info already exists for this employee and tax year' });
    }

    // Create the employee tax info
    const employeeTaxInfo = await EmployeeTaxInfo.create({
      employee_id: employeeId,
      farm_id: farmId,
      tax_year: taxYear,
      ssn,
      filing_status: filingStatus,
      withholding_allowances: withholdingAllowances,
      additional_withholding: additionalWithholding,
      is_exempt: isExempt,
      w2_generated: false
    });

    return res.status(201).json({
      employeeTaxInfo: {
        id: employeeTaxInfo.id,
        employeeId: employeeTaxInfo.employee_id,
        farmId: employeeTaxInfo.farm_id,
        taxYear: employeeTaxInfo.tax_year,
        filingStatus: employeeTaxInfo.filing_status,
        withholdingAllowances: employeeTaxInfo.withholding_allowances,
        additionalWithholding: employeeTaxInfo.additional_withholding,
        isExempt: employeeTaxInfo.is_exempt,
        w2Generated: employeeTaxInfo.w2_generated,
        createdAt: employeeTaxInfo.created_at,
        updatedAt: employeeTaxInfo.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating employee tax info:', error);
    return res.status(500).json({ error: 'Failed to create employee tax info' });
  }
};

/**
 * Update employee tax info
 */
export const updateEmployeeTaxInfo = async (req, res) => {
  try {
    const { taxInfoId } = req.params;
    const { 
      ssn,
      filingStatus,
      withholdingAllowances,
      additionalWithholding,
      isExempt
    } = req.body;

    // Find the employee tax info
    const employeeTaxInfo = await EmployeeTaxInfo.findByPk(taxInfoId);
    if (!employeeTaxInfo) {
      return res.status(404).json({ error: 'Employee tax info not found' });
    }

    // Update the employee tax info
    if (ssn) employeeTaxInfo.ssn = ssn;
    if (filingStatus) employeeTaxInfo.filing_status = filingStatus;
    if (withholdingAllowances !== undefined) employeeTaxInfo.withholding_allowances = withholdingAllowances;
    if (additionalWithholding !== undefined) employeeTaxInfo.additional_withholding = additionalWithholding;
    if (isExempt !== undefined) employeeTaxInfo.is_exempt = isExempt;

    await employeeTaxInfo.save();

    return res.status(200).json({
      employeeTaxInfo: {
        id: employeeTaxInfo.id,
        employeeId: employeeTaxInfo.employee_id,
        farmId: employeeTaxInfo.farm_id,
        taxYear: employeeTaxInfo.tax_year,
        filingStatus: employeeTaxInfo.filing_status,
        withholdingAllowances: employeeTaxInfo.withholding_allowances,
        additionalWithholding: employeeTaxInfo.additional_withholding,
        isExempt: employeeTaxInfo.is_exempt,
        w2Generated: employeeTaxInfo.w2_generated,
        w2DocumentId: employeeTaxInfo.w2_document_id,
        createdAt: employeeTaxInfo.created_at,
        updatedAt: employeeTaxInfo.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating employee tax info:', error);
    return res.status(500).json({ error: 'Failed to update employee tax info' });
  }
};

/**
 * Delete employee tax info
 */
export const deleteEmployeeTaxInfo = async (req, res) => {
  try {
    const { taxInfoId } = req.params;

    // Find the employee tax info
    const employeeTaxInfo = await EmployeeTaxInfo.findByPk(taxInfoId);
    if (!employeeTaxInfo) {
      return res.status(404).json({ error: 'Employee tax info not found' });
    }

    // If there's a W2 document associated, don't delete the tax info
    if (employeeTaxInfo.w2_document_id) {
      return res.status(400).json({ 
        error: 'Cannot delete tax info with an associated W2 document. Delete the W2 document first.' 
      });
    }

    // Delete the employee tax info
    await employeeTaxInfo.destroy();

    return res.status(200).json({
      message: 'Employee tax info deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting employee tax info:', error);
    return res.status(500).json({ error: 'Failed to delete employee tax info' });
  }
};

/**
 * Generate W2 for employee
 */
export const generateW2 = async (req, res) => {
  try {
    const { taxInfoId } = req.params;
    const { year = new Date().getFullYear() } = req.body;

    // Find the employee tax info
    const employeeTaxInfo = await EmployeeTaxInfo.findByPk(taxInfoId, {
      include: [
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'first_name', 'last_name', 'email', 'role']
        }
      ]
    });

    if (!employeeTaxInfo) {
      return res.status(404).json({ error: 'Employee tax info not found' });
    }

    // Check if W2 already generated
    if (employeeTaxInfo.w2_generated && employeeTaxInfo.w2_document_id) {
      return res.status(400).json({ 
        error: 'W2 already generated for this employee',
        documentId: employeeTaxInfo.w2_document_id
      });
    }

    // In a real implementation, this would generate an actual W2 form
    // For now, we'll just create a tax document to represent the W2

    // Create a tax document for the W2
    const w2Document = await TaxDocument.create({
      farm_id: employeeTaxInfo.farm_id,
      title: `W2 for ${employeeTaxInfo.employee.first_name} ${employeeTaxInfo.employee.last_name} - ${year}`,
      description: `W2 tax form for ${employeeTaxInfo.employee.first_name} ${employeeTaxInfo.employee.last_name} for tax year ${year}`,
      document_type: 'w2',
      tax_year: year,
      status: 'draft',
      created_by: req.user.id
    });

    // Update the employee tax info with the W2 document ID
    employeeTaxInfo.w2_generated = true;
    employeeTaxInfo.w2_document_id = w2Document.id;
    await employeeTaxInfo.save();

    return res.status(200).json({
      message: 'W2 generated successfully',
      w2Document: {
        id: w2Document.id,
        title: w2Document.title,
        documentType: w2Document.document_type,
        taxYear: w2Document.tax_year,
        status: w2Document.status,
        createdAt: w2Document.created_at
      }
    });
  } catch (error) {
    console.error('Error generating W2:', error);
    return res.status(500).json({ error: 'Failed to generate W2' });
  }
};

// ==================== CONTRACTOR TAX INFO ENDPOINTS ====================

/**
 * Get contractor tax info for a farm
 */
export const getContractorTaxInfo = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get contractor tax info for the farm
    const contractorTaxInfo = await ContractorTaxInfo.findAll({
      where: {
        farm_id: farmId,
        tax_year: year
      },
      include: [
        {
          model: TaxDocument,
          as: 'form1099Document',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ],
      order: [['created_at', 'DESC']]
    });

    // Format the contractor tax info
    const formattedTaxInfo = contractorTaxInfo.map(info => ({
      id: info.id,
      contractorName: info.contractor_name,
      businessName: info.business_name,
      taxYear: info.tax_year,
      totalPayments: info.total_payments,
      form1099Generated: info.form_1099_generated,
      form1099Document: info.form1099Document ? {
        id: info.form1099Document.id,
        title: info.form1099Document.title,
        fileName: info.form1099Document.file_name,
        status: info.form1099Document.status
      } : null,
      createdAt: info.created_at,
      updatedAt: info.updated_at
    }));

    return res.status(200).json({
      contractorTaxInfo: formattedTaxInfo
    });
  } catch (error) {
    console.error('Error getting contractor tax info:', error);
    return res.status(500).json({ error: 'Failed to get contractor tax info' });
  }
};

/**
 * Get a specific contractor tax info
 */
export const getContractorTaxInfoById = async (req, res) => {
  try {
    const { taxInfoId } = req.params;

    // Find the contractor tax info
    const contractorTaxInfo = await ContractorTaxInfo.findByPk(taxInfoId, {
      include: [
        {
          model: TaxDocument,
          as: 'form1099Document',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ]
    });

    if (!contractorTaxInfo) {
      return res.status(404).json({ error: 'Contractor tax info not found' });
    }

    // Format the contractor tax info
    const formattedTaxInfo = {
      id: contractorTaxInfo.id,
      farmId: contractorTaxInfo.farm_id,
      contractorName: contractorTaxInfo.contractor_name,
      businessName: contractorTaxInfo.business_name,
      taxId: contractorTaxInfo.tax_id ? '***-**-' + contractorTaxInfo.tax_id.slice(-4) : null, // Mask tax ID for security
      address: contractorTaxInfo.address,
      city: contractorTaxInfo.city,
      state: contractorTaxInfo.state,
      zipCode: contractorTaxInfo.zip_code,
      taxYear: contractorTaxInfo.tax_year,
      totalPayments: contractorTaxInfo.total_payments,
      form1099Generated: contractorTaxInfo.form_1099_generated,
      form1099Document: contractorTaxInfo.form1099Document ? {
        id: contractorTaxInfo.form1099Document.id,
        title: contractorTaxInfo.form1099Document.title,
        fileName: contractorTaxInfo.form1099Document.file_name,
        status: contractorTaxInfo.form1099Document.status
      } : null,
      createdAt: contractorTaxInfo.created_at,
      updatedAt: contractorTaxInfo.updated_at
    };

    return res.status(200).json({
      contractorTaxInfo: formattedTaxInfo
    });
  } catch (error) {
    console.error('Error getting contractor tax info:', error);
    return res.status(500).json({ error: 'Failed to get contractor tax info' });
  }
};

/**
 * Create contractor tax info
 */
export const createContractorTaxInfo = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      contractorName,
      businessName,
      taxId,
      address,
      city,
      state,
      zipCode,
      taxYear = new Date().getFullYear(),
      totalPayments = 0
    } = req.body;

    // Validate required fields
    if (!contractorName) {
      return res.status(400).json({ error: 'Contractor name is required' });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if tax info already exists for this contractor and year
    const existingTaxInfo = await ContractorTaxInfo.findOne({
      where: {
        farm_id: farmId,
        contractor_name: contractorName,
        business_name: businessName,
        tax_year: taxYear
      }
    });

    if (existingTaxInfo) {
      return res.status(400).json({ error: 'Tax info already exists for this contractor and tax year' });
    }

    // Create the contractor tax info
    const contractorTaxInfo = await ContractorTaxInfo.create({
      farm_id: farmId,
      contractor_name: contractorName,
      business_name: businessName,
      tax_id: taxId,
      address,
      city,
      state,
      zip_code: zipCode,
      tax_year: taxYear,
      total_payments: totalPayments,
      form_1099_generated: false
    });

    return res.status(201).json({
      contractorTaxInfo: {
        id: contractorTaxInfo.id,
        farmId: contractorTaxInfo.farm_id,
        contractorName: contractorTaxInfo.contractor_name,
        businessName: contractorTaxInfo.business_name,
        address: contractorTaxInfo.address,
        city: contractorTaxInfo.city,
        state: contractorTaxInfo.state,
        zipCode: contractorTaxInfo.zip_code,
        taxYear: contractorTaxInfo.tax_year,
        totalPayments: contractorTaxInfo.total_payments,
        form1099Generated: contractorTaxInfo.form_1099_generated,
        createdAt: contractorTaxInfo.created_at,
        updatedAt: contractorTaxInfo.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating contractor tax info:', error);
    return res.status(500).json({ error: 'Failed to create contractor tax info' });
  }
};

/**
 * Update contractor tax info
 */
export const updateContractorTaxInfo = async (req, res) => {
  try {
    const { taxInfoId } = req.params;
    const { 
      contractorName,
      businessName,
      taxId,
      address,
      city,
      state,
      zipCode,
      totalPayments
    } = req.body;

    // Find the contractor tax info
    const contractorTaxInfo = await ContractorTaxInfo.findByPk(taxInfoId);
    if (!contractorTaxInfo) {
      return res.status(404).json({ error: 'Contractor tax info not found' });
    }

    // Update the contractor tax info
    if (contractorName) contractorTaxInfo.contractor_name = contractorName;
    if (businessName !== undefined) contractorTaxInfo.business_name = businessName;
    if (taxId) contractorTaxInfo.tax_id = taxId;
    if (address !== undefined) contractorTaxInfo.address = address;
    if (city !== undefined) contractorTaxInfo.city = city;
    if (state !== undefined) contractorTaxInfo.state = state;
    if (zipCode !== undefined) contractorTaxInfo.zip_code = zipCode;
    if (totalPayments !== undefined) contractorTaxInfo.total_payments = totalPayments;

    await contractorTaxInfo.save();

    return res.status(200).json({
      contractorTaxInfo: {
        id: contractorTaxInfo.id,
        farmId: contractorTaxInfo.farm_id,
        contractorName: contractorTaxInfo.contractor_name,
        businessName: contractorTaxInfo.business_name,
        address: contractorTaxInfo.address,
        city: contractorTaxInfo.city,
        state: contractorTaxInfo.state,
        zipCode: contractorTaxInfo.zip_code,
        taxYear: contractorTaxInfo.tax_year,
        totalPayments: contractorTaxInfo.total_payments,
        form1099Generated: contractorTaxInfo.form_1099_generated,
        form1099DocumentId: contractorTaxInfo.form_1099_document_id,
        createdAt: contractorTaxInfo.created_at,
        updatedAt: contractorTaxInfo.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating contractor tax info:', error);
    return res.status(500).json({ error: 'Failed to update contractor tax info' });
  }
};

/**
 * Delete contractor tax info
 */
export const deleteContractorTaxInfo = async (req, res) => {
  try {
    const { taxInfoId } = req.params;

    // Find the contractor tax info
    const contractorTaxInfo = await ContractorTaxInfo.findByPk(taxInfoId);
    if (!contractorTaxInfo) {
      return res.status(404).json({ error: 'Contractor tax info not found' });
    }

    // If there's a 1099 document associated, don't delete the tax info
    if (contractorTaxInfo.form_1099_document_id) {
      return res.status(400).json({ 
        error: 'Cannot delete tax info with an associated 1099 document. Delete the 1099 document first.' 
      });
    }

    // Delete the contractor tax info
    await contractorTaxInfo.destroy();

    return res.status(200).json({
      message: 'Contractor tax info deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting contractor tax info:', error);
    return res.status(500).json({ error: 'Failed to delete contractor tax info' });
  }
};

/**
 * Generate 1099 for contractor
 */
export const generate1099 = async (req, res) => {
  try {
    const { taxInfoId } = req.params;
    const { year = new Date().getFullYear() } = req.body;

    // Find the contractor tax info
    const contractorTaxInfo = await ContractorTaxInfo.findByPk(taxInfoId);

    if (!contractorTaxInfo) {
      return res.status(404).json({ error: 'Contractor tax info not found' });
    }

    // Check if 1099 already generated
    if (contractorTaxInfo.form_1099_generated && contractorTaxInfo.form_1099_document_id) {
      return res.status(400).json({ 
        error: '1099 already generated for this contractor',
        documentId: contractorTaxInfo.form_1099_document_id
      });
    }

    // In a real implementation, this would generate an actual 1099 form
    // For now, we'll just create a tax document to represent the 1099

    // Create a tax document for the 1099
    const form1099Document = await TaxDocument.create({
      farm_id: contractorTaxInfo.farm_id,
      title: `1099 for ${contractorTaxInfo.contractor_name} - ${year}`,
      description: `1099 tax form for ${contractorTaxInfo.contractor_name} for tax year ${year}`,
      document_type: '1099',
      tax_year: year,
      status: 'draft',
      created_by: req.user.id
    });

    // Update the contractor tax info with the 1099 document ID
    contractorTaxInfo.form_1099_generated = true;
    contractorTaxInfo.form_1099_document_id = form1099Document.id;
    await contractorTaxInfo.save();

    return res.status(200).json({
      message: '1099 generated successfully',
      form1099Document: {
        id: form1099Document.id,
        title: form1099Document.title,
        documentType: form1099Document.document_type,
        taxYear: form1099Document.tax_year,
        status: form1099Document.status,
        createdAt: form1099Document.created_at
      }
    });
  } catch (error) {
    console.error('Error generating 1099:', error);
    return res.status(500).json({ error: 'Failed to generate 1099' });
  }
};

// ==================== TAX PAYMENT ENDPOINTS ====================

/**
 * Get tax payments for a farm
 */
export const getTaxPayments = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear(), taxType, taxPeriod } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId,
      tax_year: year
    };

    if (taxType) {
      whereConditions.tax_type = taxType;
    }

    if (taxPeriod) {
      whereConditions.tax_period = taxPeriod;
    }

    // Get tax payments for the farm
    const taxPayments = await TaxPayment.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: TaxDocument,
          as: 'receiptDocument',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ],
      order: [['payment_date', 'DESC']]
    });

    // Format the tax payments
    const formattedPayments = taxPayments.map(payment => ({
      id: payment.id,
      paymentDate: payment.payment_date,
      amount: payment.amount,
      paymentMethod: payment.payment_method,
      paymentReference: payment.payment_reference,
      taxYear: payment.tax_year,
      taxPeriod: payment.tax_period,
      taxType: payment.tax_type,
      description: payment.description,
      receiptDocument: payment.receiptDocument ? {
        id: payment.receiptDocument.id,
        title: payment.receiptDocument.title,
        fileName: payment.receiptDocument.file_name,
        status: payment.receiptDocument.status
      } : null,
      createdBy: payment.creator ? {
        id: payment.creator.id,
        name: `${payment.creator.first_name} ${payment.creator.last_name}`,
        email: payment.creator.email
      } : null,
      createdAt: payment.created_at,
      updatedAt: payment.updated_at
    }));

    return res.status(200).json({
      taxPayments: formattedPayments
    });
  } catch (error) {
    console.error('Error getting tax payments:', error);
    return res.status(500).json({ error: 'Failed to get tax payments' });
  }
};

/**
 * Get a specific tax payment
 */
export const getTaxPayment = async (req, res) => {
  try {
    const { paymentId } = req.params;

    // Find the tax payment
    const taxPayment = await TaxPayment.findByPk(paymentId, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: TaxDocument,
          as: 'receiptDocument',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ]
    });

    if (!taxPayment) {
      return res.status(404).json({ error: 'Tax payment not found' });
    }

    // Format the tax payment
    const formattedPayment = {
      id: taxPayment.id,
      farmId: taxPayment.farm_id,
      paymentDate: taxPayment.payment_date,
      amount: taxPayment.amount,
      paymentMethod: taxPayment.payment_method,
      paymentReference: taxPayment.payment_reference,
      taxYear: taxPayment.tax_year,
      taxPeriod: taxPayment.tax_period,
      taxType: taxPayment.tax_type,
      description: taxPayment.description,
      receiptDocument: taxPayment.receiptDocument ? {
        id: taxPayment.receiptDocument.id,
        title: taxPayment.receiptDocument.title,
        fileName: taxPayment.receiptDocument.file_name,
        status: taxPayment.receiptDocument.status
      } : null,
      createdBy: taxPayment.creator ? {
        id: taxPayment.creator.id,
        name: `${taxPayment.creator.first_name} ${taxPayment.creator.last_name}`,
        email: taxPayment.creator.email
      } : null,
      createdAt: taxPayment.created_at,
      updatedAt: taxPayment.updated_at
    };

    return res.status(200).json({
      taxPayment: formattedPayment
    });
  } catch (error) {
    console.error('Error getting tax payment:', error);
    return res.status(500).json({ error: 'Failed to get tax payment' });
  }
};

/**
 * Create a new tax payment
 */
export const createTaxPayment = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      paymentDate,
      amount,
      paymentMethod,
      paymentReference,
      taxYear = new Date().getFullYear(),
      taxPeriod,
      taxType,
      description
    } = req.body;

    // Validate required fields
    if (!paymentDate || !amount || !paymentMethod || !taxPeriod || !taxType) {
      return res.status(400).json({ 
        error: 'Payment date, amount, payment method, tax period, and tax type are required' 
      });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create the tax payment
    const taxPayment = await TaxPayment.create({
      farm_id: farmId,
      payment_date: paymentDate,
      amount,
      payment_method: paymentMethod,
      payment_reference: paymentReference,
      tax_year: taxYear,
      tax_period: taxPeriod,
      tax_type: taxType,
      description,
      created_by: req.user.id
    });

    return res.status(201).json({
      taxPayment: {
        id: taxPayment.id,
        farmId: taxPayment.farm_id,
        paymentDate: taxPayment.payment_date,
        amount: taxPayment.amount,
        paymentMethod: taxPayment.payment_method,
        paymentReference: taxPayment.payment_reference,
        taxYear: taxPayment.tax_year,
        taxPeriod: taxPayment.tax_period,
        taxType: taxPayment.tax_type,
        description: taxPayment.description,
        createdBy: req.user.id,
        createdAt: taxPayment.created_at,
        updatedAt: taxPayment.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating tax payment:', error);
    return res.status(500).json({ error: 'Failed to create tax payment' });
  }
};

/**
 * Update a tax payment
 */
export const updateTaxPayment = async (req, res) => {
  try {
    const { paymentId } = req.params;
    const { 
      paymentDate,
      amount,
      paymentMethod,
      paymentReference,
      taxYear,
      taxPeriod,
      taxType,
      description
    } = req.body;

    // Find the tax payment
    const taxPayment = await TaxPayment.findByPk(paymentId);
    if (!taxPayment) {
      return res.status(404).json({ error: 'Tax payment not found' });
    }

    // Update the tax payment
    if (paymentDate) taxPayment.payment_date = paymentDate;
    if (amount !== undefined) taxPayment.amount = amount;
    if (paymentMethod) taxPayment.payment_method = paymentMethod;
    if (paymentReference !== undefined) taxPayment.payment_reference = paymentReference;
    if (taxYear) taxPayment.tax_year = taxYear;
    if (taxPeriod) taxPayment.tax_period = taxPeriod;
    if (taxType) taxPayment.tax_type = taxType;
    if (description !== undefined) taxPayment.description = description;

    await taxPayment.save();

    return res.status(200).json({
      taxPayment: {
        id: taxPayment.id,
        farmId: taxPayment.farm_id,
        paymentDate: taxPayment.payment_date,
        amount: taxPayment.amount,
        paymentMethod: taxPayment.payment_method,
        paymentReference: taxPayment.payment_reference,
        taxYear: taxPayment.tax_year,
        taxPeriod: taxPayment.tax_period,
        taxType: taxPayment.tax_type,
        description: taxPayment.description,
        receiptDocumentId: taxPayment.receipt_document_id,
        createdBy: taxPayment.created_by,
        createdAt: taxPayment.created_at,
        updatedAt: taxPayment.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating tax payment:', error);
    return res.status(500).json({ error: 'Failed to update tax payment' });
  }
};

/**
 * Delete a tax payment
 */
export const deleteTaxPayment = async (req, res) => {
  try {
    const { paymentId } = req.params;

    // Find the tax payment
    const taxPayment = await TaxPayment.findByPk(paymentId);
    if (!taxPayment) {
      return res.status(404).json({ error: 'Tax payment not found' });
    }

    // If there's a receipt document associated, don't delete the payment
    if (taxPayment.receipt_document_id) {
      return res.status(400).json({ 
        error: 'Cannot delete payment with an associated receipt document. Delete the receipt document first.' 
      });
    }

    // Delete the tax payment
    await taxPayment.destroy();

    return res.status(200).json({
      message: 'Tax payment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tax payment:', error);
    return res.status(500).json({ error: 'Failed to delete tax payment' });
  }
};

// ==================== TAX FILING ENDPOINTS ====================

/**
 * Get tax filings for a farm
 */
export const getTaxFilings = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear(), filingType, status } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build query conditions
    const whereConditions = {
      farm_id: farmId,
      tax_year: year
    };

    if (filingType) {
      whereConditions.filing_type = filingType;
    }

    if (status) {
      whereConditions.status = status;
    }

    // Get tax filings for the farm
    const taxFilings = await TaxFiling.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: TaxDocument,
          as: 'filingDocument',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ],
      order: [['due_date', 'ASC']]
    });

    // Format the tax filings
    const formattedFilings = taxFilings.map(filing => ({
      id: filing.id,
      taxYear: filing.tax_year,
      filingType: filing.filing_type,
      formNumber: filing.form_number,
      dueDate: filing.due_date,
      filingDate: filing.filing_date,
      extensionFiled: filing.extension_filed,
      extensionDate: filing.extension_date,
      status: filing.status,
      filingMethod: filing.filing_method,
      confirmationNumber: filing.confirmation_number,
      totalTax: filing.total_tax,
      totalPaid: filing.total_paid,
      balanceDue: filing.balance_due,
      refundAmount: filing.refund_amount,
      notes: filing.notes,
      filingDocument: filing.filingDocument ? {
        id: filing.filingDocument.id,
        title: filing.filingDocument.title,
        fileName: filing.filingDocument.file_name,
        status: filing.filingDocument.status
      } : null,
      createdBy: filing.creator ? {
        id: filing.creator.id,
        name: `${filing.creator.first_name} ${filing.creator.last_name}`,
        email: filing.creator.email
      } : null,
      createdAt: filing.created_at,
      updatedAt: filing.updated_at
    }));

    return res.status(200).json({
      taxFilings: formattedFilings
    });
  } catch (error) {
    console.error('Error getting tax filings:', error);
    return res.status(500).json({ error: 'Failed to get tax filings' });
  }
};

/**
 * Get a specific tax filing
 */
export const getTaxFiling = async (req, res) => {
  try {
    const { filingId } = req.params;

    // Find the tax filing
    const taxFiling = await TaxFiling.findByPk(filingId, {
      include: [
        {
          model: User,
          as: 'creator',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: TaxDocument,
          as: 'filingDocument',
          attributes: ['id', 'title', 'file_name', 'file_path', 'status']
        }
      ]
    });

    if (!taxFiling) {
      return res.status(404).json({ error: 'Tax filing not found' });
    }

    // Format the tax filing
    const formattedFiling = {
      id: taxFiling.id,
      farmId: taxFiling.farm_id,
      taxYear: taxFiling.tax_year,
      filingType: taxFiling.filing_type,
      formNumber: taxFiling.form_number,
      dueDate: taxFiling.due_date,
      filingDate: taxFiling.filing_date,
      extensionFiled: taxFiling.extension_filed,
      extensionDate: taxFiling.extension_date,
      status: taxFiling.status,
      filingMethod: taxFiling.filing_method,
      confirmationNumber: taxFiling.confirmation_number,
      totalTax: taxFiling.total_tax,
      totalPaid: taxFiling.total_paid,
      balanceDue: taxFiling.balance_due,
      refundAmount: taxFiling.refund_amount,
      notes: taxFiling.notes,
      filingDocument: taxFiling.filingDocument ? {
        id: taxFiling.filingDocument.id,
        title: taxFiling.filingDocument.title,
        fileName: taxFiling.filingDocument.file_name,
        status: taxFiling.filingDocument.status
      } : null,
      createdBy: taxFiling.creator ? {
        id: taxFiling.creator.id,
        name: `${taxFiling.creator.first_name} ${taxFiling.creator.last_name}`,
        email: taxFiling.creator.email
      } : null,
      createdAt: taxFiling.created_at,
      updatedAt: taxFiling.updated_at
    };

    return res.status(200).json({
      taxFiling: formattedFiling
    });
  } catch (error) {
    console.error('Error getting tax filing:', error);
    return res.status(500).json({ error: 'Failed to get tax filing' });
  }
};

/**
 * Create a new tax filing
 */
export const createTaxFiling = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { 
      taxYear = new Date().getFullYear(),
      filingType,
      formNumber,
      dueDate,
      filingDate,
      extensionFiled = false,
      extensionDate,
      status = 'not_started',
      filingMethod,
      confirmationNumber,
      totalTax,
      totalPaid,
      balanceDue,
      refundAmount,
      notes
    } = req.body;

    // Validate required fields
    if (!filingType || !dueDate) {
      return res.status(400).json({ 
        error: 'Filing type and due date are required' 
      });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Create the tax filing
    const taxFiling = await TaxFiling.create({
      farm_id: farmId,
      tax_year: taxYear,
      filing_type: filingType,
      form_number: formNumber,
      due_date: dueDate,
      filing_date: filingDate,
      extension_filed: extensionFiled,
      extension_date: extensionDate,
      status,
      filing_method: filingMethod,
      confirmation_number: confirmationNumber,
      total_tax: totalTax,
      total_paid: totalPaid,
      balance_due: balanceDue,
      refund_amount: refundAmount,
      notes,
      created_by: req.user.id
    });

    return res.status(201).json({
      taxFiling: {
        id: taxFiling.id,
        farmId: taxFiling.farm_id,
        taxYear: taxFiling.tax_year,
        filingType: taxFiling.filing_type,
        formNumber: taxFiling.form_number,
        dueDate: taxFiling.due_date,
        filingDate: taxFiling.filing_date,
        extensionFiled: taxFiling.extension_filed,
        extensionDate: taxFiling.extension_date,
        status: taxFiling.status,
        filingMethod: taxFiling.filing_method,
        confirmationNumber: taxFiling.confirmation_number,
        totalTax: taxFiling.total_tax,
        totalPaid: taxFiling.total_paid,
        balanceDue: taxFiling.balance_due,
        refundAmount: taxFiling.refund_amount,
        notes: taxFiling.notes,
        createdBy: req.user.id,
        createdAt: taxFiling.created_at,
        updatedAt: taxFiling.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating tax filing:', error);
    return res.status(500).json({ error: 'Failed to create tax filing' });
  }
};

/**
 * Update a tax filing
 */
export const updateTaxFiling = async (req, res) => {
  try {
    const { filingId } = req.params;
    const { 
      taxYear,
      filingType,
      formNumber,
      dueDate,
      filingDate,
      extensionFiled,
      extensionDate,
      status,
      filingMethod,
      confirmationNumber,
      totalTax,
      totalPaid,
      balanceDue,
      refundAmount,
      notes,
      documentId
    } = req.body;

    // Find the tax filing
    const taxFiling = await TaxFiling.findByPk(filingId);
    if (!taxFiling) {
      return res.status(404).json({ error: 'Tax filing not found' });
    }

    // Update the tax filing
    if (taxYear) taxFiling.tax_year = taxYear;
    if (filingType) taxFiling.filing_type = filingType;
    if (formNumber !== undefined) taxFiling.form_number = formNumber;
    if (dueDate) taxFiling.due_date = dueDate;
    if (filingDate !== undefined) taxFiling.filing_date = filingDate;
    if (extensionFiled !== undefined) taxFiling.extension_filed = extensionFiled;
    if (extensionDate !== undefined) taxFiling.extension_date = extensionDate;
    if (status) taxFiling.status = status;
    if (filingMethod !== undefined) taxFiling.filing_method = filingMethod;
    if (confirmationNumber !== undefined) taxFiling.confirmation_number = confirmationNumber;
    if (totalTax !== undefined) taxFiling.total_tax = totalTax;
    if (totalPaid !== undefined) taxFiling.total_paid = totalPaid;
    if (balanceDue !== undefined) taxFiling.balance_due = balanceDue;
    if (refundAmount !== undefined) taxFiling.refund_amount = refundAmount;
    if (notes !== undefined) taxFiling.notes = notes;
    if (documentId !== undefined) taxFiling.document_id = documentId;

    await taxFiling.save();

    return res.status(200).json({
      taxFiling: {
        id: taxFiling.id,
        farmId: taxFiling.farm_id,
        taxYear: taxFiling.tax_year,
        filingType: taxFiling.filing_type,
        formNumber: taxFiling.form_number,
        dueDate: taxFiling.due_date,
        filingDate: taxFiling.filing_date,
        extensionFiled: taxFiling.extension_filed,
        extensionDate: taxFiling.extension_date,
        status: taxFiling.status,
        filingMethod: taxFiling.filing_method,
        confirmationNumber: taxFiling.confirmation_number,
        totalTax: taxFiling.total_tax,
        totalPaid: taxFiling.total_paid,
        balanceDue: taxFiling.balance_due,
        refundAmount: taxFiling.refund_amount,
        notes: taxFiling.notes,
        documentId: taxFiling.document_id,
        createdBy: taxFiling.created_by,
        createdAt: taxFiling.created_at,
        updatedAt: taxFiling.updated_at
      }
    });
  } catch (error) {
    console.error('Error updating tax filing:', error);
    return res.status(500).json({ error: 'Failed to update tax filing' });
  }
};

/**
 * Delete a tax filing
 */
export const deleteTaxFiling = async (req, res) => {
  try {
    const { filingId } = req.params;

    // Find the tax filing
    const taxFiling = await TaxFiling.findByPk(filingId);
    if (!taxFiling) {
      return res.status(404).json({ error: 'Tax filing not found' });
    }

    // If there's a document associated, don't delete the filing
    if (taxFiling.document_id) {
      return res.status(400).json({ 
        error: 'Cannot delete filing with an associated document. Delete the document first.' 
      });
    }

    // Delete the tax filing
    await taxFiling.destroy();

    return res.status(200).json({
      message: 'Tax filing deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tax filing:', error);
    return res.status(500).json({ error: 'Failed to delete tax filing' });
  }
};

/**
 * Forecast annual tax liability
 */
export const forecastTaxLiability = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear() } = req.query;

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Get current year transactions
    const currentYearTransactions = await Transaction.findAll({
      where: {
        farm_id: farmId,
        transaction_date: {
          [Op.between]: [
            new Date(`${year}-01-01`),
            new Date()
          ]
        }
      }
    });

    // Calculate current income and expenses
    let currentIncome = 0;
    let currentExpenses = 0;

    currentYearTransactions.forEach(transaction => {
      if (transaction.amount > 0) {
        currentIncome += transaction.amount;
      } else {
        currentExpenses += Math.abs(transaction.amount);
      }
    });

    // Get current year tax deductions
    const currentYearDeductions = await TaxDeduction.findAll({
      where: {
        farm_id: farmId,
        tax_year: year
      }
    });

    // Calculate current deductions
    const currentDeductions = currentYearDeductions.reduce((sum, deduction) => {
      return sum + (deduction.status === 'approved' ? deduction.amount : 0);
    }, 0);

    // Get previous year's data for the same period
    const currentDate = new Date();
    const currentMonth = currentDate.getMonth();
    const currentDay = currentDate.getDate();

    const previousYear = parseInt(year) - 1;
    const previousYearTransactions = await Transaction.findAll({
      where: {
        farm_id: farmId,
        transaction_date: {
          [Op.between]: [
            new Date(`${previousYear}-01-01`),
            new Date(`${previousYear}-${currentMonth + 1}-${currentDay}`)
          ]
        }
      }
    });

    // Calculate previous year income and expenses for the same period
    let previousIncome = 0;
    let previousExpenses = 0;

    previousYearTransactions.forEach(transaction => {
      if (transaction.amount > 0) {
        previousIncome += transaction.amount;
      } else {
        previousExpenses += Math.abs(transaction.amount);
      }
    });

    // Get previous year's total data
    const previousYearTotalTransactions = await Transaction.findAll({
      where: {
        farm_id: farmId,
        transaction_date: {
          [Op.between]: [
            new Date(`${previousYear}-01-01`),
            new Date(`${previousYear}-12-31`)
          ]
        }
      }
    });

    // Calculate previous year total income and expenses
    let previousYearTotalIncome = 0;
    let previousYearTotalExpenses = 0;

    previousYearTotalTransactions.forEach(transaction => {
      if (transaction.amount > 0) {
        previousYearTotalIncome += transaction.amount;
      } else {
        previousYearTotalExpenses += Math.abs(transaction.amount);
      }
    });

    // Get previous year tax deductions
    const previousYearDeductions = await TaxDeduction.findAll({
      where: {
        farm_id: farmId,
        tax_year: previousYear
      }
    });

    // Calculate previous year deductions
    const previousYearTotalDeductions = previousYearDeductions.reduce((sum, deduction) => {
      return sum + (deduction.status === 'approved' ? deduction.amount : 0);
    }, 0);

    // Calculate growth rates if previous data exists
    let incomeGrowthRate = 1;
    let expenseGrowthRate = 1;
    let deductionGrowthRate = 1;

    if (previousIncome > 0) {
      incomeGrowthRate = currentIncome / previousIncome;
    }

    if (previousExpenses > 0) {
      expenseGrowthRate = currentExpenses / previousExpenses;
    }

    if (previousYearTotalDeductions > 0) {
      deductionGrowthRate = currentDeductions / previousYearTotalDeductions;
    }

    // Forecast annual income, expenses, and deductions
    let forecastedAnnualIncome = 0;
    let forecastedAnnualExpenses = 0;
    let forecastedAnnualDeductions = 0;

    if (previousYearTotalIncome > 0 && previousIncome > 0) {
      // Use growth rate to forecast annual income
      forecastedAnnualIncome = previousYearTotalIncome * incomeGrowthRate;
    } else {
      // If no previous year data, extrapolate from current year
      const dayOfYear = Math.floor((currentDate - new Date(`${year}-01-01`)) / (1000 * 60 * 60 * 24));
      forecastedAnnualIncome = (currentIncome / dayOfYear) * 365;
    }

    if (previousYearTotalExpenses > 0 && previousExpenses > 0) {
      // Use growth rate to forecast annual expenses
      forecastedAnnualExpenses = previousYearTotalExpenses * expenseGrowthRate;
    } else {
      // If no previous year data, extrapolate from current year
      const dayOfYear = Math.floor((currentDate - new Date(`${year}-01-01`)) / (1000 * 60 * 60 * 24));
      forecastedAnnualExpenses = (currentExpenses / dayOfYear) * 365;
    }

    if (previousYearTotalDeductions > 0) {
      // Use growth rate to forecast annual deductions
      forecastedAnnualDeductions = previousYearTotalDeductions * deductionGrowthRate;
    } else {
      // If no previous year data, use current deductions
      forecastedAnnualDeductions = currentDeductions;
    }

    // Calculate forecasted taxable income
    const forecastedTaxableIncome = Math.max(0, forecastedAnnualIncome - forecastedAnnualExpenses - forecastedAnnualDeductions);

    // Calculate forecasted tax liability (simplified progressive tax calculation)
    let forecastedTaxLiability = 0;
    if (forecastedTaxableIncome > 0) {
      if (forecastedTaxableIncome <= 50000) {
        forecastedTaxLiability = forecastedTaxableIncome * 0.15;
      } else if (forecastedTaxableIncome <= 100000) {
        forecastedTaxLiability = 7500 + (forecastedTaxableIncome - 50000) * 0.25;
      } else {
        forecastedTaxLiability = 20000 + (forecastedTaxableIncome - 100000) * 0.35;
      }
    }

    // Get tax payments made so far this year
    const taxPayments = await TaxPayment.findAll({
      where: {
        farm_id: farmId,
        payment_date: {
          [Op.between]: [
            new Date(`${year}-01-01`),
            new Date()
          ]
        }
      }
    });

    // Calculate total tax payments made
    const totalTaxPayments = taxPayments.reduce((sum, payment) => {
      return sum + payment.amount;
    }, 0);

    // Calculate remaining tax liability
    const remainingTaxLiability = Math.max(0, forecastedTaxLiability - totalTaxPayments);

    // Calculate quarterly estimated payments
    const quarterlyPayment = remainingTaxLiability / 4;

    // Determine which quarters are remaining
    const currentQuarter = Math.floor(currentMonth / 3) + 1;
    const remainingQuarters = [];

    for (let i = currentQuarter; i <= 4; i++) {
      remainingQuarters.push(i);
    }

    // Return the forecast
    return res.status(200).json({
      forecast: {
        year: parseInt(year),
        currentData: {
          income: currentIncome,
          expenses: currentExpenses,
          deductions: currentDeductions,
          taxPayments: totalTaxPayments
        },
        annualForecast: {
          income: forecastedAnnualIncome,
          expenses: forecastedAnnualExpenses,
          deductions: forecastedAnnualDeductions,
          taxableIncome: forecastedTaxableIncome,
          taxLiability: forecastedTaxLiability,
          remainingTaxLiability: remainingTaxLiability
        },
        quarterlyEstimates: {
          remainingQuarters,
          paymentPerQuarter: quarterlyPayment
        },
        previousYearData: {
          income: previousYearTotalIncome,
          expenses: previousYearTotalExpenses,
          deductions: previousYearTotalDeductions
        }
      }
    });
  } catch (error) {
    console.error('Error forecasting tax liability:', error);
    return res.status(500).json({ error: 'Failed to forecast tax liability' });
  }
};

/**
 * Calculate payroll taxes for employees in a farm
 */
export const calculatePayrollTaxes = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { year = new Date().getFullYear(), quarter, month, employeeId } = req.query;
    const currentYear = new Date().getFullYear();

    // Validate year is not in the future
    if (parseInt(year) > currentYear) {
      return res.status(400).json({ 
        error: `Cannot calculate payroll taxes for future year ${year}. Current year is ${currentYear}.` 
      });
    }

    // Validate farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Build date range filter
    let startDate, endDate;
    if (quarter) {
      // Filter by quarter
      const quarterNum = parseInt(quarter);
      if (quarterNum < 1 || quarterNum > 4) {
        return res.status(400).json({ error: 'Invalid quarter. Must be 1-4' });
      }
      startDate = new Date(parseInt(year), (quarterNum - 1) * 3, 1);
      endDate = new Date(parseInt(year), quarterNum * 3, 0, 23, 59, 59, 999);
    } else if (month) {
      // Filter by month
      const monthNum = parseInt(month);
      if (monthNum < 1 || monthNum > 12) {
        return res.status(400).json({ error: 'Invalid month. Must be 1-12' });
      }
      startDate = new Date(parseInt(year), monthNum - 1, 1);
      endDate = new Date(parseInt(year), monthNum, 0, 23, 59, 59, 999);
    } else {
      // Filter by year
      startDate = new Date(parseInt(year), 0, 1);
      endDate = new Date(parseInt(year), 11, 31, 23, 59, 59, 999);
    }

    // Build employee filter
    const employeeFilter = employeeId ? { id: employeeId } : {};

    // Get regular employees for the farm
    const employees = await Employee.findAll({
      where: {
        farm_id: farmId,
        ...employeeFilter
      },
      include: [
        {
          model: EmployeeTaxInfo,
          as: 'taxInfo',
          where: { tax_year: year },
          required: false
        }
      ]
    });

    // Get farm owners, admins, and accountants for the farm
    let farmUsers = [];
    if (!employeeId) { // Only include additional users if not filtering by specific employee ID
      const userFarms = await UserFarm.findAll({
        where: {
          farm_id: farmId,
          role: {
            [Op.in]: ['farm_owner', 'farm_admin', 'accountant']
          },
          is_approved: true
        },
        include: [
          {
            model: User,
            attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number']
          }
        ]
      });

      // Create virtual "employee" objects for farm owners, admins, and accountants
      farmUsers = userFarms.map(userFarm => {
        // Create a virtual employee object with the necessary properties
        const virtualEmployee = {
          id: userFarm.user_id,
          farm_id: farmId,
          first_name: userFarm.User.first_name,
          last_name: userFarm.User.last_name,
          role: userFarm.role,
          taxInfo: [], // Empty array since they don't have employee tax info
          is_system_user: true // Flag to identify these as system users, not regular employees
        };
        return virtualEmployee;
      });
    }

    // Combine regular employees with farm owners, admins, and accountants
    const allEmployees = [...employees, ...farmUsers];

    // If no employees found, try again without filtering tax info by year
    if (allEmployees.length === 0) {
      const employeesWithoutTaxFilter = await Employee.findAll({
        where: {
          farm_id: farmId,
          ...employeeFilter
        },
        include: [
          {
            model: EmployeeTaxInfo,
            as: 'taxInfo',
            required: false
          }
        ]
      });

      // Get farm owners, admins, and accountants for the farm (without tax filter)
      let farmUsersWithoutTaxFilter = [];
      if (!employeeId) {
        const userFarms = await UserFarm.findAll({
          where: {
            farm_id: farmId,
            role: {
              [Op.in]: ['farm_owner', 'farm_admin', 'accountant']
            },
            is_approved: true
          },
          include: [
            {
              model: User,
              attributes: ['id', 'first_name', 'last_name', 'email', 'phone_number']
            }
          ]
        });

        farmUsersWithoutTaxFilter = userFarms.map(userFarm => {
          const virtualEmployee = {
            id: userFarm.user_id,
            farm_id: farmId,
            first_name: userFarm.User.first_name,
            last_name: userFarm.User.last_name,
            role: userFarm.role,
            taxInfo: [],
            is_system_user: true
          };
          return virtualEmployee;
        });
      }

      const allEmployeesWithoutTaxFilter = [...employeesWithoutTaxFilter, ...farmUsersWithoutTaxFilter];

      if (allEmployeesWithoutTaxFilter.length > 0) {
        return res.status(200).json({
          period: {
            year: parseInt(year),
            quarter: quarter ? parseInt(quarter) : null,
            month: month ? parseInt(month) : null,
            startDate,
            endDate
          },
          employees: allEmployeesWithoutTaxFilter.map(employee => ({
            employee: {
              id: employee.id,
              name: `${employee.first_name} ${employee.last_name}`,
              position: employee.role
            },
            taxInfo: null,
            payPeriods: 0,
            totalGrossPay: 0,
            totalNetPay: 0,
            federalIncomeTax: 0,
            socialSecurityTax: 0,
            medicareTax: 0,
            stateTax: 0,
            totalTaxes: 0,
            effectiveTaxRate: 0
          })),
          farmTotals: {
            totalGrossPay: 0,
            totalNetPay: 0,
            federalIncomeTax: 0,
            socialSecurityTax: 0,
            medicareTax: 0,
            stateTax: 0,
            totalTaxes: 0,
            effectiveTaxRate: 0
          }
        });
      }
    }

    if (allEmployees.length === 0) {
      return res.status(404).json({ error: 'No employees found for the specified criteria' });
    }

    // Calculate payroll taxes for each employee
    const payrollTaxResults = await Promise.all(allEmployees.map(async (employee) => {
      // For system users (farm owners, admins, accountants), return default values
      if (employee.is_system_user) {
        return {
          employee: {
            id: employee.id,
            name: `${employee.first_name} ${employee.last_name}`,
            position: employee.role
          },
          taxInfo: null,
          payPeriods: 0,
          totalGrossPay: 0,
          totalNetPay: 0,
          federalIncomeTax: 0,
          socialSecurityTax: 0,
          medicareTax: 0,
          stateTax: 0,
          totalTaxes: 0,
          effectiveTaxRate: 0
        };
      }

      // For regular employees, get pay stubs and calculate taxes
      const payStubs = await PayStub.findAll({
        where: {
          employee_id: employee.id,
          payment_date: {
            [Op.between]: [startDate, endDate]
          }
        },
        order: [['payment_date', 'ASC']]
      });

      if (payStubs.length === 0) {
        return {
          employee: {
            id: employee.id,
            name: `${employee.first_name} ${employee.last_name}`,
            position: employee.role
          },
          taxInfo: employee.taxInfo && employee.taxInfo.length > 0 ? employee.taxInfo[0] : null,
          payPeriods: 0,
          totalGrossPay: 0,
          totalNetPay: 0,
          federalIncomeTax: 0,
          socialSecurityTax: 0,
          medicareTax: 0,
          stateTax: 0,
          totalTaxes: 0,
          effectiveTaxRate: 0
        };
      }

      // Calculate total gross pay and taxes
      let totalGrossPay = 0;
      let totalNetPay = 0;
      let federalIncomeTax = 0;
      let socialSecurityTax = 0;
      let medicareTax = 0;
      let stateTax = 0;
      let totalTaxes = 0;

      // Process each pay stub
      payStubs.forEach(payStub => {
        totalGrossPay += parseFloat(payStub.gross_pay) || 0;
        totalNetPay += parseFloat(payStub.net_pay) || 0;

        // Extract tax details from the taxes JSON field
        if (payStub.taxes) {
          const taxes = typeof payStub.taxes === 'string' 
            ? JSON.parse(payStub.taxes) 
            : payStub.taxes;

          if (taxes && typeof taxes === 'object') {
            federalIncomeTax += parseFloat(taxes.federal || 0);
            socialSecurityTax += parseFloat(taxes.socialSecurity || 0);
            medicareTax += parseFloat(taxes.medicare || 0);
            stateTax += parseFloat(taxes.state || 0);
          }
        }
      });

      // Calculate total taxes and effective tax rate
      totalTaxes = federalIncomeTax + socialSecurityTax + medicareTax + stateTax;
      const effectiveTaxRate = totalGrossPay > 0 ? (totalTaxes / totalGrossPay) * 100 : 0;

      // Return employee payroll tax summary
      return {
        employee: {
          id: employee.id,
          name: `${employee.first_name} ${employee.last_name}`,
          position: employee.role
        },
        taxInfo: employee.taxInfo && employee.taxInfo.length > 0 ? employee.taxInfo[0] : null,
        payPeriods: payStubs.length,
        totalGrossPay,
        totalNetPay,
        federalIncomeTax,
        socialSecurityTax,
        medicareTax,
        stateTax,
        totalTaxes,
        effectiveTaxRate: parseFloat(effectiveTaxRate.toFixed(2))
      };
    }));

    // Calculate farm-wide totals
    const farmTotals = payrollTaxResults.reduce((totals, employee) => {
      totals.totalGrossPay += employee.totalGrossPay;
      totals.totalNetPay += employee.totalNetPay;
      totals.federalIncomeTax += employee.federalIncomeTax;
      totals.socialSecurityTax += employee.socialSecurityTax;
      totals.medicareTax += employee.medicareTax;
      totals.stateTax += employee.stateTax;
      totals.totalTaxes += employee.totalTaxes;
      return totals;
    }, {
      totalGrossPay: 0,
      totalNetPay: 0,
      federalIncomeTax: 0,
      socialSecurityTax: 0,
      medicareTax: 0,
      stateTax: 0,
      totalTaxes: 0
    });

    farmTotals.effectiveTaxRate = farmTotals.totalGrossPay > 0 
      ? parseFloat(((farmTotals.totalTaxes / farmTotals.totalGrossPay) * 100).toFixed(2)) 
      : 0;

    // Return the payroll tax calculation results
    return res.status(200).json({
      period: {
        year: parseInt(year),
        quarter: quarter ? parseInt(quarter) : null,
        month: month ? parseInt(month) : null,
        startDate,
        endDate
      },
      employees: payrollTaxResults,
      farmTotals
    });
  } catch (error) {
    console.error('Error calculating payroll taxes:', error);
    return res.status(500).json({ error: 'Failed to calculate payroll taxes' });
  }
};
