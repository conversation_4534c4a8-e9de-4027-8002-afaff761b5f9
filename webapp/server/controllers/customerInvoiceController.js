import Invoice from '../models/Invoice.js';
import InvoiceItem from '../models/InvoiceItem.js';
import InvoiceQuestion from '../models/InvoiceQuestion.js';
import Customer from '../models/Customer.js';
import Farm from '../models/Farm.js';
import Transaction from '../models/Transaction.js';
import CustomerNotification from '../models/CustomerNotification.js';
import Alert from '../models/Alert.js';
import { sequelize, Op } from '../config/database.js';
import { stripe } from '../config/stripe.js';

// Get all invoices for a customer
export const getCustomerInvoices = async (req, res) => {
  try {
    // The customer is attached to the request by the authenticateCustomer middleware
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get all invoices for the customer, excluding draft and cancelled invoices
    const invoices = await Invoice.findAll({
      where: { 
        farm_id: farmId,
        customer_id: customer.id,
        status: {
          [Op.notIn]: ['draft', 'cancelled']
        }
      },
      order: [['issue_date', 'DESC']]
    });

    return res.status(200).json({ invoices });
  } catch (error) {
    console.error('Error getting customer invoices:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get a single invoice by ID for a customer
export const getCustomerInvoiceById = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the invoice, ensuring it belongs to this customer
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId,
        farm_id: farmId,
        customer_id: customer.id
      },
      include: [
        {
          model: InvoiceItem,
          required: false,
          attributes: ['id', 'description', 'quantity', 'unit_price', 'amount']
        }
      ]
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if the invoice is in draft or cancelled status
    if (invoice.status.toLowerCase() === 'draft' || invoice.status.toLowerCase() === 'cancelled') {
      return res.status(403).json({ 
        error: `You cannot view invoices in ${invoice.status} status until they are sent to you.`
      });
    }

    return res.status(200).json({ invoice });
  } catch (error) {
    console.error('Error getting customer invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Create a payment intent for Stripe
export const createPaymentIntent = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { paymentMethodType = 'card' } = req.body;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the invoice, ensuring it belongs to this customer
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId,
        farm_id: farmId,
        customer_id: customer.id
      }
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if invoice is already paid
    if (invoice.status === 'paid') {
      return res.status(400).json({ error: 'Invoice is already paid' });
    }

    // Calculate the amount to charge
    let amount = Math.round(invoice.total_amount * 100); // Convert to cents for Stripe

    // Check if customer pays Stripe fees
    const applicationFeeAmount = farm.customer_pays_stripe_fees ? calculateStripeFee(amount, paymentMethodType) : 0;

    // Create a payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount,
      currency: 'usd',
      customer: farm.stripe_customer_id,
      payment_method_types: [paymentMethodType],
      metadata: {
        invoice_id: invoice.id,
        farm_id: farmId,
        customer_id: customer.id
      },
      application_fee_amount: applicationFeeAmount,
      description: `Payment for invoice #${invoice.invoice_number}`
    });

    // Return the client secret to the frontend
    return res.status(200).json({
      clientSecret: paymentIntent.client_secret,
      amount,
      applicationFeeAmount,
      paymentIntent: paymentIntent.id,
      customerPaysStripeFees: farm.customer_pays_stripe_fees,
      stripeFeeMessage: farm.customer_portal_stripe_fee_message
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Pay an invoice with Stripe
export const payInvoice = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { paymentIntentId, paymentMethodId } = req.body;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the invoice, ensuring it belongs to this customer
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId,
        farm_id: farmId,
        customer_id: customer.id
      }
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Check if invoice is already paid
    if (invoice.status === 'paid') {
      return res.status(400).json({ error: 'Invoice is already paid' });
    }

    // Verify the payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== 'succeeded') {
      return res.status(400).json({ error: 'Payment has not been completed' });
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // Create a transaction record
      const paymentTransaction = await Transaction.create({
        farm_id: farmId,
        transaction_date: new Date(),
        description: `Payment for invoice #${invoice.invoice_number}`,
        amount: invoice.total_amount,
        transaction_type: 'payment',
        category: 'invoice_payment',
        stripe_transaction_id: paymentIntentId
      }, { transaction });

      // Update the invoice
      await invoice.update({
        status: 'paid',
        payment_transaction_id: paymentTransaction.id,
        payment_method: paymentMethodId ? 'card' : 'ach',
        payment_date: new Date(),
        payment_amount: invoice.total_amount,
        stripe_payment_intent_id: paymentIntentId,
        stripe_payment_method_id: paymentMethodId
      }, { transaction });

      // Commit the transaction
      await transaction.commit();

      return res.status(200).json({
        message: 'Invoice paid successfully',
        invoice,
        transaction: paymentTransaction
      });
    } catch (error) {
      // Rollback the transaction in case of error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error paying invoice:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all questions for an invoice
export const getInvoiceQuestions = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const customer = req.customer;
    const farmId = req.farmId;

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the invoice, ensuring it belongs to this customer
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId,
        farm_id: farmId,
        customer_id: customer.id
      }
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Get all questions for this invoice
    const questions = await InvoiceQuestion.findAll({
      where: {
        invoice_id: invoiceId,
        customer_id: customer.id
      },
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({ questions });
  } catch (error) {
    console.error('Error getting invoice questions:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Add a question to an invoice
export const addInvoiceQuestion = async (req, res) => {
  try {
    const { invoiceId } = req.params;
    const { question } = req.body;
    const customer = req.customer;
    const farmId = req.farmId;

    // Validate input
    if (!question) {
      return res.status(400).json({ error: 'Question is required' });
    }

    // Find farm to ensure it exists and has customer portal enabled
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({ error: 'Farm not found' });
    }

    // Check if customer portal is enabled for this farm
    if (!farm.customer_portal_enabled) {
      return res.status(403).json({ error: 'Customer portal is not enabled for this farm' });
    }

    // Get the invoice, ensuring it belongs to this customer
    const invoice = await Invoice.findOne({
      where: {
        id: invoiceId,
        farm_id: farmId,
        customer_id: customer.id
      }
    });

    if (!invoice) {
      return res.status(404).json({ error: 'Invoice not found' });
    }

    // Create a new question
    const newQuestion = await InvoiceQuestion.create({
      invoice_id: invoiceId,
      customer_id: customer.id,
      farm_id: farmId,
      question
    });

    // Get invoice details for the notification
    const invoiceDetails = await Invoice.findByPk(invoiceId, {
      attributes: ['invoice_number']
    });

    // Create a notification for the farm (this will be shown to farm users)
    await Alert.create({
      title: 'New Invoice Question',
      message: `Customer ${customer.name} has asked a question about invoice #${invoiceDetails.invoice_number}`,
      type: 'info',
      source: 'system',
      farm_id: farmId,
      user_id: null, // Farm-wide notification
      related_entity_type: 'invoice_question',
      related_entity_id: newQuestion.id,
      action_url: `/invoices/${invoiceId}?tab=questions`
    });

    return res.status(201).json({
      message: 'Question added successfully',
      question: newQuestion
    });
  } catch (error) {
    console.error('Error adding invoice question:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to calculate Stripe fee
const calculateStripeFee = (amount, paymentMethodType) => {
  // Stripe fee is 2.9% + $0.30 for card payments
  // For ACH payments, it's 0.8% capped at $5
  if (paymentMethodType === 'ach_debit') {
    const fee = Math.round(amount * 0.008);
    return Math.min(fee, 500); // Cap at $5.00 (500 cents)
  } else {
    // Default to card payment fee
    return Math.round(amount * 0.029 + 30);
  }
};
