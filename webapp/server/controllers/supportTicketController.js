import SupportTicket from '../models/SupportTicket.js';
import SupportTicketComment from '../models/SupportTicketComment.js';
import SupportTicketAttachment from '../models/SupportTicketAttachment.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import UserFarm from '../models/UserFarm.js';
import RolePermission from '../models/RolePermission.js';
import { sequelize } from '../config/database.js';
import { Op } from 'sequelize';
import { sendTicketCommentEmail, sendTicketCreatedEmail, getFrontendUrl } from '../utils/emailUtils.js';
import fs from 'fs';
import path from 'path';

/**
 * Helper function to check if a user has permission to view support tickets for a farm
 * @param {string} userId - The user ID
 * @param {string} farmId - The farm ID
 * @returns {boolean} - Whether the user has permission to view support tickets
 */
const checkUserHasSupportPermission = async (userId, farmId) => {
  try {
    // Check if user has access to the farm
    const userFarm = await UserFarm.findOne({
      where: {
        user_id: userId,
        farm_id: farmId
      }
    });

    if (!userFarm) {
      return false;
    }

    // Check if user has custom permissions in the UserFarm model
    if (userFarm.permissions && 
        userFarm.permissions.support && 
        userFarm.permissions.support.view !== undefined) {
      return userFarm.permissions.support.view;
    }

    // If not, check the RolePermission model for default permissions for this role
    const rolePermission = await RolePermission.findOne({
      where: {
        farm_id: farmId,
        role_id: userFarm.role_id,
        feature: 'support'
      }
    });

    if (!rolePermission) {
      return false;
    }

    return rolePermission.can_view;
  } catch (error) {
    console.error('Error checking support permission:', error);
    return false;
  }
};

// Create a new support ticket
export const createSupportTicket = async (req, res) => {
  try {
    const { 
      subject, 
      description, 
      priority = 'medium', 
      category,
      farmId,
      emailNotificationsEnabled = true
    } = req.body;

    // Validate required fields
    if (!subject || !description) {
      return res.status(400).json({ error: 'Subject and description are required' });
    }

    // Validate farm_id if provided
    if (farmId) {
      const farm = await Farm.findByPk(farmId);
      if (!farm) {
        return res.status(400).json({ error: 'Invalid farm ID provided' });
      }
    }

    // Generate a unique email thread ID for the ticket
    const emailThreadId = `ticket-${Date.now()}-${Math.random().toString(36).substring(2, 10)}`;

    // Create the support ticket
    const ticket = await SupportTicket.create({
      subject,
      description,
      priority,
      category,
      user_id: req.user.id, // From auth middleware
      farm_id: farmId,
      status: 'open',
      email_thread_id: emailThreadId,
      email_notifications_enabled: emailNotificationsEnabled
    });

    // Get the creator details for the response
    const creator = await User.findByPk(req.user.id, {
      attributes: ['id', 'first_name', 'last_name', 'email']
    });

    // Send initial email notification if email notifications are enabled
    if (emailNotificationsEnabled && creator) {
      try {
        const frontendUrl = await getFrontendUrl(creator.id);
        await sendTicketCreatedEmail(ticket, creator, frontendUrl);
        console.log(`Initial email notification sent to ${creator.email} for ticket ${ticket.id}`);
      } catch (emailError) {
        console.error('Error sending initial email notification:', emailError);
        // Continue even if email sending fails
      }
    }

    return res.status(201).json({
      ticket: {
        id: ticket.id,
        subject: ticket.subject,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        userId: ticket.user_id,
        farmId: ticket.farm_id,
        assignedTo: ticket.assigned_to,
        emailThreadId: ticket.email_thread_id,
        emailNotificationsEnabled: ticket.email_notifications_enabled,
        creator: creator ? {
          id: creator.id,
          firstName: creator.first_name,
          lastName: creator.last_name,
          email: creator.email
        } : null,
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at
      }
    });
  } catch (error) {
    console.error('Error creating support ticket:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get all support tickets with optional filtering
export const getSupportTickets = async (req, res) => {
  try {
    const { 
      status, 
      priority, 
      category, 
      userId, 
      farmId,
      assignedTo,
      search,
      limit = 100,
      offset = 0
    } = req.query;

    // Build the where clause based on query parameters
    const whereClause = {};

    if (status) {
      whereClause.status = status;
    }

    if (priority) {
      whereClause.priority = priority;
    }

    if (category) {
      whereClause.category = category;
    }

    if (userId) {
      whereClause.user_id = userId;
    }

    // Handle farm access permissions
    if (farmId) {
      // If a specific farm is requested, check if user has access to it
      if (!req.user.is_global_admin) {
        const userFarm = await UserFarm.findOne({
          where: {
            user_id: req.user.id,
            farm_id: farmId
          }
        });

        if (!userFarm) {
          return res.status(403).json({ error: 'You do not have access to this farm' });
        }

        // Check if user has permission to view support tickets for this farm
        const hasPermission = await checkUserHasSupportPermission(req.user.id, farmId);
        if (!hasPermission) {
          return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
        }
      }
      whereClause.farm_id = farmId;
    } else {
      // If no specific farm is requested, only show tickets from farms the user has access to
      if (!req.user.is_global_admin) {
        // Get all farms the user has access to
        const userFarms = await UserFarm.findAll({
          where: {
            user_id: req.user.id
          },
          attributes: ['farm_id']
        });

        if (userFarms.length === 0) {
          // User doesn't have access to any farms
          return res.status(200).json({ tickets: [], totalCount: 0 });
        }

        // Filter farms based on support ticket view permission
        const farmIdsWithPermission = [];
        for (const userFarm of userFarms) {
          const hasPermission = await checkUserHasSupportPermission(req.user.id, userFarm.farm_id);
          if (hasPermission) {
            farmIdsWithPermission.push(userFarm.farm_id);
          }
        }

        if (farmIdsWithPermission.length === 0) {
          // User doesn't have permission to view support tickets for any farms
          return res.status(200).json({ tickets: [], totalCount: 0 });
        }

        // Use farm IDs with permission
        whereClause.farm_id = { [Op.in]: farmIdsWithPermission };
      }
    }

    if (assignedTo) {
      whereClause.assigned_to = assignedTo;
    }

    if (search) {
      whereClause[Op.or] = [
        { subject: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    // Get tickets with pagination
    const tickets = await SupportTicket.findAll({
      where: whereClause,
      include: [
        { model: User, as: 'creator', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: User, as: 'assignee', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: Farm, as: 'supportTicketFarm', attributes: ['id', 'name'] }
      ],
      limit: parseInt(limit),
      offset: parseInt(offset),
      order: [['created_at', 'DESC']]
    });

    // Get total count for pagination
    const totalCount = await SupportTicket.count({ where: whereClause });

    return res.status(200).json({
      tickets: tickets.map(ticket => ({
        id: ticket.id,
        subject: ticket.subject,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        creator: ticket.creator ? {
          id: ticket.creator.id,
          firstName: ticket.creator.first_name,
          lastName: ticket.creator.last_name,
          email: ticket.creator.email
        } : null,
        farm: ticket.supportTicketFarm ? {
          id: ticket.supportTicketFarm.id,
          name: ticket.supportTicketFarm.name
        } : null,
        assignee: ticket.assignee ? {
          id: ticket.assignee.id,
          firstName: ticket.assignee.first_name,
          lastName: ticket.assignee.last_name,
          email: ticket.assignee.email
        } : null,
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at,
        resolvedAt: ticket.resolved_at
      })),
      totalCount
    });
  } catch (error) {
    console.error('Error getting support tickets:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get support ticket by ID
export const getSupportTicketById = async (req, res) => {
  try {
    const { ticketId } = req.params;

    const ticket = await SupportTicket.findByPk(ticketId, {
      include: [
        { model: User, as: 'creator', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: User, as: 'assignee', attributes: ['id', 'first_name', 'last_name', 'email'] },
        { model: Farm, as: 'supportTicketFarm', attributes: ['id', 'name'] },
        { 
          model: SupportTicketComment, 
          as: 'comments',
          include: [
            { model: User, as: 'author', attributes: ['id', 'first_name', 'last_name', 'email'] }
          ],
          order: [['created_at', 'ASC']]
        },
        {
          model: SupportTicketAttachment,
          as: 'attachments',
          include: [
            { model: User, as: 'uploader', attributes: ['id', 'first_name', 'last_name', 'email'] }
          ],
          order: [['created_at', 'DESC']]
        }
      ]
    });

    if (!ticket) {
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    return res.status(200).json({
      ticket: {
        id: ticket.id,
        subject: ticket.subject,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        creator: ticket.creator ? {
          id: ticket.creator.id,
          firstName: ticket.creator.first_name,
          lastName: ticket.creator.last_name,
          email: ticket.creator.email
        } : null,
        farm: ticket.supportTicketFarm ? {
          id: ticket.supportTicketFarm.id,
          name: ticket.supportTicketFarm.name
        } : null,
        assignee: ticket.assignee ? {
          id: ticket.assignee.id,
          firstName: ticket.assignee.first_name,
          lastName: ticket.assignee.last_name,
          email: ticket.assignee.email
        } : null,
        comments: ticket.comments ? ticket.comments.map(comment => ({
          id: comment.id,
          content: comment.content,
          author: comment.author ? {
            id: comment.author.id,
            firstName: comment.author.first_name,
            lastName: comment.author.last_name,
            email: comment.author.email
          } : null,
          isInternal: comment.is_internal,
          createdAt: comment.created_at,
          updatedAt: comment.updated_at
        })) : [],
        attachments: ticket.attachments ? ticket.attachments.map(attachment => ({
          id: attachment.id,
          filename: attachment.original_filename,
          fileSize: attachment.file_size,
          fileType: attachment.file_type,
          uploader: attachment.uploader ? {
            id: attachment.uploader.id,
            firstName: attachment.uploader.first_name,
            lastName: attachment.uploader.last_name,
            email: attachment.uploader.email
          } : null,
          createdAt: attachment.created_at
        })) : [],
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at,
        resolvedAt: ticket.resolved_at
      }
    });
  } catch (error) {
    console.error('Error getting support ticket by ID:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Update support ticket
export const updateSupportTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { 
      subject, 
      description, 
      status, 
      priority, 
      category,
      assignedTo,
      farmId
    } = req.body;

    const ticket = await SupportTicket.findByPk(ticketId);
    if (!ticket) {
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Update ticket fields if provided
    if (subject !== undefined) ticket.subject = subject;
    if (description !== undefined) ticket.description = description;
    if (status !== undefined) {
      ticket.status = status;
      // If status is changed to resolved, set resolved_at timestamp
      if (status === 'resolved' && ticket.status !== 'resolved') {
        ticket.resolved_at = new Date();
      } else if (status !== 'resolved') {
        ticket.resolved_at = null;
      }
    }
    if (priority !== undefined) ticket.priority = priority;
    if (category !== undefined) ticket.category = category;
    if (assignedTo !== undefined) ticket.assigned_to = assignedTo;
    if (farmId !== undefined) {
      // Validate farm_id if provided
      if (farmId) {
        const farm = await Farm.findByPk(farmId);
        if (!farm) {
          return res.status(400).json({ error: 'Invalid farm ID provided' });
        }
      }
      ticket.farm_id = farmId;
    }

    await ticket.save();

    return res.status(200).json({
      ticket: {
        id: ticket.id,
        subject: ticket.subject,
        description: ticket.description,
        status: ticket.status,
        priority: ticket.priority,
        category: ticket.category,
        userId: ticket.user_id,
        farmId: ticket.farm_id,
        assignedTo: ticket.assigned_to,
        createdAt: ticket.created_at,
        updatedAt: ticket.updated_at,
        resolvedAt: ticket.resolved_at
      }
    });
  } catch (error) {
    console.error('Error updating support ticket:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete support ticket
export const deleteSupportTicket = async (req, res) => {
  try {
    const { ticketId } = req.params;

    const ticket = await SupportTicket.findByPk(ticketId);
    if (!ticket) {
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    await ticket.destroy();

    return res.status(200).json({
      message: 'Support ticket deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting support ticket:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Add comment to support ticket
export const addComment = async (req, res) => {
  try {
    const { ticketId } = req.params;
    const { content, isInternal = false } = req.body;

    // Validate required fields
    if (!content) {
      return res.status(400).json({ error: 'Comment content is required' });
    }

    // Check if ticket exists
    const ticket = await SupportTicket.findByPk(ticketId, {
      include: [
        { model: User, as: 'creator', attributes: ['id', 'first_name', 'last_name', 'email'] }
      ]
    });

    if (!ticket) {
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Create the comment
    const comment = await SupportTicketComment.create({
      content,
      ticket_id: ticketId,
      user_id: req.user.id, // From auth middleware
      is_internal: isInternal
    });

    // Get the author details
    const author = await User.findByPk(req.user.id, {
      attributes: ['id', 'first_name', 'last_name', 'email']
    });

    // Send email notification if the comment is not internal and email notifications are enabled
    if (!isInternal && ticket.email_notifications_enabled && ticket.creator) {
      try {
        // Always send email to the ticket creator for non-internal comments
        const frontendUrl = await getFrontendUrl(ticket.creator.id);
        await sendTicketCommentEmail(ticket, comment, author, ticket.creator, frontendUrl);
        console.log(`Email notification sent to ${ticket.creator.email} for ticket ${ticketId}`);
      } catch (emailError) {
        console.error('Error sending email notification:', emailError);
        // Continue even if email sending fails
      }
    }

    return res.status(201).json({
      comment: {
        id: comment.id,
        content: comment.content,
        ticketId: comment.ticket_id,
        author: {
          id: author.id,
          firstName: author.first_name,
          lastName: author.last_name,
          email: author.email
        },
        isInternal: comment.is_internal,
        createdAt: comment.created_at,
        updatedAt: comment.updated_at
      }
    });
  } catch (error) {
    console.error('Error adding comment to support ticket:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete comment from support ticket
export const deleteComment = async (req, res) => {
  try {
    const { commentId } = req.params;

    const comment = await SupportTicketComment.findByPk(commentId);
    if (!comment) {
      return res.status(404).json({ error: 'Comment not found' });
    }

    // Get the ticket associated with this comment to check farm permissions
    const ticket = await SupportTicket.findByPk(comment.ticket_id);
    if (!ticket) {
      return res.status(404).json({ error: 'Associated support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Check if user is authorized to delete the comment
    if (comment.user_id !== req.user.id && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Not authorized to delete this comment' });
    }

    await comment.destroy();

    return res.status(200).json({
      message: 'Comment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting comment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Upload file attachment to support ticket
export const uploadAttachment = async (req, res) => {
  try {
    const { ticketId } = req.params;

    // Check if file was uploaded
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Check if ticket exists
    const ticket = await SupportTicket.findByPk(ticketId);
    if (!ticket) {
      // Remove the uploaded file if ticket doesn't exist
      fs.unlinkSync(req.file.path);
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        // Remove the uploaded file if user doesn't have access
        fs.unlinkSync(req.file.path);
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        // Remove the uploaded file if user doesn't have permission
        fs.unlinkSync(req.file.path);
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Create the attachment record
    const attachment = await SupportTicketAttachment.create({
      ticket_id: ticketId,
      user_id: req.user.id,
      filename: req.file.filename,
      original_filename: req.file.originalname,
      file_path: req.file.path,
      file_size: req.file.size,
      file_type: req.file.mimetype
    });

    // Get the uploader details
    const uploader = await User.findByPk(req.user.id, {
      attributes: ['id', 'first_name', 'last_name', 'email']
    });

    return res.status(201).json({
      attachment: {
        id: attachment.id,
        ticketId: attachment.ticket_id,
        filename: attachment.original_filename,
        fileSize: attachment.file_size,
        fileType: attachment.file_type,
        uploader: uploader ? {
          id: uploader.id,
          firstName: uploader.first_name,
          lastName: uploader.last_name,
          email: uploader.email
        } : null,
        createdAt: attachment.created_at
      }
    });
  } catch (error) {
    console.error('Error uploading attachment:', error);
    // Remove the uploaded file if there was an error
    if (req.file && req.file.path) {
      try {
        fs.unlinkSync(req.file.path);
      } catch (unlinkError) {
        console.error('Error removing file after upload error:', unlinkError);
      }
    }
    return res.status(500).json({ error: error.message });
  }
};

// Get all attachments for a support ticket
export const getAttachments = async (req, res) => {
  try {
    const { ticketId } = req.params;

    // Check if ticket exists
    const ticket = await SupportTicket.findByPk(ticketId);
    if (!ticket) {
      return res.status(404).json({ error: 'Support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Get all attachments for the ticket
    const attachments = await SupportTicketAttachment.findAll({
      where: { ticket_id: ticketId },
      include: [
        { model: User, as: 'uploader', attributes: ['id', 'first_name', 'last_name', 'email'] }
      ],
      order: [['created_at', 'DESC']]
    });

    return res.status(200).json({
      attachments: attachments.map(attachment => ({
        id: attachment.id,
        ticketId: attachment.ticket_id,
        filename: attachment.original_filename,
        fileSize: attachment.file_size,
        fileType: attachment.file_type,
        uploader: attachment.uploader ? {
          id: attachment.uploader.id,
          firstName: attachment.uploader.first_name,
          lastName: attachment.uploader.last_name,
          email: attachment.uploader.email
        } : null,
        createdAt: attachment.created_at
      }))
    });
  } catch (error) {
    console.error('Error getting attachments:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Download an attachment
export const downloadAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;

    // Find the attachment
    const attachment = await SupportTicketAttachment.findByPk(attachmentId);
    if (!attachment) {
      return res.status(404).json({ error: 'Attachment not found' });
    }

    // Check if ticket exists
    const ticket = await SupportTicket.findByPk(attachment.ticket_id);
    if (!ticket) {
      return res.status(404).json({ error: 'Associated support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Check if file exists
    if (!fs.existsSync(attachment.file_path)) {
      return res.status(404).json({ error: 'File not found on server' });
    }

    // Send the file
    res.download(attachment.file_path, attachment.original_filename);
  } catch (error) {
    console.error('Error downloading attachment:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Delete an attachment
export const deleteAttachment = async (req, res) => {
  try {
    const { attachmentId } = req.params;

    // Find the attachment
    const attachment = await SupportTicketAttachment.findByPk(attachmentId);
    if (!attachment) {
      return res.status(404).json({ error: 'Attachment not found' });
    }

    // Check if ticket exists
    const ticket = await SupportTicket.findByPk(attachment.ticket_id);
    if (!ticket) {
      return res.status(404).json({ error: 'Associated support ticket not found' });
    }

    // Check if user has access to the farm associated with this ticket
    if (!req.user.is_global_admin && ticket.farm_id) {
      const userFarm = await UserFarm.findOne({
        where: {
          user_id: req.user.id,
          farm_id: ticket.farm_id
        }
      });

      if (!userFarm) {
        return res.status(403).json({ error: 'You do not have access to this support ticket' });
      }

      // Check if user has permission to view support tickets for this farm
      const hasPermission = await checkUserHasSupportPermission(req.user.id, ticket.farm_id);
      if (!hasPermission) {
        return res.status(403).json({ error: 'You do not have permission to view support tickets for this farm' });
      }
    }

    // Check if user is authorized to delete the attachment
    if (attachment.user_id !== req.user.id && !req.user.is_global_admin) {
      return res.status(403).json({ error: 'Not authorized to delete this attachment' });
    }

    // Delete the file from the filesystem
    if (fs.existsSync(attachment.file_path)) {
      fs.unlinkSync(attachment.file_path);
    }

    // Delete the attachment record
    await attachment.destroy();

    return res.status(200).json({
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting attachment:', error);
    return res.status(500).json({ error: error.message });
  }
};
