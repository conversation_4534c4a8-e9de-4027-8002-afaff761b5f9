import Password from '../models/Password.js';
import PasswordGroup from '../models/PasswordGroup.js';
import PasswordGroupPermission from '../models/PasswordGroupPermission.js';
import { Op } from 'sequelize';
import { sequelize } from '../config/database.js';
import * as passwordEncryption from '../utils/passwordEncryption.js';

/**
 * Controller for managing password entries
 */
class PasswordController {
  /**
   * Get all passwords in a group
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPasswords(req, res) {
    try {
      const { groupId } = req.params;
      const userId = req.user.id;
      const { masterPassword } = req.body;
      
      if (!masterPassword) {
        return res.status(400).json({
          success: false,
          message: 'Master password is required to decrypt passwords'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(groupId);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Get user's roles for this farm
      const userRoles = userFarm.map(uf => uf.role_id);
      
      // Check if user has permission to access this group
      const hasPermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: groupId,
          [Op.or]: [
            { user_id: userId },
            { role_id: { [Op.in]: userRoles } }
          ]
        }
      });
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this password group'
        });
      }
      
      // Get all passwords in the group
      const passwords = await Password.findAll({
        where: { group_id: groupId }
      });
      
      // Decrypt passwords
      const decryptedPasswords = [];
      for (const password of passwords) {
        try {
          const decryptedPassword = passwordEncryption.decryptPasswordEntry(
            password.toJSON(),
            masterPassword
          );
          decryptedPasswords.push(decryptedPassword);
        } catch (error) {
          console.error(`Error decrypting password ${password.id}:`, error);
          // Include the password with a note that it couldn't be decrypted
          const passwordData = password.toJSON();
          passwordData.decryption_failed = true;
          decryptedPasswords.push(passwordData);
        }
      }
      
      return res.status(200).json({
        success: true,
        data: decryptedPasswords
      });
    } catch (error) {
      console.error('Error getting passwords:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while getting passwords',
        error: error.message
      });
    }
  }
  
  /**
   * Get a specific password by ID
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async getPassword(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      const { masterPassword } = req.body;
      
      if (!masterPassword) {
        return res.status(400).json({
          success: false,
          message: 'Master password is required to decrypt the password'
        });
      }
      
      // Find the password
      const password = await Password.findByPk(id);
      
      if (!password) {
        return res.status(404).json({
          success: false,
          message: 'Password not found'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(password.group_id);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Get user's roles for this farm
      const userRoles = userFarm.map(uf => uf.role_id);
      
      // Check if user has permission to access this group
      const hasPermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: password.group_id,
          [Op.or]: [
            { user_id: userId },
            { role_id: { [Op.in]: userRoles } }
          ]
        }
      });
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to access this password'
        });
      }
      
      // Decrypt the password
      try {
        const decryptedPassword = passwordEncryption.decryptPasswordEntry(
          password.toJSON(),
          masterPassword
        );
        
        return res.status(200).json({
          success: true,
          data: decryptedPassword
        });
      } catch (error) {
        console.error(`Error decrypting password ${password.id}:`, error);
        return res.status(400).json({
          success: false,
          message: 'Failed to decrypt password. Incorrect master password?',
          error: error.message
        });
      }
    } catch (error) {
      console.error('Error getting password:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while getting the password',
        error: error.message
      });
    }
  }
  
  /**
   * Create a new password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async createPassword(req, res) {
    try {
      const { groupId } = req.params;
      const { name, username, password, url, notes, has_2fa, totp_secret, masterPassword } = req.body;
      const userId = req.user.id;
      
      if (!masterPassword) {
        return res.status(400).json({
          success: false,
          message: 'Master password is required to encrypt the password'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(groupId);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has edit or manage permission for this group
      const hasPermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: groupId,
          user_id: userId,
          permission_type: { [Op.in]: ['edit', 'manage'] }
        }
      });
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to add passwords to this group'
        });
      }
      
      // Encrypt the password data
      const passwordData = {
        name,
        username,
        password,
        url,
        notes,
        has_2fa: has_2fa || false,
        totp_secret
      };
      
      const encryptedData = passwordEncryption.encryptPasswordEntry(passwordData, masterPassword);
      
      // Create the password entry
      const newPassword = await Password.create({
        group_id: groupId,
        name: encryptedData.name,
        username: encryptedData.username,
        password: encryptedData.password,
        url: encryptedData.url,
        notes: encryptedData.notes,
        has_2fa: encryptedData.has_2fa,
        totp_secret: encryptedData.totp_secret,
        encryption_key_id: encryptedData.encryption_key_id,
        encryption_iv: encryptedData.encryption_iv,
        created_by: userId
      });
      
      return res.status(201).json({
        success: true,
        data: {
          id: newPassword.id,
          name: name,
          has_2fa: has_2fa || false,
          created_at: newPassword.created_at,
          updated_at: newPassword.updated_at
        },
        message: 'Password created successfully'
      });
    } catch (error) {
      console.error('Error creating password:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while creating the password',
        error: error.message
      });
    }
  }
  
  /**
   * Update a password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async updatePassword(req, res) {
    try {
      const { id } = req.params;
      const { name, username, password, url, notes, has_2fa, totp_secret, masterPassword } = req.body;
      const userId = req.user.id;
      
      if (!masterPassword) {
        return res.status(400).json({
          success: false,
          message: 'Master password is required to update the password'
        });
      }
      
      // Find the password
      const passwordEntry = await Password.findByPk(id);
      
      if (!passwordEntry) {
        return res.status(404).json({
          success: false,
          message: 'Password not found'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(passwordEntry.group_id);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has edit or manage permission for this group
      const hasPermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: passwordEntry.group_id,
          user_id: userId,
          permission_type: { [Op.in]: ['edit', 'manage'] }
        }
      });
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to update this password'
        });
      }
      
      // First decrypt the existing password to get any fields not being updated
      let currentData;
      try {
        currentData = passwordEncryption.decryptPasswordEntry(
          passwordEntry.toJSON(),
          masterPassword
        );
      } catch (error) {
        return res.status(400).json({
          success: false,
          message: 'Failed to decrypt existing password. Incorrect master password?',
          error: error.message
        });
      }
      
      // Merge current data with updates
      const updatedData = {
        name: name || currentData.name,
        username: username !== undefined ? username : currentData.username,
        password: password !== undefined ? password : currentData.password,
        url: url !== undefined ? url : currentData.url,
        notes: notes !== undefined ? notes : currentData.notes,
        has_2fa: has_2fa !== undefined ? has_2fa : currentData.has_2fa,
        totp_secret: totp_secret !== undefined ? totp_secret : currentData.totp_secret
      };
      
      // Encrypt the updated data
      const encryptedData = passwordEncryption.encryptPasswordEntry(updatedData, masterPassword);
      
      // Update the password entry
      await passwordEntry.update({
        name: encryptedData.name,
        username: encryptedData.username,
        password: encryptedData.password,
        url: encryptedData.url,
        notes: encryptedData.notes,
        has_2fa: encryptedData.has_2fa,
        totp_secret: encryptedData.totp_secret,
        encryption_key_id: encryptedData.encryption_key_id,
        encryption_iv: encryptedData.encryption_iv
      });
      
      return res.status(200).json({
        success: true,
        data: {
          id: passwordEntry.id,
          name: updatedData.name,
          has_2fa: updatedData.has_2fa,
          created_at: passwordEntry.created_at,
          updated_at: passwordEntry.updated_at
        },
        message: 'Password updated successfully'
      });
    } catch (error) {
      console.error('Error updating password:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while updating the password',
        error: error.message
      });
    }
  }
  
  /**
   * Delete a password
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  async deletePassword(req, res) {
    try {
      const { id } = req.params;
      const userId = req.user.id;
      
      // Find the password
      const password = await Password.findByPk(id);
      
      if (!password) {
        return res.status(404).json({
          success: false,
          message: 'Password not found'
        });
      }
      
      // Find the password group
      const passwordGroup = await PasswordGroup.findByPk(password.group_id);
      
      if (!passwordGroup) {
        return res.status(404).json({
          success: false,
          message: 'Password group not found'
        });
      }
      
      // Check if user has access to the farm
      const userFarm = await req.user.getUserFarms({
        where: { farm_id: passwordGroup.farm_id }
      });
      
      if (!userFarm || userFarm.length === 0) {
        return res.status(403).json({
          success: false,
          message: 'You do not have access to this farm'
        });
      }
      
      // Check if user has edit or manage permission for this group
      const hasPermission = await PasswordGroupPermission.findOne({
        where: {
          group_id: password.group_id,
          user_id: userId,
          permission_type: { [Op.in]: ['edit', 'manage'] }
        }
      });
      
      if (!hasPermission) {
        return res.status(403).json({
          success: false,
          message: 'You do not have permission to delete this password'
        });
      }
      
      // Delete the password
      await password.destroy();
      
      return res.status(200).json({
        success: true,
        message: 'Password deleted successfully'
      });
    } catch (error) {
      console.error('Error deleting password:', error);
      return res.status(500).json({
        success: false,
        message: 'An error occurred while deleting the password',
        error: error.message
      });
    }
  }
}

export default new PasswordController();