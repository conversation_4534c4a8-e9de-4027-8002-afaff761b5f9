import MenuPreference from '../models/MenuPreference.js';
import User from '../models/User.js';
import { updateAllUsersMenuPreferences, getDefaultMenuStructure as getDefaultMenuFromUtils } from '../utils/menuUtils.js';

// Get default menu structure
export const getDefaultMenuStructure = async (req, res) => {
  try {
    // Set cache control headers to prevent 304 responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');

    // Get the default menu structure from the utility function
    const defaultMenuStructure = getDefaultMenuFromUtils();

    // Update all users' menu preferences with new menu items
    try {
      // Run this asynchronously so it doesn't block the response
      updateAllUsersMenuPreferences(defaultMenuStructure)
        .then(updatedCount => {
          console.log(`Updated menu preferences for ${updatedCount} users`);
        })
        .catch(updateError => {
          console.error('Error updating users menu preferences:', updateError);
        });
    } catch (updateError) {
      console.error('Error updating users menu preferences:', updateError);
      // Continue with the response even if updating users fails
    }

    return res.status(200).json(defaultMenuStructure);
  } catch (error) {
    console.error('Error getting default menu structure:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Get user's menu preferences
export const getUserMenuPreferences = async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Set cache control headers to prevent 304 responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');

    // Get user's menu preferences
    let menuPreference = await MenuPreference.findOne({
      where: { user_id: userId }
    });

    // If user doesn't have menu preferences yet, return default menu structure
    if (!menuPreference) {
      // Get default menu structure
      const defaultMenuStructure = await getDefaultMenuStructureHelper();

      return res.status(200).json({
        ...defaultMenuStructure,
        userId
      });
    }

    return res.status(200).json(menuPreference.preferences);
  } catch (error) {
    console.error('Error getting user menu preferences:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Save user's menu preferences
export const saveUserMenuPreferences = async (req, res) => {
  try {
    const { userId, headerItems, sidebarCategories, quickLinksItems } = req.body;

    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Validate header items - maximum 5 visible items
    const visibleHeaderItems = headerItems.filter(item => item.isVisible);
    if (visibleHeaderItems.length > 5) {
      return res.status(400).json({ 
        error: 'You can have a maximum of 5 visible header menu items.',
        details: {
          currentCount: visibleHeaderItems.length,
          maxAllowed: 5
        }
      });
    }

    // Set cache control headers to prevent 304 responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');

    // Create or update menu preferences
    const [menuPreference, created] = await MenuPreference.findOrCreate({
      where: { user_id: userId },
      defaults: {
        preferences: {
          userId,
          headerItems,
          sidebarCategories,
          quickLinksItems
        }
      }
    });

    // If menu preferences already exist, update them
    if (!created) {
      menuPreference.preferences = {
        userId,
        headerItems,
        sidebarCategories,
        quickLinksItems
      };
      await menuPreference.save();
    }

    return res.status(200).json(menuPreference.preferences);
  } catch (error) {
    console.error('Error saving user menu preferences:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Reset user's menu preferences to defaults
export const resetUserMenuPreferences = async (req, res) => {
  try {
    const { userId } = req.params;

    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    // Set cache control headers to prevent 304 responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    res.setHeader('Surrogate-Control', 'no-store');

    // Delete user's menu preferences
    await MenuPreference.destroy({
      where: { user_id: userId }
    });

    // Get default menu structure
    const defaultMenuStructure = await getDefaultMenuStructureHelper();

    return res.status(200).json({
      ...defaultMenuStructure,
      userId
    });
  } catch (error) {
    console.error('Error resetting user menu preferences:', error);
    return res.status(500).json({ error: error.message });
  }
};

// Helper function to get default menu structure
const getDefaultMenuStructureHelper = async () => {
  // Get the default menu structure from the utility function
  const defaultMenuStructure = getDefaultMenuFromUtils();

  // Update all users' menu preferences with new menu items
  try {
    // Run this asynchronously so it doesn't block the response
    updateAllUsersMenuPreferences(defaultMenuStructure)
      .then(updatedCount => {
        console.log(`Updated menu preferences for ${updatedCount} users from helper function`);
      })
      .catch(updateError => {
        console.error('Error updating users menu preferences from helper function:', updateError);
      });
  } catch (updateError) {
    console.error('Error updating users menu preferences from helper function:', updateError);
    // Continue with the response even if updating users fails
  }

  return defaultMenuStructure;
};
