import { sequelize } from '../config/database.js';
import Farm from '../models/Farm.js';
import User from '../models/User.js';
import DeliverySchedule from '../models/DeliverySchedule.js';
import DeliverySlot from '../models/DeliverySlot.js';
import DeliveryRoute from '../models/DeliveryRoute.js';
import DeliveryAssignment from '../models/DeliveryAssignment.js';
import DeliveryTracking from '../models/DeliveryTracking.js';
import PurchaseRequest from '../models/PurchaseRequest.js';
import { Op } from 'sequelize';

// Get all delivery schedules for a farm
export const getFarmDeliverySchedules = async (req, res) => {
  try {
    const { farmId } = req.params;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get all delivery schedules for the farm
    const schedules = await DeliverySchedule.findAll({
      where: { farm_id: farmId },
      include: [
        {
          model: DeliverySlot,
          as: 'slots'
        }
      ],
      order: [
        ['day_of_week', 'ASC'],
        ['start_time', 'ASC'],
        [{ model: DeliverySlot, as: 'slots' }, 'slot_time', 'ASC']
      ]
    });

    res.status(200).json(schedules);
  } catch (error) {
    console.error('Error fetching farm delivery schedules:', error);
    res.status(500).json({
      message: "Failed to fetch farm delivery schedules",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Create a new delivery schedule for a farm
export const createDeliverySchedule = async (req, res) => {
  try {
    const { farmId } = req.params;
    const {
      day_of_week,
      start_time,
      end_time,
      max_deliveries,
      slots
    } = req.body;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to create delivery schedules
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to create delivery schedules for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Create the delivery schedule
    const schedule = await DeliverySchedule.create({
      farm_id: farmId,
      day_of_week,
      start_time,
      end_time,
      max_deliveries,
      is_active: true
    });

    // Create delivery slots if provided
    if (slots && Array.isArray(slots) && slots.length > 0) {
      const slotPromises = slots.map(slot => {
        return DeliverySlot.create({
          schedule_id: schedule.id,
          slot_time: slot.time,
          max_deliveries: slot.max_deliveries,
          is_active: true
        });
      });

      await Promise.all(slotPromises);
    }

    // Fetch the created schedule with slots
    const createdSchedule = await DeliverySchedule.findByPk(schedule.id, {
      include: [
        {
          model: DeliverySlot,
          as: 'slots'
        }
      ]
    });

    res.status(201).json(createdSchedule);
  } catch (error) {
    console.error('Error creating delivery schedule:', error);
    res.status(500).json({
      message: "Failed to create delivery schedule",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update a delivery schedule
export const updateDeliverySchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const {
      day_of_week,
      start_time,
      end_time,
      max_deliveries,
      is_active
    } = req.body;

    // Check if schedule exists
    const schedule = await DeliverySchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({
        message: "Delivery schedule not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          scheduleId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to update delivery schedules
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: schedule.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to update delivery schedules for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: schedule.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update the schedule
    await schedule.update({
      day_of_week: day_of_week !== undefined ? day_of_week : schedule.day_of_week,
      start_time: start_time !== undefined ? start_time : schedule.start_time,
      end_time: end_time !== undefined ? end_time : schedule.end_time,
      max_deliveries: max_deliveries !== undefined ? max_deliveries : schedule.max_deliveries,
      is_active: is_active !== undefined ? is_active : schedule.is_active
    });

    // Fetch the updated schedule with slots
    const updatedSchedule = await DeliverySchedule.findByPk(scheduleId, {
      include: [
        {
          model: DeliverySlot,
          as: 'slots'
        }
      ]
    });

    res.status(200).json(updatedSchedule);
  } catch (error) {
    console.error('Error updating delivery schedule:', error);
    res.status(500).json({
      message: "Failed to update delivery schedule",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Delete a delivery schedule
export const deleteDeliverySchedule = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Check if schedule exists
    const schedule = await DeliverySchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({
        message: "Delivery schedule not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          scheduleId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to delete delivery schedules
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: schedule.farm_id,
        role: { [Op.in]: ['owner', 'admin'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to delete delivery schedules for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: schedule.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Delete the schedule (this will also delete associated slots due to CASCADE)
    await schedule.destroy();

    res.status(200).json({
      message: "Delivery schedule deleted successfully"
    });
  } catch (error) {
    console.error('Error deleting delivery schedule:', error);
    res.status(500).json({
      message: "Failed to delete delivery schedule",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get all delivery slots for a schedule
export const getDeliverySlots = async (req, res) => {
  try {
    const { scheduleId } = req.params;

    // Check if schedule exists
    const schedule = await DeliverySchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({
        message: "Delivery schedule not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          scheduleId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get all slots for the schedule
    const slots = await DeliverySlot.findAll({
      where: { schedule_id: scheduleId },
      order: [['slot_time', 'ASC']]
    });

    res.status(200).json(slots);
  } catch (error) {
    console.error('Error fetching delivery slots:', error);
    res.status(500).json({
      message: "Failed to fetch delivery slots",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Create a new delivery slot
export const createDeliverySlot = async (req, res) => {
  try {
    const { scheduleId } = req.params;
    const { slot_time, max_deliveries } = req.body;

    // Check if schedule exists
    const schedule = await DeliverySchedule.findByPk(scheduleId);
    if (!schedule) {
      return res.status(404).json({
        message: "Delivery schedule not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          scheduleId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to create delivery slots
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: schedule.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to create delivery slots for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: schedule.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Create the delivery slot
    const slot = await DeliverySlot.create({
      schedule_id: scheduleId,
      slot_time,
      max_deliveries,
      is_active: true
    });

    res.status(201).json(slot);
  } catch (error) {
    console.error('Error creating delivery slot:', error);
    res.status(500).json({
      message: "Failed to create delivery slot",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update a delivery slot
export const updateDeliverySlot = async (req, res) => {
  try {
    const { slotId } = req.params;
    const { slot_time, max_deliveries, is_active } = req.body;

    // Check if slot exists
    const slot = await DeliverySlot.findByPk(slotId, {
      include: [
        {
          model: DeliverySchedule,
          as: 'schedule'
        }
      ]
    });

    if (!slot) {
      return res.status(404).json({
        message: "Delivery slot not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          slotId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to update delivery slots
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: slot.schedule.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to update delivery slots for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: slot.schedule.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update the slot
    await slot.update({
      slot_time: slot_time !== undefined ? slot_time : slot.slot_time,
      max_deliveries: max_deliveries !== undefined ? max_deliveries : slot.max_deliveries,
      is_active: is_active !== undefined ? is_active : slot.is_active
    });

    res.status(200).json(slot);
  } catch (error) {
    console.error('Error updating delivery slot:', error);
    res.status(500).json({
      message: "Failed to update delivery slot",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Delete a delivery slot
export const deleteDeliverySlot = async (req, res) => {
  try {
    const { slotId } = req.params;

    // Check if slot exists
    const slot = await DeliverySlot.findByPk(slotId, {
      include: [
        {
          model: DeliverySchedule,
          as: 'schedule'
        }
      ]
    });

    if (!slot) {
      return res.status(404).json({
        message: "Delivery slot not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          slotId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to delete delivery slots
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: slot.schedule.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to delete delivery slots for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: slot.schedule.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Delete the slot
    await slot.destroy();

    res.status(200).json({
      message: "Delivery slot deleted successfully"
    });
  } catch (error) {
    console.error('Error deleting delivery slot:', error);
    res.status(500).json({
      message: "Failed to delete delivery slot",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get all delivery routes for a farm
export const getFarmDeliveryRoutes = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { date } = req.query;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Build query conditions
    const whereConditions = { farm_id: farmId };
    if (date) {
      whereConditions.delivery_date = date;
    }

    // Get all delivery routes for the farm
    const routes = await DeliveryRoute.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'driver',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DeliveryAssignment,
          as: 'assignments',
          include: [
            {
              model: PurchaseRequest,
              as: 'purchase_request'
            }
          ]
        }
      ],
      order: [
        ['delivery_date', 'ASC'],
        ['created_at', 'ASC']
      ]
    });

    res.status(200).json(routes);
  } catch (error) {
    console.error('Error fetching farm delivery routes:', error);
    res.status(500).json({
      message: "Failed to fetch farm delivery routes",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Create a new delivery route
export const createDeliveryRoute = async (req, res) => {
  try {
    const { farmId } = req.params;
    const {
      name,
      description,
      delivery_date,
      driver_id,
      purchase_request_ids
    } = req.body;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to create delivery routes
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: farmId,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to create delivery routes for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Create the delivery route
    const route = await DeliveryRoute.create({
      farm_id: farmId,
      name,
      description,
      delivery_date,
      driver_id,
      status: 'pending'
    });

    // Assign purchase requests to the route if provided
    if (purchase_request_ids && Array.isArray(purchase_request_ids) && purchase_request_ids.length > 0) {
      const assignmentPromises = purchase_request_ids.map((requestId, index) => {
        return DeliveryAssignment.create({
          route_id: route.id,
          purchase_request_id: requestId,
          delivery_order: index + 1,
          status: 'pending'
        });
      });

      await Promise.all(assignmentPromises);

      // Update purchase requests with the route ID
      await PurchaseRequest.update(
        { delivery_route_id: route.id },
        { where: { id: { [Op.in]: purchase_request_ids } } }
      );
    }

    // Fetch the created route with assignments
    const createdRoute = await DeliveryRoute.findByPk(route.id, {
      include: [
        {
          model: User,
          as: 'driver',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DeliveryAssignment,
          as: 'assignments',
          include: [
            {
              model: PurchaseRequest,
              as: 'purchase_request'
            }
          ]
        }
      ]
    });

    res.status(201).json(createdRoute);
  } catch (error) {
    console.error('Error creating delivery route:', error);
    res.status(500).json({
      message: "Failed to create delivery route",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update a delivery route
export const updateDeliveryRoute = async (req, res) => {
  try {
    const { routeId } = req.params;
    const {
      name,
      description,
      delivery_date,
      driver_id,
      status,
      start_time,
      end_time
    } = req.body;

    // Check if route exists
    const route = await DeliveryRoute.findByPk(routeId);
    if (!route) {
      return res.status(404).json({
        message: "Delivery route not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          routeId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to update delivery routes
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: route.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to update delivery routes for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: route.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update the route
    await route.update({
      name: name !== undefined ? name : route.name,
      description: description !== undefined ? description : route.description,
      delivery_date: delivery_date !== undefined ? delivery_date : route.delivery_date,
      driver_id: driver_id !== undefined ? driver_id : route.driver_id,
      status: status !== undefined ? status : route.status,
      start_time: start_time !== undefined ? start_time : route.start_time,
      end_time: end_time !== undefined ? end_time : route.end_time
    });

    // Fetch the updated route with assignments
    const updatedRoute = await DeliveryRoute.findByPk(routeId, {
      include: [
        {
          model: User,
          as: 'driver',
          attributes: ['id', 'first_name', 'last_name', 'email']
        },
        {
          model: DeliveryAssignment,
          as: 'assignments',
          include: [
            {
              model: PurchaseRequest,
              as: 'purchase_request'
            }
          ]
        }
      ]
    });

    res.status(200).json(updatedRoute);
  } catch (error) {
    console.error('Error updating delivery route:', error);
    res.status(500).json({
      message: "Failed to update delivery route",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Delete a delivery route
export const deleteDeliveryRoute = async (req, res) => {
  try {
    const { routeId } = req.params;

    // Check if route exists
    const route = await DeliveryRoute.findByPk(routeId);
    if (!route) {
      return res.status(404).json({
        message: "Delivery route not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          routeId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to delete delivery routes
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: route.farm_id,
        role: { [Op.in]: ['owner', 'admin'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to delete delivery routes for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: route.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Update purchase requests to remove route ID
    await PurchaseRequest.update(
      { delivery_route_id: null },
      { where: { delivery_route_id: routeId } }
    );

    // Delete the route (this will also delete associated assignments due to CASCADE)
    await route.destroy();

    res.status(200).json({
      message: "Delivery route deleted successfully"
    });
  } catch (error) {
    console.error('Error deleting delivery route:', error);
    res.status(500).json({
      message: "Failed to delete delivery route",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update delivery route assignments
export const updateRouteAssignments = async (req, res) => {
  try {
    const { routeId } = req.params;
    const { assignments } = req.body;

    // Check if route exists
    const route = await DeliveryRoute.findByPk(routeId);
    if (!route) {
      return res.status(404).json({
        message: "Delivery route not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          routeId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user has permission to update route assignments
    const userFarm = await sequelize.models.user_farms.findOne({
      where: {
        user_id: req.user.id,
        farm_id: route.farm_id,
        role: { [Op.in]: ['owner', 'admin', 'manager'] }
      }
    });

    if (!userFarm) {
      return res.status(403).json({
        message: "You don't have permission to update route assignments for this farm",
        errorType: "Forbidden",
        errorCode: "403",
        context: {
          farmId: route.farm_id,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // Delete existing assignments
      await DeliveryAssignment.destroy({
        where: { route_id: routeId },
        transaction
      });

      // Reset purchase requests' route ID
      await PurchaseRequest.update(
        { delivery_route_id: null },
        { 
          where: { delivery_route_id: routeId },
          transaction
        }
      );

      // Create new assignments
      if (assignments && Array.isArray(assignments) && assignments.length > 0) {
        const assignmentPromises = assignments.map((assignment, index) => {
          return DeliveryAssignment.create({
            route_id: routeId,
            purchase_request_id: assignment.purchase_request_id,
            delivery_order: assignment.delivery_order || index + 1,
            estimated_arrival_time: assignment.estimated_arrival_time || null,
            status: assignment.status || 'pending'
          }, { transaction });
        });

        await Promise.all(assignmentPromises);

        // Update purchase requests with the route ID
        const purchaseRequestIds = assignments.map(a => a.purchase_request_id);
        await PurchaseRequest.update(
          { delivery_route_id: routeId },
          { 
            where: { id: { [Op.in]: purchaseRequestIds } },
            transaction
          }
        );
      }

      // Commit the transaction
      await transaction.commit();

      // Fetch the updated route with assignments
      const updatedRoute = await DeliveryRoute.findByPk(routeId, {
        include: [
          {
            model: User,
            as: 'driver',
            attributes: ['id', 'first_name', 'last_name', 'email']
          },
          {
            model: DeliveryAssignment,
            as: 'assignments',
            include: [
              {
                model: PurchaseRequest,
                as: 'purchase_request'
              }
            ]
          }
        ]
      });

      res.status(200).json(updatedRoute);
    } catch (error) {
      // Rollback the transaction in case of error
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    console.error('Error updating route assignments:', error);
    res.status(500).json({
      message: "Failed to update route assignments",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Track driver location
export const trackDriverLocation = async (req, res) => {
  try {
    const { routeId } = req.params;
    const { latitude, longitude } = req.body;

    // Check if route exists
    const route = await DeliveryRoute.findByPk(routeId);
    if (!route) {
      return res.status(404).json({
        message: "Delivery route not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          routeId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user is the assigned driver or has admin permissions
    if (req.user.id !== route.driver_id) {
      const userFarm = await sequelize.models.user_farms.findOne({
        where: {
          user_id: req.user.id,
          farm_id: route.farm_id,
          role: { [Op.in]: ['owner', 'admin'] }
        }
      });

      if (!userFarm) {
        return res.status(403).json({
          message: "You don't have permission to track this route",
          errorType: "Forbidden",
          errorCode: "403",
          context: {
            farmId: route.farm_id,
            url: req.originalUrl,
            method: req.method
          }
        });
      }
    }

    // Create tracking point
    const trackingPoint = await DeliveryTracking.create({
      route_id: routeId,
      latitude,
      longitude,
      timestamp: new Date()
    });

    // If route is not in progress, update it
    if (route.status === 'pending') {
      await route.update({
        status: 'in_progress',
        start_time: new Date()
      });
    }

    res.status(201).json(trackingPoint);
  } catch (error) {
    console.error('Error tracking driver location:', error);
    res.status(500).json({
      message: "Failed to track driver location",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get driver location tracking points
export const getDriverLocationTracking = async (req, res) => {
  try {
    const { routeId } = req.params;
    const { limit } = req.query;

    // Check if route exists
    const route = await DeliveryRoute.findByPk(routeId);
    if (!route) {
      return res.status(404).json({
        message: "Delivery route not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          routeId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Build query options
    const queryOptions = {
      where: { route_id: routeId },
      order: [['timestamp', 'DESC']]
    };

    // Add limit if provided
    if (limit) {
      queryOptions.limit = parseInt(limit);
    }

    // Get tracking points
    const trackingPoints = await DeliveryTracking.findAll(queryOptions);

    res.status(200).json(trackingPoints);
  } catch (error) {
    console.error('Error fetching driver location tracking:', error);
    res.status(500).json({
      message: "Failed to fetch driver location tracking",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Update delivery assignment status
export const updateAssignmentStatus = async (req, res) => {
  try {
    const { assignmentId } = req.params;
    const { status, actual_arrival_time } = req.body;

    // Check if assignment exists
    const assignment = await DeliveryAssignment.findByPk(assignmentId, {
      include: [
        {
          model: DeliveryRoute,
          as: 'route'
        }
      ]
    });

    if (!assignment) {
      return res.status(404).json({
        message: "Delivery assignment not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          assignmentId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Check if user is the assigned driver or has admin permissions
    if (req.user.id !== assignment.route.driver_id) {
      const userFarm = await sequelize.models.user_farms.findOne({
        where: {
          user_id: req.user.id,
          farm_id: assignment.route.farm_id,
          role: { [Op.in]: ['owner', 'admin', 'manager'] }
        }
      });

      if (!userFarm) {
        return res.status(403).json({
          message: "You don't have permission to update this assignment",
          errorType: "Forbidden",
          errorCode: "403",
          context: {
            farmId: assignment.route.farm_id,
            url: req.originalUrl,
            method: req.method
          }
        });
      }
    }

    // Update the assignment
    await assignment.update({
      status: status || assignment.status,
      actual_arrival_time: actual_arrival_time || (status === 'completed' ? new Date() : assignment.actual_arrival_time)
    });

    // Check if all assignments are completed
    if (status === 'completed') {
      const pendingAssignments = await DeliveryAssignment.count({
        where: {
          route_id: assignment.route_id,
          status: { [Op.ne]: 'completed' }
        }
      });

      if (pendingAssignments === 0) {
        // All assignments completed, update route status
        await DeliveryRoute.update(
          {
            status: 'completed',
            end_time: new Date()
          },
          {
            where: { id: assignment.route_id }
          }
        );
      }
    }

    // Fetch the updated assignment
    const updatedAssignment = await DeliveryAssignment.findByPk(assignmentId, {
      include: [
        {
          model: PurchaseRequest,
          as: 'purchase_request'
        }
      ]
    });

    res.status(200).json(updatedAssignment);
  } catch (error) {
    console.error('Error updating assignment status:', error);
    res.status(500).json({
      message: "Failed to update assignment status",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};

// Get available delivery slots for customers
export const getAvailableDeliverySlots = async (req, res) => {
  try {
    const { farmId } = req.params;
    const { date } = req.query;

    // Check if farm exists
    const farm = await Farm.findByPk(farmId);
    if (!farm) {
      return res.status(404).json({
        message: "Farm not found",
        errorType: "NotFound",
        errorCode: "404",
        context: {
          farmId,
          url: req.originalUrl,
          method: req.method
        }
      });
    }

    // Get the day of week for the requested date
    const requestDate = date ? new Date(date) : new Date();
    const dayOfWeek = requestDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

    // Get delivery schedules for the farm and day of week
    const schedules = await DeliverySchedule.findAll({
      where: {
        farm_id: farmId,
        day_of_week: dayOfWeek,
        is_active: true
      },
      include: [
        {
          model: DeliverySlot,
          as: 'slots',
          where: {
            is_active: true
          },
          required: false
        }
      ]
    });

    // Format the response
    const formattedDate = requestDate.toISOString().split('T')[0];
    const availableSlots = [];

    for (const schedule of schedules) {
      for (const slot of schedule.slots) {
        // Count existing bookings for this slot
        const bookingCount = await PurchaseRequest.count({
          where: {
            farm_id: farmId,
            delivery_slot_id: slot.id,
            fulfillment_method: 'delivery',
            status: { [Op.notIn]: ['canceled', 'rejected'] }
          }
        });

        // Check if slot has available capacity
        const isAvailable = !slot.max_deliveries || bookingCount < slot.max_deliveries;

        if (isAvailable) {
          availableSlots.push({
            id: slot.id,
            date: formattedDate,
            time: slot.slot_time,
            available: isAvailable,
            remaining: slot.max_deliveries ? slot.max_deliveries - bookingCount : null
          });
        }
      }
    }

    res.status(200).json({
      date: formattedDate,
      day_of_week: dayOfWeek,
      available_slots: availableSlots
    });
  } catch (error) {
    console.error('Error fetching available delivery slots:', error);
    res.status(500).json({
      message: "Failed to fetch available delivery slots",
      errorType: "ServerError",
      errorCode: "500",
      context: {
        url: req.originalUrl,
        method: req.method
      }
    });
  }
};