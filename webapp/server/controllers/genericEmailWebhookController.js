import SupportTicket from '../models/SupportTicket.js';
import SupportTicketComment from '../models/SupportTicketComment.js';
import SupportTicketAttachment from '../models/SupportTicketAttachment.js';
import User from '../models/User.js';
import Farm from '../models/Farm.js';
import Receipt from '../models/Receipt.js';
import UserFarm from '../models/UserFarm.js';
import { sequelize } from '../config/database.js';
import { 
  generateStoragePath, 
  saveFile, 
  updateStorageUsage 
} from '../utils/fileUtils.js';
import { 
  sendTicketCreatedEmail, 
  getFrontendUrl 
} from '../utils/emailUtils.js';
import path from 'path';
import { marked } from 'marked';

/**
 * Generic email webhook handler
 * This endpoint will process all incoming emails and route them to the appropriate handler
 * based on the recipient email address
 */
export const processIncomingEmail = async (req, res) => {
  const transaction = await sequelize.transaction();

  try {
    const { 
      from, 
      subject, 
      text, 
      html, 
      attachments, 
      recipient,
      headers,
      timestamp 
    } = req.body;

    console.log('Received email webhook:', { from, subject, recipient });

    if (!recipient) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Recipient email address is required' });
    }

    // Parse the recipient email to determine how to handle it
    const recipientParts = recipient.split('@');
    if (recipientParts.length !== 2) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid recipient email format' });
    }

    const localPart = recipientParts[0].toLowerCase(); // e.g., "support", "receipts"
    const domain = recipientParts[1].toLowerCase();    // e.g., "nxtacre.com", "farm1.nxtacre.com"

    // Check if this is a support ticket email
    if (localPart === 'support') {
      return await processTicketEmail(req, res, transaction);
    }

    // Check if this is a receipt email
    if (localPart === 'receipts') {
      return await processReceiptEmail(req, res, transaction);
    }

    // If we reach here, we don't know how to handle this email
    await transaction.rollback();
    return res.status(400).json({ 
      error: 'Unrecognized email recipient format',
      details: `Email to ${recipient} does not match any known patterns`
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing incoming email:', error);
    return res.status(500).json({ error: error.message });
  }
};

/**
 * Process an email for a support ticket
 */
async function processTicketEmail(req, res, transaction) {
  try {
    const { 
      from, 
      subject, 
      text, 
      html, 
      attachments,
      recipient,
      headers,
      timestamp 
    } = req.body;

    // Extract email address from the "from" field
    const fromEmail = extractEmailAddress(from);
    if (!fromEmail) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid sender email format' });
    }

    // Extract ticket ID from subject or headers
    let ticketId = extractTicketIdFromSubject(subject);

    // If not found in subject, try to get it from headers
    if (!ticketId && headers) {
      ticketId = headers['X-Ticket-ID'] || headers['x-ticket-id'] || 
                 headers['In-Reply-To'] || headers['in-reply-to'] ||
                 headers['References'] || headers['references'];

      // If it's a reference or in-reply-to, we need to extract the ticket ID
      if (ticketId && (ticketId.includes('ticket-'))) {
        const match = ticketId.match(/ticket-([a-f0-9-]+)/i);
        if (match && match[1]) {
          ticketId = match[1];
        }
      }
    }

    // Find the user by email
    const user = await User.findOne({
      where: { email: fromEmail }
    });

    if (!user) {
      await transaction.rollback();
      return res.status(404).json({ error: 'User not found for the given email address' });
    }

    let ticket;
    let isNewTicket = false;

    // Extract content from the email
    let content = '';

    // Process email content - prefer plain text if available, otherwise parse HTML
    if (text && text.trim()) {
      // For plain text emails, just clean the content
      content = cleanEmailContent(text);
    } else if (html && html.trim()) {
      // For HTML emails, parse the HTML to extract readable text
      // This handles emails sent in HTML format (rich text, formatting, etc.)
      try {
        // Convert HTML to plain text using marked library
        const plainText = marked.parse(html, { mangle: false, headerIds: false });
        // Remove any HTML tags that might remain after parsing
        const textWithoutTags = plainText.replace(/<[^>]*>/g, '');
        // Clean the content using the same function as for plain text
        // to remove quoted replies, signatures, etc.
        content = cleanEmailContent(textWithoutTags);

        // If after cleaning we have empty content, try to use the original HTML
        if (!content.trim() && html.trim()) {
          // Just strip HTML tags as a fallback
          const strippedHtml = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
          content = strippedHtml || 'Unable to extract content from HTML';
        }
      } catch (error) {
        console.error('Error parsing HTML email content:', error);
        // Try a simpler approach as fallback
        const strippedHtml = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim();
        content = strippedHtml || 'Error parsing HTML content';
      }
    } else {
      // Check if we have any content at all
      if ((text && !text.trim()) || (html && !html.trim())) {
        content = 'Email received but content appears to be empty';
      } else {
        content = 'Empty email content';
      }
    }

    // If we have a ticket ID, try to find the existing ticket
    if (ticketId) {
      ticket = await SupportTicket.findByPk(ticketId);
    }

    // If no ticket ID or ticket not found, create a new ticket
    if (!ticket) {
      isNewTicket = true;

      // Check if user has only one farm association
      const userFarms = await UserFarm.findAll({
        where: { user_id: user.id },
        transaction
      });

      // Set farm_id only if user has exactly one farm association
      let farmId = null;
      if (userFarms.length === 1) {
        farmId = userFarms[0].farm_id;
      }

      // Create a new ticket
      ticket = await SupportTicket.create({
        subject: subject || 'New Support Request',
        description: content,
        status: 'open',
        priority: 'medium',
        user_id: user.id,
        farm_id: farmId, // Set to the user's only farm or null if multiple/none
      }, { transaction });

      // Get frontend URL for the user
      const frontendUrl = await getFrontendUrl(user.id);

      // Send email notification to the user
      await sendTicketCreatedEmail(ticket, user, frontendUrl);

      // Process attachments if any
      if (attachments && attachments.length > 0) {
        for (const attachment of attachments) {
          try {
            // Generate a unique filename
            const filename = `${Date.now()}-${attachment.filename || 'attachment'}`;

            // Generate storage path - use ticket's farm_id if available, otherwise use a global storage path
            const storagePath = generateStoragePath(ticket.farm_id || 'global', 'ticket-attachments', filename);

            // Save file to disk
            const fullPath = await saveFile(Buffer.from(attachment.content, 'base64'), storagePath);

            // Create attachment record
            await SupportTicketAttachment.create({
              ticket_id: ticket.id,
              user_id: user.id,
              filename: filename,
              original_filename: attachment.filename || 'attachment',
              file_path: storagePath,
              file_size: attachment.size || Buffer.from(attachment.content, 'base64').length,
              file_type: attachment.contentType || 'application/octet-stream'
            }, { transaction });

            // Update storage usage - use ticket's farm_id if available, otherwise use a global storage
            await updateStorageUsage(ticket.farm_id || 'global', attachment.size || Buffer.from(attachment.content, 'base64').length, false, 1);
          } catch (error) {
            console.error('Error processing attachment:', error);
            // Continue with other attachments even if one fails
          }
        }
      }

      // Get the attachments that were just created
      const ticketAttachments = await SupportTicketAttachment.findAll({
        where: { ticket_id: ticket.id },
        transaction
      });

      // Return success response for new ticket
      await transaction.commit();

      return res.status(201).json({
        message: 'New support ticket created from email',
        ticket: {
          id: ticket.id,
          subject: ticket.subject,
          description: ticket.description,
          status: ticket.status,
          userId: ticket.user_id,
          createdAt: ticket.created_at,
          attachments: ticketAttachments.map(att => ({
            id: att.id,
            filename: att.original_filename,
            fileSize: att.file_size,
            fileType: att.file_type
          }))
        }
      });
    }

    // Create the comment
    const comment = await SupportTicketComment.create({
      content,
      ticket_id: ticket.id,
      user_id: user.id,
      is_internal: false,
      is_from_email: true,
      email_source: fromEmail,
      email_message_id: headers && (headers['Message-ID'] || headers['message-id'])
    }, { transaction });

    // Update the ticket's updated_at timestamp
    await ticket.update({ 
      updated_at: new Date(),
      // If the ticket was closed, reopen it
      status: ticket.status === 'closed' ? 'open' : ticket.status
    }, { transaction });

    // Process attachments if any
    if (attachments && attachments.length > 0) {
      for (const attachment of attachments) {
        try {
          // Generate a unique filename
          const filename = `${Date.now()}-${attachment.filename || 'attachment'}`;

          // Generate storage path - use ticket's farm_id if available, otherwise use a global storage path
          const storagePath = generateStoragePath(ticket.farm_id || 'global', 'ticket-attachments', filename);

          // Save file to disk
          const fullPath = await saveFile(Buffer.from(attachment.content, 'base64'), storagePath);

          // Create attachment record
          await SupportTicketAttachment.create({
            ticket_id: ticket.id,
            user_id: user.id,
            filename: filename,
            original_filename: attachment.filename || 'attachment',
            file_path: storagePath,
            file_size: attachment.size || Buffer.from(attachment.content, 'base64').length,
            file_type: attachment.contentType || 'application/octet-stream'
          }, { transaction });

          // Update storage usage - use ticket's farm_id if available, otherwise use a global storage
          await updateStorageUsage(ticket.farm_id || 'global', attachment.size || Buffer.from(attachment.content, 'base64').length, false, 1);
        } catch (error) {
          console.error('Error processing attachment:', error);
          // Continue with other attachments even if one fails
        }
      }
    }

    // Get the attachments that were just created
    const commentAttachments = await SupportTicketAttachment.findAll({
      where: { 
        ticket_id: ticket.id,
        created_at: {
          [sequelize.Op.gte]: new Date(Date.now() - 60000) // Attachments created in the last minute
        }
      },
      transaction
    });

    await transaction.commit();

    return res.status(201).json({
      message: 'Email reply processed successfully',
      comment: {
        id: comment.id,
        content: comment.content,
        ticketId: comment.ticket_id,
        userId: comment.user_id,
        isFromEmail: comment.is_from_email,
        createdAt: comment.created_at,
        attachments: commentAttachments.map(att => ({
          id: att.id,
          filename: att.original_filename,
          fileSize: att.file_size,
          fileType: att.file_type
        }))
      }
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing ticket email:', error);
    return res.status(500).json({ error: error.message });
  }
}

/**
 * Process an email receipt
 */
async function processReceiptEmail(req, res, transaction) {
  try {
    const { 
      from, 
      subject, 
      text, 
      html, 
      attachments, 
      recipient,
      timestamp 
    } = req.body;

    // Extract farm subdomain from recipient
    // Example: <EMAIL> -> farm1
    const recipientParts = recipient.split('@');
    if (recipientParts.length !== 2) {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid recipient email format' });
    }

    const domainParts = recipientParts[1].split('.');
    if (domainParts.length < 3 || domainParts[domainParts.length - 2] !== 'nxtacre') {
      await transaction.rollback();
      return res.status(400).json({ error: 'Invalid domain format' });
    }

    const subdomain = domainParts[0];

    // Find farm by subdomain
    const farm = await Farm.findOne({
      where: {
        subdomain: subdomain
      }
    });

    if (!farm) {
      await transaction.rollback();
      return res.status(404).json({ error: 'Farm not found for the given subdomain' });
    }

    // Create receipt data
    const receiptData = {
      email_source: from,
      email_subject: subject,
      email_received_at: timestamp ? new Date(timestamp) : new Date(),
      description: text || '',
      status: 'pending',
      farm_id: farm.id,
      tenant_id: farm.id, // Using farm_id as tenant_id for now
      receipt_date: new Date()
    };

    // Try to extract vendor name and amount from subject or text
    const vendorMatch = subject.match(/from\s+([^$]+)/i) || text?.match(/from\s+([^$]+)/i);
    if (vendorMatch && vendorMatch[1]) {
      receiptData.vendor_name = vendorMatch[1].trim();
    }

    const amountMatch = subject.match(/\$\s*(\d+(\.\d{1,2})?)/i) || text?.match(/\$\s*(\d+(\.\d{1,2})?)/i);
    if (amountMatch && amountMatch[1]) {
      receiptData.amount = parseFloat(amountMatch[1]);
    }

    // Handle attachments
    if (attachments && attachments.length > 0) {
      // Process the first attachment only for simplicity
      const attachment = attachments[0];

      // Generate a unique filename
      const filename = `${Date.now()}-${attachment.filename || 'attachment'}`;

      // Generate storage path
      const storagePath = generateStoragePath(farm.id, 'email', filename);

      // Save file to disk
      const fullPath = await saveFile(Buffer.from(attachment.content, 'base64'), storagePath);

      // Add file information to receipt data
      receiptData.file_path = storagePath;
      receiptData.file_size = attachment.size || Buffer.from(attachment.content, 'base64').length;
      receiptData.file_type = path.extname(attachment.filename || 'attachment').substring(1) || 'unknown';
      receiptData.mime_type = attachment.contentType || 'application/octet-stream';

      // Update storage usage
      await updateStorageUsage(farm.id, receiptData.file_size, false, 1);
    }

    // Create receipt record
    const receipt = await Receipt.create(receiptData, { transaction });

    await transaction.commit();

    return res.status(201).json({
      message: 'Email receipt processed successfully',
      receipt
    });
  } catch (error) {
    await transaction.rollback();
    console.error('Error processing email receipt:', error);
    return res.status(500).json({ error: error.message });
  }
}

/**
 * Extract email address from a string like "John Doe <<EMAIL>>"
 */
function extractEmailAddress(from) {
  if (!from) return null;

  // Check if the from string contains an email address in angle brackets
  const match = from.match(/<([^>]+)>/);
  if (match && match[1]) {
    return match[1];
  }

  // If no angle brackets, check if it's a plain email address
  const emailMatch = from.match(/([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/);
  if (emailMatch && emailMatch[1]) {
    return emailMatch[1];
  }

  return null;
}

/**
 * Extract ticket ID from subject like "Re: [Ticket #abc-123] Subject"
 */
function extractTicketIdFromSubject(subject) {
  if (!subject) return null;

  const match = subject.match(/\[Ticket #([^\]]+)\]/);
  if (match && match[1]) {
    return match[1];
  }

  return null;
}

/**
 * Clean email content by removing quoted replies and signatures
 */
function cleanEmailContent(text) {
  if (!text) return '';

  // Split the text into lines
  const lines = text.split('\n');

  // Find the first line that starts a quoted reply
  let quoteStart = lines.findIndex(line => 
    line.trim().startsWith('>') || 
    line.trim().startsWith('On ') && line.includes('wrote:') ||
    line.includes('From:') && line.includes('Sent:') && line.includes('To:')
  );

  // If no quote markers found, look for common reply separators
  if (quoteStart === -1) {
    quoteStart = lines.findIndex(line => 
      line.trim() === '---' || 
      line.trim() === '___' || 
      line.trim() === '***' ||
      line.trim().startsWith('--') && line.trim().length < 5
    );
  }

  // If a quote or separator was found, only keep the content before it
  if (quoteStart !== -1) {
    lines.splice(quoteStart);
  }

  // Join the remaining lines and trim
  return lines.join('\n').trim();
}
