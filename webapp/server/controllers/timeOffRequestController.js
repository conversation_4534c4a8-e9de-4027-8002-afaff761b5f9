import TimeOffRequest from '../models/TimeOffRequest.js';
import Employee from '../models/Employee.js';
import User from '../models/User.js';
import { Op } from 'sequelize';

// Get all time off requests with optional filtering
export const getTimeOffRequests = async (req, res) => {
  try {
    const { employee_id, status, request_type, start_date, end_date } = req.query;
    
    // Build filter conditions
    const whereConditions = {};
    
    if (employee_id) {
      whereConditions.employee_id = employee_id;
    }
    
    if (status && status !== 'all') {
      whereConditions.status = status;
    }
    
    if (request_type && request_type !== 'all') {
      whereConditions.request_type = request_type;
    }
    
    // Filter by date range (requests that overlap with the given range)
    if (start_date || end_date) {
      if (start_date && end_date) {
        whereConditions[Op.or] = [
          {
            // Start date falls within range
            start_date: {
              [Op.between]: [start_date, end_date]
            }
          },
          {
            // End date falls within range
            end_date: {
              [Op.between]: [start_date, end_date]
            }
          },
          {
            // Range falls within request
            [Op.and]: [
              { start_date: { [Op.lte]: start_date } },
              { end_date: { [Op.gte]: end_date } }
            ]
          }
        ];
      } else if (start_date) {
        whereConditions.end_date = {
          [Op.gte]: start_date
        };
      } else if (end_date) {
        whereConditions.start_date = {
          [Op.lte]: end_date
        };
      }
    }
    
    // Find time off requests with filters
    const timeOffRequests = await TimeOffRequest.findAll({
      where: whereConditions,
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ],
      order: [['start_date', 'DESC']]
    });
    
    res.status(200).json(timeOffRequests);
  } catch (error) {
    console.error('Error fetching time off requests:', error);
    res.status(500).json({ message: 'Failed to fetch time off requests', error: error.message });
  }
};

// Get time off request by ID
export const getTimeOffRequestById = async (req, res) => {
  try {
    const { requestId } = req.params;
    
    const timeOffRequest = await TimeOffRequest.findByPk(requestId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    if (!timeOffRequest) {
      return res.status(404).json({ message: 'Time off request not found' });
    }
    
    res.status(200).json(timeOffRequest);
  } catch (error) {
    console.error('Error fetching time off request:', error);
    res.status(500).json({ message: 'Failed to fetch time off request', error: error.message });
  }
};

// Create a new time off request
export const createTimeOffRequest = async (req, res) => {
  try {
    const requestData = req.body;
    
    // Validate required fields
    if (!requestData.employee_id || !requestData.request_type || !requestData.start_date || !requestData.end_date) {
      return res.status(400).json({ message: 'Employee ID, request type, start date, and end date are required' });
    }
    
    // Calculate total days if not provided
    if (!requestData.total_days) {
      const startDate = new Date(requestData.start_date);
      const endDate = new Date(requestData.end_date);
      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
      requestData.total_days = diffDays;
    }
    
    // Create the time off request
    const newRequest = await TimeOffRequest.create(requestData);
    
    // Fetch the created request with associations
    const timeOffRequest = await TimeOffRequest.findByPk(newRequest.id, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(201).json(timeOffRequest);
  } catch (error) {
    console.error('Error creating time off request:', error);
    res.status(500).json({ message: 'Failed to create time off request', error: error.message });
  }
};

// Update a time off request
export const updateTimeOffRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const requestData = req.body;
    
    // Find the time off request
    const timeOffRequest = await TimeOffRequest.findByPk(requestId);
    
    if (!timeOffRequest) {
      return res.status(404).json({ message: 'Time off request not found' });
    }
    
    // Calculate total days if start or end date changed
    if ((requestData.start_date && requestData.start_date !== timeOffRequest.start_date) || 
        (requestData.end_date && requestData.end_date !== timeOffRequest.end_date)) {
      const startDate = new Date(requestData.start_date || timeOffRequest.start_date);
      const endDate = new Date(requestData.end_date || timeOffRequest.end_date);
      const diffTime = Math.abs(endDate - startDate);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end days
      requestData.total_days = diffDays;
    }
    
    // Update the time off request
    await timeOffRequest.update(requestData);
    
    // Fetch the updated request with associations
    const updatedRequest = await TimeOffRequest.findByPk(requestId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedRequest);
  } catch (error) {
    console.error('Error updating time off request:', error);
    res.status(500).json({ message: 'Failed to update time off request', error: error.message });
  }
};

// Delete a time off request
export const deleteTimeOffRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    
    // Find the time off request
    const timeOffRequest = await TimeOffRequest.findByPk(requestId);
    
    if (!timeOffRequest) {
      return res.status(404).json({ message: 'Time off request not found' });
    }
    
    // Delete the time off request
    await timeOffRequest.destroy();
    
    res.status(200).json({ message: 'Time off request deleted successfully' });
  } catch (error) {
    console.error('Error deleting time off request:', error);
    res.status(500).json({ message: 'Failed to delete time off request', error: error.message });
  }
};

// Get time off requests by employee ID
export const getTimeOffRequestsByEmployee = async (req, res) => {
  try {
    const { employeeId } = req.params;
    
    const timeOffRequests = await TimeOffRequest.findAll({
      where: { employee_id: employeeId },
      order: [['start_date', 'DESC']]
    });
    
    res.status(200).json(timeOffRequests);
  } catch (error) {
    console.error('Error fetching employee time off requests:', error);
    res.status(500).json({ message: 'Failed to fetch employee time off requests', error: error.message });
  }
};

// Review a time off request (approve or deny)
export const reviewTimeOffRequest = async (req, res) => {
  try {
    const { requestId } = req.params;
    const { status, reviewed_by, review_notes } = req.body;
    
    // Validate required fields
    if (!status || !reviewed_by) {
      return res.status(400).json({ message: 'Status and reviewer ID are required' });
    }
    
    // Find the time off request
    const timeOffRequest = await TimeOffRequest.findByPk(requestId);
    
    if (!timeOffRequest) {
      return res.status(404).json({ message: 'Time off request not found' });
    }
    
    // Update the time off request
    await timeOffRequest.update({
      status,
      reviewed_by,
      reviewed_at: new Date(),
      review_notes: review_notes || timeOffRequest.review_notes
    });
    
    // Fetch the updated request with associations
    const updatedRequest = await TimeOffRequest.findByPk(requestId, {
      include: [
        {
          model: Employee,
          attributes: ['id', 'first_name', 'last_name'],
        },
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'first_name', 'last_name'],
        }
      ]
    });
    
    res.status(200).json(updatedRequest);
  } catch (error) {
    console.error('Error reviewing time off request:', error);
    res.status(500).json({ message: 'Failed to review time off request', error: error.message });
  }
};

// Get time off summary by employee
export const getTimeOffSummary = async (req, res) => {
  try {
    const { employee_id, year } = req.query;
    
    if (!employee_id) {
      return res.status(400).json({ message: 'Employee ID is required' });
    }
    
    const currentYear = year ? parseInt(year) : new Date().getFullYear();
    const startOfYear = new Date(currentYear, 0, 1);
    const endOfYear = new Date(currentYear, 11, 31);
    
    // Find all approved time off requests for the employee in the specified year
    const timeOffRequests = await TimeOffRequest.findAll({
      where: {
        employee_id,
        status: 'approved',
        [Op.or]: [
          {
            // Start date falls within year
            start_date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          {
            // End date falls within year
            end_date: {
              [Op.between]: [startOfYear, endOfYear]
            }
          },
          {
            // Year falls within request
            [Op.and]: [
              { start_date: { [Op.lte]: startOfYear } },
              { end_date: { [Op.gte]: endOfYear } }
            ]
          }
        ]
      }
    });
    
    // Calculate summary by request type
    const summary = {
      total_days: 0,
      by_type: {}
    };
    
    timeOffRequests.forEach(request => {
      const days = parseFloat(request.total_days) || 0;
      summary.total_days += days;
      
      if (!summary.by_type[request.request_type]) {
        summary.by_type[request.request_type] = 0;
      }
      summary.by_type[request.request_type] += days;
    });
    
    res.status(200).json(summary);
  } catch (error) {
    console.error('Error generating time off summary:', error);
    res.status(500).json({ message: 'Failed to generate time off summary', error: error.message });
  }
};