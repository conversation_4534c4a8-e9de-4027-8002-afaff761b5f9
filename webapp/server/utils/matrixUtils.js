import axios from 'axios';
import crypto from 'crypto';

/**
 * Utility functions for interacting with Matrix Synapse
 */

/**
 * Register a new user in Matrix Synapse
 * @param {string} username - The username to register (without the @ prefix or domain)
 * @param {string} password - The password for the user (optional if using shared secret)
 * @param {boolean} admin - Whether the user should be an admin (default: false)
 * @returns {Promise<Object>} The response from the Matrix server
 */
export const registerMatrixUser = async (username, password = null, admin = false) => {
  try {
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const registrationSharedSecret = process.env.REGISTRATION_SHARED_SECRET;

    if (!registrationSharedSecret) {
      throw new Error('REGISTRATION_SHARED_SECRET is not set');
    }

    // Generate a random nonce
    const getNonce = async () => {
      const response = await axios.get(`${matrixServerUrl}/_synapse/admin/v1/register`);
      return response.data.nonce;
    };

    const nonce = await getNonce();

    // Create the HMAC
    const hmac = crypto.createHmac('sha1', registrationSharedSecret);
    hmac.update(nonce);
    hmac.update('\0');
    hmac.update(username);
    hmac.update('\0');
    hmac.update(password || '');
    hmac.update('\0');
    hmac.update(admin ? 'admin' : 'notadmin');
    const mac = hmac.digest('hex');

    // Register the user
    const response = await axios.post(`${matrixServerUrl}/_synapse/admin/v1/register`, {
      nonce,
      username,
      password: password || undefined,
      admin,
      mac
    });

    return response.data;
  } catch (error) {
    console.error('Error registering Matrix user:', error);
    throw error;
  }
};

/**
 * Get a Matrix user by ID
 * @param {string} userId - The Matrix user ID (e.g., @user:domain)
 * @returns {Promise<Object>} The user object
 */
export const getMatrixUser = async (userId) => {
  try {
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const adminToken = await getAdminToken();

    const response = await axios.get(`${matrixServerUrl}/_synapse/admin/v2/users/${encodeURIComponent(userId)}`, {
      headers: {
        Authorization: `Bearer ${adminToken}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error getting Matrix user:', error);
    throw error;
  }
};

/**
 * Create a Matrix room
 * @param {string} name - The name of the room
 * @param {string} topic - The topic of the room (optional)
 * @param {boolean} isPublic - Whether the room is public (default: false)
 * @param {string} creatorUserId - The Matrix user ID of the room creator
 * @returns {Promise<Object>} The response from the Matrix server
 */
export const createMatrixRoom = async (name, topic = '', isPublic = false, creatorUserId) => {
  try {
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const adminToken = await getAdminToken();

    const response = await axios.post(`${matrixServerUrl}/_matrix/client/r0/createRoom`, {
      name,
      topic,
      visibility: isPublic ? 'public' : 'private',
      preset: isPublic ? 'public_chat' : 'private_chat',
      creation_content: {
        'm.federate': false
      },
      initial_state: [
        {
          type: 'm.room.history_visibility',
          state_key: '',
          content: {
            history_visibility: 'shared'
          }
        }
      ]
    }, {
      headers: {
        Authorization: `Bearer ${adminToken}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error creating Matrix room:', error);
    throw error;
  }
};

/**
 * Invite a user to a Matrix room
 * @param {string} roomId - The Matrix room ID
 * @param {string} userId - The Matrix user ID to invite
 * @returns {Promise<Object>} The response from the Matrix server
 */
export const inviteToMatrixRoom = async (roomId, userId) => {
  try {
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const adminToken = await getAdminToken();

    const response = await axios.post(`${matrixServerUrl}/_matrix/client/r0/rooms/${encodeURIComponent(roomId)}/invite`, {
      user_id: userId
    }, {
      headers: {
        Authorization: `Bearer ${adminToken}`
      }
    });

    return response.data;
  } catch (error) {
    console.error('Error inviting user to Matrix room:', error);
    throw error;
  }
};

/**
 * Get an admin access token for Matrix Synapse
 * @returns {Promise<string>} The admin access token
 */
let adminToken = null;
let adminTokenExpiry = 0;

export const getAdminToken = async () => {
  try {
    // Check if we have a valid token
    if (adminToken && adminTokenExpiry > Date.now()) {
      return adminToken;
    }

    // Check if MATRIX_ADMIN_TOKEN is set in environment variables
    if (process.env.MATRIX_ADMIN_TOKEN) {
      adminToken = process.env.MATRIX_ADMIN_TOKEN;
      // Set token expiry to 1 hour from now
      adminTokenExpiry = Date.now() + 3600000;
      return adminToken;
    }

    // If MATRIX_ADMIN_TOKEN is not set, try to login with username and password
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const adminUsername = process.env.MATRIX_ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.MATRIX_ADMIN_PASSWORD;

    if (!adminPassword) {
      throw new Error('Neither MATRIX_ADMIN_TOKEN nor MATRIX_ADMIN_PASSWORD is set');
    }

    // Login as admin
    const response = await axios.post(`${matrixServerUrl}/_matrix/client/r0/login`, {
      type: 'm.login.password',
      identifier: {
        type: 'm.id.user',
        user: adminUsername
      },
      password: adminPassword
    });

    adminToken = response.data.access_token;
    // Set token expiry to 1 hour from now
    adminTokenExpiry = Date.now() + 3600000;

    return adminToken;
  } catch (error) {
    console.error('Error getting admin token:', error);
    throw error;
  }
};

/**
 * Convert a NxtAcre user ID to a Matrix user ID
 * @param {string} userId - The NxtAcre user ID
 * @returns {string} The Matrix user ID
 */
export const getNxtAcreUserIdToMatrixUserId = (userId) => {
  const matrixDomain = process.env.MATRIX_DOMAIN || 'nxtacre.local';
  return `@nxtacre_${userId}:${matrixDomain}`;
};

/**
 * Convert a Matrix user ID to a NxtAcre user ID
 * @param {string} matrixUserId - The Matrix user ID
 * @returns {string} The NxtAcre user ID
 */
export const getMatrixUserIdToNxtAcreUserId = (matrixUserId) => {
  // Extract the username part (without @ and domain)
  const username = matrixUserId.split(':')[0].substring(1);
  // Remove the 'nxtacre_' prefix
  return username.startsWith('nxtacre_') ? username.substring(8) : username;
};

/**
 * Set the display name for a Matrix user
 * @param {string} userId - The Matrix user ID
 * @param {string} displayName - The display name to set
 * @returns {Promise<Object>} The response from the Matrix server
 */
export const setDisplayName = async (userId, displayName) => {
  try {
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const adminToken = await getAdminToken();

    // Use the admin API to set the display name
    const response = await axios.put(
      `${matrixServerUrl}/_synapse/admin/v2/users/${encodeURIComponent(userId)}/displayname`,
      { displayname: displayName },
      {
        headers: {
          Authorization: `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error(`Error setting display name for ${userId}:`, error);
    throw error;
  }
};

/**
 * Set the avatar URL for a Matrix user
 * @param {string} userId - The Matrix user ID
 * @param {string} avatarUrl - The avatar URL to set
 * @returns {Promise<Object>} The response from the Matrix server
 */
export const setAvatarUrl = async (userId, avatarUrl) => {
  try {
    const matrixServerUrl = process.env.MATRIX_SERVER_URL || 'http://synapse:8008';
    const adminToken = await getAdminToken();

    // Use the admin API to set the avatar URL
    const response = await axios.put(
      `${matrixServerUrl}/_synapse/admin/v2/users/${encodeURIComponent(userId)}/avatar_url`,
      { avatar_url: avatarUrl },
      {
        headers: {
          Authorization: `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response.data;
  } catch (error) {
    console.error(`Error setting avatar URL for ${userId}:`, error);
    throw error;
  }
};

export default {
  registerMatrixUser,
  getMatrixUser,
  createMatrixRoom,
  inviteToMatrixRoom,
  getAdminToken,
  getNxtAcreUserIdToMatrixUserId,
  getMatrixUserIdToNxtAcreUserId,
  setDisplayName,
  setAvatarUrl
};
