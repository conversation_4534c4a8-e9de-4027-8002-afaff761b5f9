/**
 * Utility functions for handling database connection retries
 */

/**
 * Executes a database operation with retry logic for connection errors
 * 
 * @param {Function} operation - The database operation to execute
 * @param {Object} options - Options for the retry mechanism
 * @param {number} options.maxRetries - Maximum number of retry attempts (default: 3)
 * @param {number} options.retryDelay - Delay between retry attempts in milliseconds (default: 1000)
 * @param {Function} options.onRetry - Callback function called before each retry attempt
 * @returns {Promise<any>} - The result of the database operation
 */
export const withDatabaseRetry = async (operation, options = {}) => {
  const maxRetries = options.maxRetries || 3;
  const retryDelay = options.retryDelay || 1000;
  const onRetry = options.onRetry || ((error, attempt) => {
    console.warn(`Database connection error (attempt ${attempt}/${maxRetries}):`, error.message);
    console.log(`Retrying in ${retryDelay/1000} seconds...`);
  });

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      // Check if this is a connection error
      const isConnectionError = 
        error.name === 'SequelizeConnectionRefusedError' || 
        error.name === 'SequelizeConnectionError' ||
        error.name === 'SequelizeHostNotFoundError' ||
        error.name === 'SequelizeHostNotReachableError' ||
        error.name === 'SequelizeInvalidConnectionError' ||
        error.name === 'SequelizeConnectionTimedOutError';

      if (isConnectionError && attempt < maxRetries) {
        // Call the onRetry callback
        onRetry(error, attempt, maxRetries);
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        continue;
      }

      // If we've reached max retries or it's not a connection error, rethrow the error
      throw error;
    }
  }
};