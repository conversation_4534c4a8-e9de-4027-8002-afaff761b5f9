/**
 * Utility functions for managing cookies and sessions
 * This centralizes all cookie-related code to make management easier
 */

import dotenv from 'dotenv';

dotenv.config();

/**
 * Get the cookie domain with proper formatting
 * @returns {string} The cookie domain with leading dot for subdomain support
 */
export const getCookieDomain = () => {
  const mainDomain = process.env.VITE_MAIN_DOMAIN || 'nxtacre.com';
  return `.${mainDomain}`;
};

/**
 * Default cookie options for all cookies
 * @returns {Object} Default cookie options
 */
export const getDefaultCookieOptions = () => {
  const isProduction = process.env.NODE_ENV === 'production';

  return {
    domain: getCookieDomain(),
    path: '/', // Ensure cookie is available on all paths
    secure: true, // Only secure in production for HTTPS
    httpOnly: true,
    maxAge: 60 * 60 * 1000 // 1 hour in milliseconds
  };
};

/**
 * Set a secure cookie with consistent options
 * @param {Object} res - Express response object
 * @param {string} name - <PERSON>ie name
 * @param {string} value - Cookie value
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setSecureCookie = (res, name, value, options = {}) => {
  // For auth_token, set httpOnly to false to fix cross domain issues
  const cookieOptions = name === 'auth_token'
    ? { ...getDefaultCookieOptions(), httpOnly: false, ...options }
    : { ...getDefaultCookieOptions(), ...options };

  return res.cookie(name, value, cookieOptions);
};

/**
 * Set an authentication token cookie
 * @param {Object} res - Express response object
 * @param {string} token - Authentication token
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setAuthTokenCookie = (res, token, options = {}) => {
  return setSecureCookie(res, 'auth_token', token, { httpOnly: false, ...options });
};

/**
 * Set a refresh token cookie
 * @param {Object} res - Express response object
 * @param {string} refreshToken - Refresh token
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setRefreshTokenCookie = (res, refreshToken, options = {}) => {
  return setSecureCookie(res, 'refresh_token', refreshToken, options);
};

/**
 * Clear authentication cookies
 * @param {Object} res - Express response object
 * @returns {Object} Express response object
 */
export const clearAuthCookies = (res) => {
  const isProduction = process.env.NODE_ENV === 'production';
  const clearOptions = {
    domain: getCookieDomain(),
    path: '/', // Ensure cookie is cleared from all paths
    secure: true,
    sameSite: 'lax'
  };

  res.clearCookie('auth_token', clearOptions);
  res.clearCookie('refresh_token', clearOptions);
  res.clearCookie('impersonating', clearOptions);
  res.clearCookie('admin_id', clearOptions);

  return res;
};

/**
 * Set impersonation cookies
 * @param {Object} res - Express response object
 * @param {string} adminId - Admin user ID
 * @param {Object} options - Additional cookie options
 * @returns {Object} Express response object
 */
export const setImpersonationCookies = (res, adminId, options = {}) => {
  setSecureCookie(res, 'impersonating', 'true', options);
  setSecureCookie(res, 'admin_id', adminId, options);
  return res;
};

/**
 * Apply cookie middleware to Express app
 * This overrides the default res.cookie function to set consistent options
 * @param {Object} app - Express app
 */
export const applyCookieMiddleware = (app) => {
  app.use((req, res, next) => {
    // Store the original res.cookie function
    const originalCookie = res.cookie;

    // Override the res.cookie function to set default options
    res.cookie = function(name, value, options = {}) {
      // Set default options for all cookies
      const defaultOptions = {
        ...getDefaultCookieOptions(),
        ...options // Allow overriding defaults
      };

      // Ensure path is set to '/' for all cookies
      if (defaultOptions.path === undefined) {
        defaultOptions.path = '/';
      }

      // Ensure secure is set based on environment for cross-domain cookies
      const isProduction = process.env.NODE_ENV === 'production';
      if (defaultOptions.secure === undefined) {
        defaultOptions.secure = isProduction;
      }

      // Call the original cookie function with the new options
      return originalCookie.call(this, name, value, defaultOptions);
    };

    next();
  });
};
