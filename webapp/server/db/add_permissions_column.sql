-- Add permissions column to user_farms table

-- Set the search path to the site schema
SET search_path TO site;

-- Add permissions column if it doesn't exist
ALTER TABLE site.user_farms ADD COLUMN IF NOT EXISTS permissions JSONB;
COMMENT ON COLUMN site.user_farms.permissions IS 'Custom permissions for this role, overrides default role permissions';

-- Verify the column was added
DO $$
DECLARE
    column_exists BOOLEAN;
BEGIN
    SELECT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'site' 
        AND table_name = 'user_farms' 
        AND column_name = 'permissions'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'The permissions column was successfully added to the user_farms table.';
    ELSE
        RAISE EXCEPTION 'Failed to add the permissions column to the user_farms table.';
    END IF;
END $$;