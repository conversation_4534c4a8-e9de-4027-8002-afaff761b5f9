-- Create harvest_schedules table

-- Set the search path to the site schema
SET search_path TO site;

-- Enable the uuid-ossp extension if it's not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE TABLE IF NOT EXISTS harvest_schedules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  farm_id UUID NOT NULL REFERENCES farms(id) ON DELETE CASCADE,
  field_id UUID NOT NULL REFERENCES fields(id) ON DELETE CASCADE,
  crop_id UUID NOT NULL REFERENCES crops(id) ON DELETE CASCADE,
  scheduled_date DATE NOT NULL,
  status VARCHAR(20) NOT NULL DEFAULT 'planned',
  weather_dependent BOOLEAN DEFAULT TRUE,
  optimal_conditions JSONB,
  actual_start_date DATE,
  actual_end_date DATE,
  yield_amount DECIMAL(10, 2),
  yield_unit VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create index on farm_id
CREATE INDEX IF NOT EXISTS idx_harvest_schedules_farm_id ON harvest_schedules(farm_id);

-- Create index on field_id
CREATE INDEX IF NOT EXISTS idx_harvest_schedules_field_id ON harvest_schedules(field_id);

-- Create index on crop_id
CREATE INDEX IF NOT EXISTS idx_harvest_schedules_crop_id ON harvest_schedules(crop_id);

-- Create index on scheduled_date
CREATE INDEX IF NOT EXISTS idx_harvest_schedules_scheduled_date ON harvest_schedules(scheduled_date);

-- Create index on status
CREATE INDEX IF NOT EXISTS idx_harvest_schedules_status ON harvest_schedules(status);

-- Add comment to table
COMMENT ON TABLE harvest_schedules IS 'Stores harvest schedules for crops in fields';

-- Create enum type for harvest schedule status if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'harvest_schedule_status') THEN
        CREATE TYPE harvest_schedule_status AS ENUM ('planned', 'in_progress', 'completed', 'cancelled');
    END IF;
END$$;

-- Add check constraint for status
ALTER TABLE harvest_schedules ADD CONSTRAINT check_harvest_schedule_status 
  CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled'));
