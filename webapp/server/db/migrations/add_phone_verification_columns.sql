-- Add phone verification columns to users table

-- Set the search path to the site schema
SET search_path TO site;

-- Add phone_verified column
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS phone_verified BOOLEAN DEFAULT FALSE;
COMMENT ON COLUMN site.users.phone_verified IS 'Whether the user has verified their phone number';

-- Add phone_verification_code column
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS phone_verification_code VARCHAR(10);
COMMENT ON COLUMN site.users.phone_verification_code IS 'Temporary code for phone verification';

-- Add phone_verification_expires column
ALTER TABLE site.users ADD COLUMN IF NOT EXISTS phone_verification_expires TIMESTAMP WITH TIME ZONE;
COMMENT ON COLUMN site.users.phone_verification_expires IS 'Expiration time for phone verification code';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_users_phone_verified ON site.users(phone_verified);