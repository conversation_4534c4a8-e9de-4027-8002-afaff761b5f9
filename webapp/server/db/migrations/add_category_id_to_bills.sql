-- Migration: Add category_id column to bills table
-- Depends on:

SET search_path TO site;

-- Add category_id column to bills table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = 'site'
        AND table_name = 'bills'
        AND column_name = 'category_id'
    ) THEN
        ALTER TABLE site.bills ADD COLUMN category_id UUID REFERENCES site.bill_categories(id) ON DELETE SET NULL;
        
        -- Create index for the new column
        CREATE INDEX IF NOT EXISTS idx_bills_category_id ON site.bills(category_id);
    END IF;
END $$;