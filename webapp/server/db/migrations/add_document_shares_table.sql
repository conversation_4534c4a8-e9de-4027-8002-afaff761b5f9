-- Migration: Add document_shares table for public file sharing
-- Depends on: add_role_to_document_permissions.sql

SET search_path TO site;

-- Create document_shares table if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_schema = 'site'
        AND table_name = 'document_shares'
    ) THEN
        CREATE TABLE site.document_shares (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            document_id UUID NOT NULL REFERENCES site.documents(id) ON DELETE CASCADE,
            share_token VARCHAR(64) NOT NULL UNIQUE,
            expires_at TIMESTAMP WITH TIME ZONE,
            password_hash VARCHAR(255),
            created_by UUID NOT NULL REFERENCES site.users(id),
            farm_id UUID NOT NULL REFERENCES site.farms(id),
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        );

        -- Add indexes
        CREATE INDEX document_shares_document_idx ON site.document_shares(document_id);
        CREATE INDEX document_shares_token_idx ON site.document_shares(share_token);
        CREATE INDEX document_shares_expires_idx ON site.document_shares(expires_at);
        CREATE INDEX document_shares_farm_idx ON site.document_shares(farm_id);
        CREATE INDEX document_shares_created_by_idx ON site.document_shares(created_by);

        -- Add comments
        COMMENT ON TABLE site.document_shares IS 'Stores public share links for documents';
        COMMENT ON COLUMN site.document_shares.id IS 'Unique identifier for the share';
        COMMENT ON COLUMN site.document_shares.document_id IS 'Reference to the document being shared';
        COMMENT ON COLUMN site.document_shares.share_token IS 'Unique token used in the share URL';
        COMMENT ON COLUMN site.document_shares.expires_at IS 'When the share link expires (NULL for no expiration)';
        COMMENT ON COLUMN site.document_shares.password_hash IS 'Optional password hash for protected shares';
        COMMENT ON COLUMN site.document_shares.created_by IS 'User who created the share';
        COMMENT ON COLUMN site.document_shares.farm_id IS 'Farm the document belongs to';
        COMMENT ON COLUMN site.document_shares.created_at IS 'When the share was created';
        COMMENT ON COLUMN site.document_shares.updated_at IS 'When the share was last updated';

        RAISE NOTICE 'Successfully created document_shares table';
    ELSE
        RAISE NOTICE 'document_shares table already exists';
    END IF;
END $$;

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status, dependencies)
        VALUES (
            gen_random_uuid(),
            'Add document_shares table',
            'webapp/server/db/migrations/add_document_shares_table.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            NOW(),
            NOW(),
            NOW(),
            'applied',
            ARRAY['add_role_to_document_permissions.sql']
        );
    END IF;
END $$;