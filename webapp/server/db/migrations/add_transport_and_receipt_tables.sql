-- Migration: Add transport management and receipt management tables
-- Depends on: None

-- Set the search path to the site schema
SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
    RAISE NOTICE 'Migration step: %', step_name;
END;
$$ LANGUAGE plpgsql;

-- Begin transaction
BEGIN;

-- Step 1: Create drivers table
SELECT log_migration_step('Creating drivers table');

-- Create drivers table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.drivers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  user_id UUID REFERENCES site.users(id),
  first_name VA<PERSON>HA<PERSON>(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255),
  phone_number VA<PERSON>HA<PERSON>(20),
  license_number VA<PERSON>HA<PERSON>(50),
  license_expiry TIMESTAMP WITH TIME ZONE,
  vehicle_type VARCHAR(100),
  vehicle_plate VARCHAR(20),
  status VARCHAR(20) NOT NULL DEFAULT 'active',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.drivers DROP CONSTRAINT IF EXISTS drivers_status_check;
ALTER TABLE site.drivers ADD CONSTRAINT drivers_status_check 
  CHECK (status IN ('active', 'inactive', 'on_leave'));

-- Add comments to columns
COMMENT ON COLUMN site.drivers.id IS 'Unique identifier for the driver';
COMMENT ON COLUMN site.drivers.farm_id IS 'ID of the farm this driver belongs to';
COMMENT ON COLUMN site.drivers.user_id IS 'ID of the user account if the driver is a user in the system';
COMMENT ON COLUMN site.drivers.first_name IS 'First name of the driver';
COMMENT ON COLUMN site.drivers.last_name IS 'Last name of the driver';
COMMENT ON COLUMN site.drivers.email IS 'Email address of the driver';
COMMENT ON COLUMN site.drivers.phone_number IS 'Phone number of the driver';
COMMENT ON COLUMN site.drivers.license_number IS 'Driver license number';
COMMENT ON COLUMN site.drivers.license_expiry IS 'Expiration date of the driver license';
COMMENT ON COLUMN site.drivers.vehicle_type IS 'Type of vehicle the driver operates';
COMMENT ON COLUMN site.drivers.vehicle_plate IS 'License plate of the vehicle';
COMMENT ON COLUMN site.drivers.status IS 'Status of the driver: active, inactive, or on_leave';
COMMENT ON COLUMN site.drivers.notes IS 'Additional notes about the driver';
COMMENT ON COLUMN site.drivers.created_at IS 'Timestamp when the driver record was created';
COMMENT ON COLUMN site.drivers.updated_at IS 'Timestamp when the driver record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS drivers_farm_id_idx ON site.drivers(farm_id);
CREATE INDEX IF NOT EXISTS drivers_user_id_idx ON site.drivers(user_id);
CREATE INDEX IF NOT EXISTS drivers_status_idx ON site.drivers(status);
CREATE INDEX IF NOT EXISTS drivers_name_idx ON site.drivers(last_name, first_name);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_drivers_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_drivers_timestamp ON site.drivers;
CREATE TRIGGER update_drivers_timestamp
BEFORE UPDATE ON site.drivers
FOR EACH ROW EXECUTE FUNCTION update_drivers_updated_at();

-- Step 2: Create driver_locations table (depends on drivers)
SELECT log_migration_step('Creating driver_locations table');

-- Create driver_locations table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.driver_locations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  driver_id UUID NOT NULL REFERENCES site.drivers(id) ON DELETE CASCADE,
  latitude DECIMAL(10, 7) NOT NULL,
  longitude DECIMAL(10, 7) NOT NULL,
  accuracy DECIMAL(10, 2),
  altitude DECIMAL(10, 2),
  speed DECIMAL(10, 2),
  heading DECIMAL(5, 2),
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add comments to columns
COMMENT ON COLUMN site.driver_locations.id IS 'Unique identifier for the location record';
COMMENT ON COLUMN site.driver_locations.driver_id IS 'ID of the driver this location belongs to';
COMMENT ON COLUMN site.driver_locations.latitude IS 'Latitude coordinate';
COMMENT ON COLUMN site.driver_locations.longitude IS 'Longitude coordinate';
COMMENT ON COLUMN site.driver_locations.accuracy IS 'Accuracy of the location in meters';
COMMENT ON COLUMN site.driver_locations.altitude IS 'Altitude in meters above sea level';
COMMENT ON COLUMN site.driver_locations.speed IS 'Speed in meters per second';
COMMENT ON COLUMN site.driver_locations.heading IS 'Direction of travel in degrees (0-360)';
COMMENT ON COLUMN site.driver_locations.timestamp IS 'Time when the location was recorded';
COMMENT ON COLUMN site.driver_locations.created_at IS 'Timestamp when the record was created';
COMMENT ON COLUMN site.driver_locations.updated_at IS 'Timestamp when the record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS driver_locations_driver_id_idx ON site.driver_locations(driver_id);
CREATE INDEX IF NOT EXISTS driver_locations_timestamp_idx ON site.driver_locations(timestamp);
CREATE INDEX IF NOT EXISTS driver_locations_coordinates_idx ON site.driver_locations(latitude, longitude);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_driver_locations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_driver_locations_timestamp ON site.driver_locations;
CREATE TRIGGER update_driver_locations_timestamp
BEFORE UPDATE ON site.driver_locations
FOR EACH ROW EXECUTE FUNCTION update_driver_locations_updated_at();

-- Step 3: Create deliveries table (depends on drivers)
SELECT log_migration_step('Creating deliveries table');

-- Create deliveries table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.deliveries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID REFERENCES site.drivers(id),
  customer_id UUID REFERENCES site.customers(id),
  order_id UUID REFERENCES site.orders(id),
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  delivery_address TEXT NOT NULL,
  delivery_city VARCHAR(100) NOT NULL,
  delivery_state VARCHAR(50) NOT NULL,
  delivery_zip VARCHAR(20) NOT NULL,
  delivery_country VARCHAR(50) NOT NULL DEFAULT 'USA',
  delivery_instructions TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
  actual_delivery_date TIMESTAMP WITH TIME ZONE,
  signature_required BOOLEAN NOT NULL DEFAULT false,
  signature_image TEXT,
  signature_name VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.deliveries DROP CONSTRAINT IF EXISTS deliveries_status_check;
ALTER TABLE site.deliveries ADD CONSTRAINT deliveries_status_check 
  CHECK (status IN ('scheduled', 'in_transit', 'delivered', 'failed', 'cancelled'));

-- Add comments to columns
COMMENT ON COLUMN site.deliveries.id IS 'Unique identifier for the delivery';
COMMENT ON COLUMN site.deliveries.farm_id IS 'ID of the farm this delivery belongs to';
COMMENT ON COLUMN site.deliveries.driver_id IS 'ID of the driver assigned to this delivery';
COMMENT ON COLUMN site.deliveries.customer_id IS 'ID of the customer receiving this delivery';
COMMENT ON COLUMN site.deliveries.order_id IS 'ID of the order associated with this delivery';
COMMENT ON COLUMN site.deliveries.scheduled_date IS 'Scheduled date and time for the delivery';
COMMENT ON COLUMN site.deliveries.delivery_address IS 'Street address for delivery';
COMMENT ON COLUMN site.deliveries.delivery_city IS 'City for delivery';
COMMENT ON COLUMN site.deliveries.delivery_state IS 'State for delivery';
COMMENT ON COLUMN site.deliveries.delivery_zip IS 'ZIP/Postal code for delivery';
COMMENT ON COLUMN site.deliveries.delivery_country IS 'Country for delivery';
COMMENT ON COLUMN site.deliveries.delivery_instructions IS 'Special instructions for the delivery';
COMMENT ON COLUMN site.deliveries.status IS 'Status of the delivery: scheduled, in_transit, delivered, failed, or cancelled';
COMMENT ON COLUMN site.deliveries.actual_delivery_date IS 'Actual date and time of delivery';
COMMENT ON COLUMN site.deliveries.signature_required IS 'Whether a signature is required upon delivery';
COMMENT ON COLUMN site.deliveries.signature_image IS 'Base64 encoded image of the signature';
COMMENT ON COLUMN site.deliveries.signature_name IS 'Name of the person who signed for the delivery';
COMMENT ON COLUMN site.deliveries.notes IS 'Additional notes about the delivery';
COMMENT ON COLUMN site.deliveries.created_at IS 'Timestamp when the delivery record was created';
COMMENT ON COLUMN site.deliveries.updated_at IS 'Timestamp when the delivery record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS deliveries_farm_id_idx ON site.deliveries(farm_id);
CREATE INDEX IF NOT EXISTS deliveries_driver_id_idx ON site.deliveries(driver_id);
CREATE INDEX IF NOT EXISTS deliveries_customer_id_idx ON site.deliveries(customer_id);
CREATE INDEX IF NOT EXISTS deliveries_order_id_idx ON site.deliveries(order_id);
CREATE INDEX IF NOT EXISTS deliveries_status_idx ON site.deliveries(status);
CREATE INDEX IF NOT EXISTS deliveries_scheduled_date_idx ON site.deliveries(scheduled_date);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_deliveries_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_deliveries_timestamp ON site.deliveries;
CREATE TRIGGER update_deliveries_timestamp
BEFORE UPDATE ON site.deliveries
FOR EACH ROW EXECUTE FUNCTION update_deliveries_updated_at();

-- Step 4: Create pickups table (depends on drivers)
SELECT log_migration_step('Creating pickups table');

-- Create pickups table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.pickups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID REFERENCES site.drivers(id),
  supplier_id UUID REFERENCES site.suppliers(id),
  customer_id UUID REFERENCES site.customers(id),
  scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
  pickup_address TEXT NOT NULL,
  pickup_city VARCHAR(100) NOT NULL,
  pickup_state VARCHAR(50) NOT NULL,
  pickup_zip VARCHAR(20) NOT NULL,
  pickup_country VARCHAR(50) NOT NULL DEFAULT 'USA',
  pickup_instructions TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
  actual_pickup_date TIMESTAMP WITH TIME ZONE,
  signature_required BOOLEAN NOT NULL DEFAULT false,
  signature_image TEXT,
  signature_name VARCHAR(255),
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.pickups DROP CONSTRAINT IF EXISTS pickups_status_check;
ALTER TABLE site.pickups ADD CONSTRAINT pickups_status_check 
  CHECK (status IN ('scheduled', 'in_transit', 'completed', 'failed', 'cancelled'));

-- Add comments to columns
COMMENT ON COLUMN site.pickups.id IS 'Unique identifier for the pickup';
COMMENT ON COLUMN site.pickups.farm_id IS 'ID of the farm this pickup belongs to';
COMMENT ON COLUMN site.pickups.driver_id IS 'ID of the driver assigned to this pickup';
COMMENT ON COLUMN site.pickups.supplier_id IS 'ID of the supplier for this pickup';
COMMENT ON COLUMN site.pickups.customer_id IS 'ID of the customer for this pickup (if applicable)';
COMMENT ON COLUMN site.pickups.scheduled_date IS 'Scheduled date and time for the pickup';
COMMENT ON COLUMN site.pickups.pickup_address IS 'Street address for pickup';
COMMENT ON COLUMN site.pickups.pickup_city IS 'City for pickup';
COMMENT ON COLUMN site.pickups.pickup_state IS 'State for pickup';
COMMENT ON COLUMN site.pickups.pickup_zip IS 'ZIP/Postal code for pickup';
COMMENT ON COLUMN site.pickups.pickup_country IS 'Country for pickup';
COMMENT ON COLUMN site.pickups.pickup_instructions IS 'Special instructions for the pickup';
COMMENT ON COLUMN site.pickups.status IS 'Status of the pickup: scheduled, in_transit, completed, failed, or cancelled';
COMMENT ON COLUMN site.pickups.actual_pickup_date IS 'Actual date and time of pickup';
COMMENT ON COLUMN site.pickups.signature_required IS 'Whether a signature is required upon pickup';
COMMENT ON COLUMN site.pickups.signature_image IS 'Base64 encoded image of the signature';
COMMENT ON COLUMN site.pickups.signature_name IS 'Name of the person who signed for the pickup';
COMMENT ON COLUMN site.pickups.notes IS 'Additional notes about the pickup';
COMMENT ON COLUMN site.pickups.created_at IS 'Timestamp when the pickup record was created';
COMMENT ON COLUMN site.pickups.updated_at IS 'Timestamp when the pickup record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS pickups_farm_id_idx ON site.pickups(farm_id);
CREATE INDEX IF NOT EXISTS pickups_driver_id_idx ON site.pickups(driver_id);
CREATE INDEX IF NOT EXISTS pickups_supplier_id_idx ON site.pickups(supplier_id);
CREATE INDEX IF NOT EXISTS pickups_customer_id_idx ON site.pickups(customer_id);
CREATE INDEX IF NOT EXISTS pickups_status_idx ON site.pickups(status);
CREATE INDEX IF NOT EXISTS pickups_scheduled_date_idx ON site.pickups(scheduled_date);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_pickups_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_pickups_timestamp ON site.pickups;
CREATE TRIGGER update_pickups_timestamp
BEFORE UPDATE ON site.pickups
FOR EACH ROW EXECUTE FUNCTION update_pickups_updated_at();

-- Step 5: Create receipts table
SELECT log_migration_step('Creating receipts table');

-- Create receipts table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.receipts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  user_id UUID NOT NULL REFERENCES site.users(id),
  expense_id UUID REFERENCES site.expenses(id),
  receipt_date TIMESTAMP WITH TIME ZONE NOT NULL,
  vendor_name VARCHAR(255) NOT NULL,
  total_amount DECIMAL(10, 2) NOT NULL,
  tax_amount DECIMAL(10, 2),
  currency VARCHAR(3) NOT NULL DEFAULT 'USD',
  receipt_image TEXT,
  receipt_file_path VARCHAR(255),
  status VARCHAR(20) NOT NULL DEFAULT 'pending',
  approved_by UUID REFERENCES site.users(id),
  approved_at TIMESTAMP WITH TIME ZONE,
  rejection_reason TEXT,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.receipts DROP CONSTRAINT IF EXISTS receipts_status_check;
ALTER TABLE site.receipts ADD CONSTRAINT receipts_status_check 
  CHECK (status IN ('pending', 'approved', 'rejected'));

-- Add comments to columns
COMMENT ON COLUMN site.receipts.id IS 'Unique identifier for the receipt';
COMMENT ON COLUMN site.receipts.farm_id IS 'ID of the farm this receipt belongs to';
COMMENT ON COLUMN site.receipts.user_id IS 'ID of the user who submitted this receipt';
COMMENT ON COLUMN site.receipts.expense_id IS 'ID of the expense associated with this receipt';
COMMENT ON COLUMN site.receipts.receipt_date IS 'Date and time on the receipt';
COMMENT ON COLUMN site.receipts.vendor_name IS 'Name of the vendor or store';
COMMENT ON COLUMN site.receipts.total_amount IS 'Total amount on the receipt';
COMMENT ON COLUMN site.receipts.tax_amount IS 'Tax amount on the receipt';
COMMENT ON COLUMN site.receipts.currency IS 'Currency code (e.g., USD, EUR)';
COMMENT ON COLUMN site.receipts.receipt_image IS 'Base64 encoded image of the receipt';
COMMENT ON COLUMN site.receipts.receipt_file_path IS 'Path to the receipt file in storage';
COMMENT ON COLUMN site.receipts.status IS 'Status of the receipt: pending, approved, or rejected';
COMMENT ON COLUMN site.receipts.approved_by IS 'ID of the user who approved this receipt';
COMMENT ON COLUMN site.receipts.approved_at IS 'Date and time when the receipt was approved';
COMMENT ON COLUMN site.receipts.rejection_reason IS 'Reason for rejection if status is rejected';
COMMENT ON COLUMN site.receipts.notes IS 'Additional notes about the receipt';
COMMENT ON COLUMN site.receipts.created_at IS 'Timestamp when the receipt record was created';
COMMENT ON COLUMN site.receipts.updated_at IS 'Timestamp when the receipt record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS receipts_farm_id_idx ON site.receipts(farm_id);
CREATE INDEX IF NOT EXISTS receipts_user_id_idx ON site.receipts(user_id);
CREATE INDEX IF NOT EXISTS receipts_expense_id_idx ON site.receipts(expense_id);
CREATE INDEX IF NOT EXISTS receipts_status_idx ON site.receipts(status);
CREATE INDEX IF NOT EXISTS receipts_receipt_date_idx ON site.receipts(receipt_date);
CREATE INDEX IF NOT EXISTS receipts_approved_by_idx ON site.receipts(approved_by);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_receipts_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_receipts_timestamp ON site.receipts;
CREATE TRIGGER update_receipts_timestamp
BEFORE UPDATE ON site.receipts
FOR EACH ROW EXECUTE FUNCTION update_receipts_updated_at();

-- Step 6: Create driver_schedules table (depends on drivers, deliveries, and pickups)
SELECT log_migration_step('Creating driver_schedules table');

-- Create driver_schedules table if it doesn't exist
CREATE TABLE IF NOT EXISTS site.driver_schedules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  farm_id UUID NOT NULL REFERENCES site.farms(id),
  driver_id UUID NOT NULL REFERENCES site.drivers(id),
  schedule_date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  delivery_id UUID REFERENCES site.deliveries(id),
  pickup_id UUID REFERENCES site.pickups(id),
  activity_type VARCHAR(50) NOT NULL,
  activity_description TEXT,
  status VARCHAR(20) NOT NULL DEFAULT 'scheduled',
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add enum constraint for status
ALTER TABLE site.driver_schedules DROP CONSTRAINT IF EXISTS driver_schedules_status_check;
ALTER TABLE site.driver_schedules ADD CONSTRAINT driver_schedules_status_check 
  CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled'));

-- Add enum constraint for activity_type
ALTER TABLE site.driver_schedules DROP CONSTRAINT IF EXISTS driver_schedules_activity_type_check;
ALTER TABLE site.driver_schedules ADD CONSTRAINT driver_schedules_activity_type_check 
  CHECK (activity_type IN ('delivery', 'pickup', 'maintenance', 'break', 'other'));

-- Add comments to columns
COMMENT ON COLUMN site.driver_schedules.id IS 'Unique identifier for the schedule entry';
COMMENT ON COLUMN site.driver_schedules.farm_id IS 'ID of the farm this schedule belongs to';
COMMENT ON COLUMN site.driver_schedules.driver_id IS 'ID of the driver for this schedule';
COMMENT ON COLUMN site.driver_schedules.schedule_date IS 'Date of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.start_time IS 'Start time of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.end_time IS 'End time of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.delivery_id IS 'ID of the delivery associated with this schedule (if applicable)';
COMMENT ON COLUMN site.driver_schedules.pickup_id IS 'ID of the pickup associated with this schedule (if applicable)';
COMMENT ON COLUMN site.driver_schedules.activity_type IS 'Type of activity: delivery, pickup, maintenance, break, or other';
COMMENT ON COLUMN site.driver_schedules.activity_description IS 'Description of the scheduled activity';
COMMENT ON COLUMN site.driver_schedules.status IS 'Status of the schedule: scheduled, in_progress, completed, or cancelled';
COMMENT ON COLUMN site.driver_schedules.notes IS 'Additional notes about the schedule';
COMMENT ON COLUMN site.driver_schedules.created_at IS 'Timestamp when the schedule record was created';
COMMENT ON COLUMN site.driver_schedules.updated_at IS 'Timestamp when the schedule record was last updated';

-- Create indexes
CREATE INDEX IF NOT EXISTS driver_schedules_farm_id_idx ON site.driver_schedules(farm_id);
CREATE INDEX IF NOT EXISTS driver_schedules_driver_id_idx ON site.driver_schedules(driver_id);
CREATE INDEX IF NOT EXISTS driver_schedules_schedule_date_idx ON site.driver_schedules(schedule_date);
CREATE INDEX IF NOT EXISTS driver_schedules_delivery_id_idx ON site.driver_schedules(delivery_id);
CREATE INDEX IF NOT EXISTS driver_schedules_pickup_id_idx ON site.driver_schedules(pickup_id);
CREATE INDEX IF NOT EXISTS driver_schedules_status_idx ON site.driver_schedules(status);
CREATE INDEX IF NOT EXISTS driver_schedules_activity_type_idx ON site.driver_schedules(activity_type);

-- Create trigger for updated_at
CREATE OR REPLACE FUNCTION update_driver_schedules_updated_at()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = CURRENT_TIMESTAMP;
   RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_driver_schedules_timestamp ON site.driver_schedules;
CREATE TRIGGER update_driver_schedules_timestamp
BEFORE UPDATE ON site.driver_schedules
FOR EACH ROW EXECUTE FUNCTION update_driver_schedules_updated_at();

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
        INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at)
        VALUES (
            gen_random_uuid(), 
            'add_transport_and_receipt_tables', 
            'webapp/server/db/migrations/add_transport_and_receipt_tables.sql',
            (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP,
            CURRENT_TIMESTAMP
        );
    END IF;
END $$;

-- Commit transaction
COMMIT;

-- Verify all tables were created
DO $$
DECLARE
    missing_tables TEXT := '';
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'drivers') THEN
        missing_tables := missing_tables || 'drivers, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'driver_locations') THEN
        missing_tables := missing_tables || 'driver_locations, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'deliveries') THEN
        missing_tables := missing_tables || 'deliveries, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'pickups') THEN
        missing_tables := missing_tables || 'pickups, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'receipts') THEN
        missing_tables := missing_tables || 'receipts, ';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'driver_schedules') THEN
        missing_tables := missing_tables || 'driver_schedules, ';
    END IF;

    IF missing_tables <> '' THEN
        missing_tables := SUBSTRING(missing_tables, 1, LENGTH(missing_tables) - 2);
        RAISE EXCEPTION 'Migration failed. The following tables were not created: %', missing_tables;
    ELSE
        RAISE NOTICE 'Migration successful. All tables were created.';
    END IF;
END $$;
