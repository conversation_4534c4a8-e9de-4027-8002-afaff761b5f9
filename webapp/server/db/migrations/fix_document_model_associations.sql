-- Migration: Fix duplicate associations in document-related models
-- Depends on: add_document_signing_tables.sql
-- This migration documents the code changes made to fix duplicate associations in document-related models

SET search_path TO site;

-- Create a function to log migration progress
CREATE OR REPLACE FUNCTION log_migration_step(step_name TEXT)
RETURNS VOID AS $$
BEGIN
  RAISE NOTICE 'Migration step: %', step_name;
  RETURN;
END;
$$ LANGUAGE plpgsql;

-- Log the start of the migration
SELECT log_migration_step('Starting fix_document_model_associations migration');

-- This migration doesn't make any schema changes, it only fixes code-level issues
-- with duplicate associations in the document-related models.
-- The actual changes are made by the JavaScript migration file: fix_document_model_associations.js

-- Record the migration in the database_migrations table if it exists
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_schema = 'site' AND table_name = 'database_migrations') THEN
    INSERT INTO site.database_migrations (id, name, file_path, "order", applied_at, created_at, updated_at, status, dependencies)
    VALUES (
      uuid_generate_v4(),
      'Fix duplicate associations in document-related models',
      'webapp/server/db/migrations/fix_document_model_associations.sql',
      (SELECT COALESCE(MAX("order"), 0) + 1 FROM site.database_migrations),
      NOW(),
      NOW(),
      NOW(),
      'completed',
      ARRAY['add_document_signing_tables.sql']
    );
    
    SELECT log_migration_step('Migration recorded in database_migrations table');
  ELSE
    SELECT log_migration_step('database_migrations table does not exist, skipping recording');
  END IF;
END $$;

-- Log the completion of the migration
SELECT log_migration_step('Completed fix_document_model_associations migration');

-- Drop the logging function
DROP FUNCTION IF EXISTS log_migration_step;