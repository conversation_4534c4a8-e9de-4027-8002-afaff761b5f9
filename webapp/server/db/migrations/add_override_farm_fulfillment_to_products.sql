-- Migration: Add override_farm_fulfillment column to products table
-- Depends on: add_marketplace_visibility_to_products.sql

SET search_path TO site;

-- Add override_farm_fulfillment column if it doesn't exist
DO $$
DECLARE
    schema_name TEXT;
BEGIN
    -- Get the schema name from environment variable
    schema_name := current_setting('app.db_schema', true);

    -- If the environment variable is not set, use 'site' as default
    IF schema_name IS NULL THEN
        schema_name := 'site';
    END IF;

    -- Add override_farm_fulfillment column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'products'
        AND column_name = 'override_farm_fulfillment'
    ) THEN
        EXECUTE format('ALTER TABLE %I.products ADD COLUMN override_farm_fulfillment BOOLEAN NOT NULL DEFAULT false', schema_name);
        EXECUTE format('COMMENT ON COLUMN %I.products.override_farm_fulfillment IS ''Whether this product overrides farm-level fulfillment options''', schema_name);
    END IF;

    -- Add offers_delivery column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'products'
        AND column_name = 'offers_delivery'
    ) THEN
        EXECUTE format('ALTER TABLE %I.products ADD COLUMN offers_delivery BOOLEAN', schema_name);
        EXECUTE format('COMMENT ON COLUMN %I.products.offers_delivery IS ''Whether this product offers delivery (if overriding farm settings)''', schema_name);
    END IF;

    -- Add offers_pickup column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_schema = schema_name
        AND table_name = 'products'
        AND column_name = 'offers_pickup'
    ) THEN
        EXECUTE format('ALTER TABLE %I.products ADD COLUMN offers_pickup BOOLEAN', schema_name);
        EXECUTE format('COMMENT ON COLUMN %I.products.offers_pickup IS ''Whether this product offers pickup (if overriding farm settings)''', schema_name);
    END IF;
END $$;