-- Migration: Add farm collaboration features
-- Depends on:

SET search_path TO site;

-- Add recipient_farm_id to invoices table to allow farm-to-farm invoicing
ALTER TABLE invoices
ADD COLUMN recipient_farm_id UUID NULL,
ADD CONSTRAINT fk_invoices_recipient_farm FOREIGN KEY (recipient_farm_id) REFERENCES farms(id) ON DELETE CASCADE;

-- Create farm_field_collaborations table to track shared fields between farms
CREATE TABLE farm_field_collaborations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  field_id UUID NOT NULL,
  owner_farm_id UUID NOT NULL,
  collaborator_farm_id UUID NOT NULL,
  permission_level VARCHAR(50) NOT NULL DEFAULT 'read', -- 'read', 'write', 'admin'
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  CONSTRAINT fk_farm_field_collaborations_field FOREIGN KEY (field_id) REFERENCES fields(id) ON DELETE CASCADE,
  CONSTRAINT fk_farm_field_collaborations_owner_farm FOREIGN KEY (owner_farm_id) REFERENCES farms(id) ON DELETE CASCADE,
  CONSTRAINT fk_farm_field_collaborations_collaborator_farm FOREIGN KEY (collaborator_farm_id) REFERENCES farms(id) ON DELETE CASCADE,
  CONSTRAINT unique_field_collaboration UNIQUE (field_id, collaborator_farm_id)
);

-- Add index for faster lookups
CREATE INDEX idx_farm_field_collaborations_field_id ON farm_field_collaborations(field_id);
CREATE INDEX idx_farm_field_collaborations_owner_farm_id ON farm_field_collaborations(owner_farm_id);
CREATE INDEX idx_farm_field_collaborations_collaborator_farm_id ON farm_field_collaborations(collaborator_farm_id);

-- Add comment to explain the purpose of the table
COMMENT ON TABLE farm_field_collaborations IS 'Tracks fields shared between farms for collaboration';
COMMENT ON COLUMN farm_field_collaborations.permission_level IS 'Level of access granted to the collaborator farm: read, write, or admin';