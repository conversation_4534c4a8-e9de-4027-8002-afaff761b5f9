import dotenv from 'dotenv';

dotenv.config();

// Mock data for testing
const mockFarms = {
  farm1: { id: 'farm-123', name: 'Test Farm 1' },
  farm2: { id: 'farm-456', name: 'Test Farm 2' }
};

const mockUsers = {
  user1: { id: 'user-123', is_global_admin: false },
  user2: { id: 'user-456', is_global_admin: false },
  anonymousUser: null
};

const mockProducts = {
  product1: {
    id: 'product-123',
    farm_id: mockFarms.farm1.id,
    name: 'Organic Apples',
    price: 3.99,
    is_marketplace_visible: true
  },
  product2: {
    id: 'product-456',
    farm_id: mockFarms.farm1.id,
    name: 'Free Range Eggs',
    price: 5.99,
    is_marketplace_visible: true
  },
  product3: {
    id: 'product-789',
    farm_id: mockFarms.farm2.id,
    name: 'Grass-Fed Beef',
    price: 12.99,
    is_marketplace_visible: true
  }
};

const mockCarts = {
  cart1: {
    id: 'cart-123',
    user_id: mockUsers.user1.id,
    session_id: null,
    farm_id: null,
    is_saved: false,
    items: [
      {
        id: 'item-123',
        product_id: mockProducts.product1.id,
        quantity: 2
      },
      {
        id: 'item-456',
        product_id: mockProducts.product2.id,
        quantity: 1
      }
    ]
  },
  cart2: {
    id: 'cart-456',
    user_id: null,
    session_id: 'session-123',
    farm_id: null,
    is_saved: false,
    items: [
      {
        id: 'item-789',
        product_id: mockProducts.product3.id,
        quantity: 3
      }
    ]
  },
  savedCart: {
    id: 'cart-789',
    user_id: mockUsers.user1.id,
    session_id: null,
    farm_id: null,
    is_saved: true,
    items: [
      {
        id: 'item-101',
        product_id: mockProducts.product1.id,
        quantity: 5
      }
    ]
  }
};

const mockAddresses = {
  address1: {
    id: 'address-123',
    customer_id: 'customer-123',
    farm_id: mockFarms.farm1.id,
    address: '123 Main St',
    city: 'Farmville',
    state: 'CA',
    zip_code: '12345'
  }
};

// Mock request objects
const createMockRequest = (user = null, body = {}, params = {}, sessionId = null) => ({
  user,
  body,
  params,
  sessionId,
  cookies: {
    cartSessionId: sessionId
  }
});

// Mock response object
const createMockResponse = () => {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    statusCode: 200,
    jsonData: null,
    cookieData: null
  };
  
  res.status.mockImplementation((code) => {
    res.statusCode = code;
    return res;
  });
  
  res.json.mockImplementation((data) => {
    res.jsonData = data;
    return res;
  });
  
  res.cookie.mockImplementation((name, value, options) => {
    res.cookieData = { name, value, options };
    return res;
  });
  
  return res;
};

// Test add item to cart functionality
const testAddItemToCart = () => {
  console.log('Testing add item to cart functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user adds item to cart',
      user: mockUsers.user1,
      body: { productId: mockProducts.product3.id, quantity: 2 },
      sessionId: null,
      expectedStatus: 200,
      expectedResult: 'success',
      expectedItemCount: 3
    },
    {
      name: 'Anonymous user adds item to cart',
      user: null,
      body: { productId: mockProducts.product1.id, quantity: 1 },
      sessionId: 'session-456',
      expectedStatus: 200,
      expectedResult: 'success',
      expectedItemCount: 1
    },
    {
      name: 'Add item with invalid product ID',
      user: mockUsers.user1,
      body: { productId: 'invalid-product-id', quantity: 1 },
      sessionId: null,
      expectedStatus: 404,
      expectedResult: 'error',
      expectedItemCount: 0
    },
    {
      name: 'Add item with invalid quantity',
      user: mockUsers.user1,
      body: { productId: mockProducts.product1.id, quantity: -1 },
      sessionId: null,
      expectedStatus: 400,
      expectedResult: 'error',
      expectedItemCount: 0
    },
    {
      name: 'Add item with quantity exceeding limit',
      user: mockUsers.user1,
      body: { productId: mockProducts.product1.id, quantity: 101 },
      sessionId: null,
      expectedStatus: 400,
      expectedResult: 'error',
      expectedItemCount: 0
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, body, sessionId, expectedStatus, expectedResult, expectedItemCount } = testCase;
    
    // Simulate add item to cart logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualItemCount = 0;
    let cart = null;
    
    // Validate product ID
    const product = Object.values(mockProducts).find(p => p.id === body.productId);
    if (!product) {
      actualStatus = 404;
      actualResult = 'error';
    }
    
    // Validate quantity
    else if (body.quantity <= 0 || body.quantity > 100) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Process the request
    else {
      // Find or create cart
      if (user) {
        cart = Object.values(mockCarts).find(c => c.user_id === user.id && !c.is_saved);
      } else if (sessionId) {
        cart = Object.values(mockCarts).find(c => c.session_id === sessionId);
      }
      
      if (!cart) {
        // Create new cart
        cart = {
          id: `cart-new-${index}`,
          user_id: user ? user.id : null,
          session_id: user ? null : sessionId,
          farm_id: null,
          is_saved: false,
          items: []
        };
      }
      
      // Check if product already in cart
      const existingItem = cart.items.find(item => item.product_id === body.productId);
      
      if (existingItem) {
        // Update quantity
        existingItem.quantity += body.quantity;
      } else {
        // Add new item
        cart.items.push({
          id: `item-new-${index}`,
          product_id: body.productId,
          quantity: body.quantity
        });
      }
      
      actualItemCount = cart.items.length;
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (actualResult === 'error' || actualItemCount === expectedItemCount);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, item count ${expectedItemCount}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, item count ${actualItemCount} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Add item to cart test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test get current cart functionality
const testGetCurrentCart = () => {
  console.log('\nTesting get current cart functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user gets their cart',
      user: mockUsers.user1,
      sessionId: null,
      expectedStatus: 200,
      expectedResult: 'success',
      expectedCartId: mockCarts.cart1.id,
      expectedItemCount: 2
    },
    {
      name: 'Anonymous user gets their cart by session ID',
      user: null,
      sessionId: 'session-123',
      expectedStatus: 200,
      expectedResult: 'success',
      expectedCartId: mockCarts.cart2.id,
      expectedItemCount: 1
    },
    {
      name: 'New anonymous user gets empty cart',
      user: null,
      sessionId: 'new-session',
      expectedStatus: 200,
      expectedResult: 'success',
      expectedCartId: null,
      expectedItemCount: 0
    },
    {
      name: 'New authenticated user gets empty cart',
      user: { id: 'new-user', is_global_admin: false },
      sessionId: null,
      expectedStatus: 200,
      expectedResult: 'success',
      expectedCartId: null,
      expectedItemCount: 0
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, sessionId, expectedStatus, expectedResult, expectedCartId, expectedItemCount } = testCase;
    
    // Simulate get current cart logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualCartId = null;
    let actualItemCount = 0;
    
    // Find cart
    let cart = null;
    
    if (user) {
      cart = Object.values(mockCarts).find(c => c.user_id === user.id && !c.is_saved);
    } else if (sessionId) {
      cart = Object.values(mockCarts).find(c => c.session_id === sessionId);
    }
    
    if (cart) {
      actualCartId = cart.id;
      actualItemCount = cart.items.length;
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedCartId === null || actualCartId === expectedCartId) &&
                       actualItemCount === expectedItemCount;
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, cart ID ${expectedCartId || 'null'}, item count ${expectedItemCount}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, cart ID ${actualCartId || 'null'}, item count ${actualItemCount} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Get current cart test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test update cart item functionality
const testUpdateCartItem = () => {
  console.log('\nTesting update cart item functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user updates their cart item',
      user: mockUsers.user1,
      params: { itemId: mockCarts.cart1.items[0].id },
      body: { quantity: 5 },
      sessionId: null,
      expectedStatus: 200,
      expectedResult: 'success',
      expectedQuantity: 5
    },
    {
      name: 'Anonymous user updates their cart item',
      user: null,
      params: { itemId: mockCarts.cart2.items[0].id },
      body: { quantity: 2 },
      sessionId: 'session-123',
      expectedStatus: 200,
      expectedResult: 'success',
      expectedQuantity: 2
    },
    {
      name: 'Update with invalid item ID',
      user: mockUsers.user1,
      params: { itemId: 'invalid-item-id' },
      body: { quantity: 3 },
      sessionId: null,
      expectedStatus: 404,
      expectedResult: 'error',
      expectedQuantity: null
    },
    {
      name: 'Update with invalid quantity',
      user: mockUsers.user1,
      params: { itemId: mockCarts.cart1.items[0].id },
      body: { quantity: -1 },
      sessionId: null,
      expectedStatus: 400,
      expectedResult: 'error',
      expectedQuantity: null
    },
    {
      name: 'User cannot update item in another user\'s cart',
      user: mockUsers.user2,
      params: { itemId: mockCarts.cart1.items[0].id },
      body: { quantity: 3 },
      sessionId: null,
      expectedStatus: 404,
      expectedResult: 'error',
      expectedQuantity: null
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, params, body, sessionId, expectedStatus, expectedResult, expectedQuantity } = testCase;
    
    // Simulate update cart item logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualQuantity = null;
    
    // Find cart
    let cart = null;
    
    if (user) {
      cart = Object.values(mockCarts).find(c => c.user_id === user.id && !c.is_saved);
    } else if (sessionId) {
      cart = Object.values(mockCarts).find(c => c.session_id === sessionId);
    }
    
    // Find item
    let item = null;
    
    if (cart) {
      item = cart.items.find(i => i.id === params.itemId);
    }
    
    if (!item) {
      actualStatus = 404;
      actualResult = 'error';
    }
    
    // Validate quantity
    else if (body.quantity <= 0 || body.quantity > 100) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Update item
    else {
      item.quantity = body.quantity;
      actualQuantity = item.quantity;
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedQuantity === null || actualQuantity === expectedQuantity);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, quantity ${expectedQuantity || 'null'}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, quantity ${actualQuantity || 'null'} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Update cart item test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test remove cart item functionality
const testRemoveCartItem = () => {
  console.log('\nTesting remove cart item functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user removes item from their cart',
      user: mockUsers.user1,
      params: { itemId: mockCarts.cart1.items[0].id },
      sessionId: null,
      expectedStatus: 200,
      expectedResult: 'success',
      expectedItemCount: 1
    },
    {
      name: 'Anonymous user removes item from their cart',
      user: null,
      params: { itemId: mockCarts.cart2.items[0].id },
      sessionId: 'session-123',
      expectedStatus: 200,
      expectedResult: 'success',
      expectedItemCount: 0
    },
    {
      name: 'Remove with invalid item ID',
      user: mockUsers.user1,
      params: { itemId: 'invalid-item-id' },
      sessionId: null,
      expectedStatus: 404,
      expectedResult: 'error',
      expectedItemCount: null
    },
    {
      name: 'User cannot remove item from another user\'s cart',
      user: mockUsers.user2,
      params: { itemId: mockCarts.cart1.items[1].id },
      sessionId: null,
      expectedStatus: 404,
      expectedResult: 'error',
      expectedItemCount: null
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, params, sessionId, expectedStatus, expectedResult, expectedItemCount } = testCase;
    
    // Simulate remove cart item logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualItemCount = null;
    
    // Find cart
    let cart = null;
    
    if (user) {
      cart = Object.values(mockCarts).find(c => c.user_id === user.id && !c.is_saved);
    } else if (sessionId) {
      cart = Object.values(mockCarts).find(c => c.session_id === sessionId);
    }
    
    // Find item
    let itemIndex = -1;
    
    if (cart) {
      itemIndex = cart.items.findIndex(i => i.id === params.itemId);
    }
    
    if (itemIndex === -1) {
      actualStatus = 404;
      actualResult = 'error';
    }
    
    // Remove item
    else {
      cart.items.splice(itemIndex, 1);
      actualItemCount = cart.items.length;
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedItemCount === null || actualItemCount === expectedItemCount);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, item count ${expectedItemCount || 'null'}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, item count ${actualItemCount || 'null'} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Remove cart item test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test save cart functionality
const testSaveCart = () => {
  console.log('\nTesting save cart functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user saves their cart',
      user: mockUsers.user1,
      params: { cartId: mockCarts.cart1.id },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedSaved: true
    },
    {
      name: 'Anonymous user cannot save cart',
      user: null,
      params: { cartId: mockCarts.cart2.id },
      expectedStatus: 401,
      expectedResult: 'error',
      expectedSaved: null
    },
    {
      name: 'Save with invalid cart ID',
      user: mockUsers.user1,
      params: { cartId: 'invalid-cart-id' },
      expectedStatus: 404,
      expectedResult: 'error',
      expectedSaved: null
    },
    {
      name: 'User cannot save another user\'s cart',
      user: mockUsers.user2,
      params: { cartId: mockCarts.cart1.id },
      expectedStatus: 403,
      expectedResult: 'error',
      expectedSaved: null
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, params, expectedStatus, expectedResult, expectedSaved } = testCase;
    
    // Simulate save cart logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualSaved = null;
    
    // Check if user is authenticated
    if (!user) {
      actualStatus = 401;
      actualResult = 'error';
    }
    
    else {
      // Find cart
      const cart = Object.values(mockCarts).find(c => c.id === params.cartId);
      
      if (!cart) {
        actualStatus = 404;
        actualResult = 'error';
      }
      
      // Check if user owns the cart
      else if (cart.user_id !== user.id) {
        actualStatus = 403;
        actualResult = 'error';
      }
      
      // Save cart
      else {
        cart.is_saved = true;
        actualSaved = cart.is_saved;
      }
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       (expectedSaved === null || actualSaved === expectedSaved);
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, saved ${expectedSaved || 'null'}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, saved ${actualSaved || 'null'} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Save cart test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test checkout functionality
const testCheckout = () => {
  console.log('\nTesting checkout functionality...\n');
  
  const testCases = [
    {
      name: 'Authenticated user checks out',
      user: mockUsers.user1,
      body: {
        fulfillmentMethod: 'delivery',
        deliveryAddressId: mockAddresses.address1.id,
        notes: 'Please deliver in the morning'
      },
      sessionId: null,
      expectedStatus: 200,
      expectedResult: 'success',
      expectedPurchaseRequestCreated: true
    },
    {
      name: 'Anonymous user checks out',
      user: null,
      body: {
        customerInfo: {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '************'
        },
        fulfillmentMethod: 'pickup',
        pickupDate: '2023-06-01T10:00:00Z',
        notes: 'Will pick up in the afternoon'
      },
      sessionId: 'session-123',
      expectedStatus: 200,
      expectedResult: 'success',
      expectedPurchaseRequestCreated: true
    },
    {
      name: 'Checkout with empty cart fails',
      user: { id: 'new-user', is_global_admin: false },
      body: {
        fulfillmentMethod: 'delivery',
        deliveryAddressId: mockAddresses.address1.id
      },
      sessionId: null,
      expectedStatus: 400,
      expectedResult: 'error',
      expectedPurchaseRequestCreated: false
    },
    {
      name: 'Checkout with missing delivery address fails',
      user: mockUsers.user1,
      body: {
        fulfillmentMethod: 'delivery'
      },
      sessionId: null,
      expectedStatus: 400,
      expectedResult: 'error',
      expectedPurchaseRequestCreated: false
    },
    {
      name: 'Checkout with missing pickup date fails',
      user: mockUsers.user1,
      body: {
        fulfillmentMethod: 'pickup'
      },
      sessionId: null,
      expectedStatus: 400,
      expectedResult: 'error',
      expectedPurchaseRequestCreated: false
    },
    {
      name: 'Anonymous checkout without customer info fails',
      user: null,
      body: {
        fulfillmentMethod: 'pickup',
        pickupDate: '2023-06-01T10:00:00Z'
      },
      sessionId: 'session-123',
      expectedStatus: 400,
      expectedResult: 'error',
      expectedPurchaseRequestCreated: false
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { user, body, sessionId, expectedStatus, expectedResult, expectedPurchaseRequestCreated } = testCase;
    
    // Simulate checkout logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualPurchaseRequestCreated = false;
    
    // Find cart
    let cart = null;
    
    if (user) {
      cart = Object.values(mockCarts).find(c => c.user_id === user.id && !c.is_saved);
    } else if (sessionId) {
      cart = Object.values(mockCarts).find(c => c.session_id === sessionId);
    }
    
    // Check if cart exists and has items
    if (!cart || cart.items.length === 0) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Validate fulfillment method
    else if (!body.fulfillmentMethod || !['delivery', 'pickup'].includes(body.fulfillmentMethod)) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Validate delivery address
    else if (body.fulfillmentMethod === 'delivery' && !body.deliveryAddressId) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Validate pickup date
    else if (body.fulfillmentMethod === 'pickup' && !body.pickupDate) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Validate customer info for anonymous users
    else if (!user && (!body.customerInfo || !body.customerInfo.name || !body.customerInfo.email)) {
      actualStatus = 400;
      actualResult = 'error';
    }
    
    // Create purchase request
    else {
      // Group items by farm
      const itemsByFarm = {};
      
      cart.items.forEach(item => {
        const product = Object.values(mockProducts).find(p => p.id === item.product_id);
        if (product) {
          if (!itemsByFarm[product.farm_id]) {
            itemsByFarm[product.farm_id] = [];
          }
          itemsByFarm[product.farm_id].push({
            product_id: item.product_id,
            quantity: item.quantity,
            price: product.price
          });
        }
      });
      
      // Create purchase request for each farm
      Object.keys(itemsByFarm).forEach(farmId => {
        const purchaseRequest = {
          id: `purchase-request-${index}-${farmId}`,
          user_id: user ? user.id : null,
          customer_id: user ? `customer-${user.id}` : null,
          farm_id: farmId,
          status: 'pending',
          notes: body.notes || null,
          fulfillment_method: body.fulfillmentMethod,
          pickup_date: body.pickupDate || null,
          delivery_address_id: body.deliveryAddressId || null,
          items: itemsByFarm[farmId]
        };
        
        // If anonymous user, create customer
        if (!user && body.customerInfo) {
          purchaseRequest.customer_info = body.customerInfo;
        }
        
        actualPurchaseRequestCreated = true;
      });
      
      // Clear cart after successful checkout
      cart.items = [];
    }
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       actualPurchaseRequestCreated === expectedPurchaseRequestCreated;
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, purchase request created ${expectedPurchaseRequestCreated}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, purchase request created ${actualPurchaseRequestCreated} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Checkout test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Test multi-farm order handling
const testMultiFarmOrderHandling = () => {
  console.log('\nTesting multi-farm order handling...\n');
  
  // Create a multi-farm cart for testing
  const multiFarmCart = {
    id: 'cart-multi',
    user_id: mockUsers.user1.id,
    session_id: null,
    farm_id: null,
    is_saved: false,
    items: [
      {
        id: 'item-multi-1',
        product_id: mockProducts.product1.id, // Farm 1
        quantity: 2
      },
      {
        id: 'item-multi-2',
        product_id: mockProducts.product3.id, // Farm 2
        quantity: 3
      }
    ]
  };
  
  const testCases = [
    {
      name: 'Multi-farm cart checkout creates separate purchase requests',
      cart: multiFarmCart,
      user: mockUsers.user1,
      body: {
        fulfillmentMethod: 'delivery',
        deliveryAddressId: mockAddresses.address1.id,
        notes: 'Multi-farm order test'
      },
      expectedStatus: 200,
      expectedResult: 'success',
      expectedPurchaseRequestCount: 2
    }
  ];
  
  let allTestsPassed = true;
  
  testCases.forEach((testCase, index) => {
    console.log(`${index + 1}. ${testCase.name}`);
    
    const { cart, user, body, expectedStatus, expectedResult, expectedPurchaseRequestCount } = testCase;
    
    // Simulate multi-farm checkout logic
    let actualStatus = 200;
    let actualResult = 'success';
    let actualPurchaseRequestCount = 0;
    const purchaseRequests = [];
    
    // Group items by farm
    const itemsByFarm = {};
    
    cart.items.forEach(item => {
      const product = Object.values(mockProducts).find(p => p.id === item.product_id);
      if (product) {
        if (!itemsByFarm[product.farm_id]) {
          itemsByFarm[product.farm_id] = [];
        }
        itemsByFarm[product.farm_id].push({
          product_id: item.product_id,
          quantity: item.quantity,
          price: product.price
        });
      }
    });
    
    // Create purchase request for each farm
    Object.keys(itemsByFarm).forEach(farmId => {
      const purchaseRequest = {
        id: `purchase-request-multi-${index}-${farmId}`,
        user_id: user.id,
        customer_id: `customer-${user.id}`,
        farm_id: farmId,
        status: 'pending',
        notes: body.notes || null,
        fulfillment_method: body.fulfillmentMethod,
        pickup_date: body.pickupDate || null,
        delivery_address_id: body.deliveryAddressId || null,
        items: itemsByFarm[farmId]
      };
      
      purchaseRequests.push(purchaseRequest);
    });
    
    actualPurchaseRequestCount = purchaseRequests.length;
    
    const testPassed = actualStatus === expectedStatus && 
                       actualResult === expectedResult && 
                       actualPurchaseRequestCount === expectedPurchaseRequestCount;
    
    console.log(`   Expected: status ${expectedStatus}, result ${expectedResult}, purchase request count ${expectedPurchaseRequestCount}`);
    console.log(`   Actual: status ${actualStatus}, result ${actualResult}, purchase request count ${actualPurchaseRequestCount} ${testPassed ? '✅' : '❌'}`);
    
    if (!testPassed) {
      allTestsPassed = false;
    }
  });
  
  console.log(`\n${allTestsPassed ? '✅' : '❌'} Multi-farm order handling test ${allTestsPassed ? 'passed' : 'failed'}`);
  return allTestsPassed;
};

// Run all tests
const runShoppingCartTests = () => {
  console.log('Starting shopping cart tests...\n');
  
  try {
    const addItemPassed = testAddItemToCart();
    const getCurrentCartPassed = testGetCurrentCart();
    const updateCartItemPassed = testUpdateCartItem();
    const removeCartItemPassed = testRemoveCartItem();
    const saveCartPassed = testSaveCart();
    const checkoutPassed = testCheckout();
    const multiFarmPassed = testMultiFarmOrderHandling();
    
    const allTestsPassed = addItemPassed && 
                          getCurrentCartPassed && 
                          updateCartItemPassed && 
                          removeCartItemPassed && 
                          saveCartPassed && 
                          checkoutPassed &&
                          multiFarmPassed;
    
    if (allTestsPassed) {
      console.log('\n🎉 All shopping cart tests passed!');
      console.log('✅ Add item to cart functionality works correctly');
      console.log('✅ Get current cart functionality works correctly');
      console.log('✅ Update cart item functionality works correctly');
      console.log('✅ Remove cart item functionality works correctly');
      console.log('✅ Save cart functionality works correctly');
      console.log('✅ Checkout functionality works correctly');
      console.log('✅ Multi-farm order handling works correctly');
    } else {
      console.log('\n❌ Some shopping cart tests failed. Please check the implementation.');
      process.exit(1);
    }
  } catch (error) {
    console.error('Test execution failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (process.argv[1].endsWith('shoppingCartTest.js')) {
  runShoppingCartTests();
}

export { 
  testAddItemToCart, 
  testGetCurrentCart, 
  testUpdateCartItem, 
  testRemoveCartItem, 
  testSaveCart, 
  testCheckout,
  testMultiFarmOrderHandling,
  runShoppingCartTests 
};