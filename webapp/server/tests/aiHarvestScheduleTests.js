// AI Harvest Schedule Analysis Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

// We need a farm ID and field ID for testing
let farmId;
let fieldId;
let cropId;
let createdAnalysisId;

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Setup: Get a farm, field, and crop for testing
const setupFarmFieldAndCrop = async () => {
  console.log('Setting up a farm, field, and crop for testing');
  
  // Try to get existing farms
  const farms = await makeRequest('GET', '/api/farms');
  
  if (farms.length > 0) {
    // Use the first farm
    farmId = farms[0].id;
    console.log(`Using existing farm with ID: ${farmId}`);
    
    // Try to get fields for this farm
    const fields = await makeRequest('GET', `/api/fields/farm/${farmId}`);
    
    if (fields.length > 0) {
      // Use the first field
      fieldId = fields[0].id;
      console.log(`Using existing field with ID: ${fieldId}`);
      
      // Try to get crops for this field
      const crops = await makeRequest('GET', `/api/crops/field/${fieldId}`);
      
      if (crops.length > 0) {
        // Use the first crop
        cropId = crops[0].id;
        console.log(`Using existing crop with ID: ${cropId}`);
        return;
      }
    }
  }
  
  console.log('No suitable farm, field, or crop found for testing. Please create them first.');
  throw new Error('No suitable farm, field, or crop found for testing');
};

// Test functions
const testGenerateHarvestScheduleAnalysis = async () => {
  console.log('Testing POST /api/ai-analysis/harvest-schedule');
  
  const testData = {
    farmId: farmId,
    fieldId: fieldId,
    cropId: cropId,
    harvestData: {
      plantingDate: new Date().toISOString().split('T')[0],
      cropMaturityData: {
        daysToMaturity: 90,
        growingDegreeDays: 2000
      }
    }
  };
  
  const result = await makeRequest('POST', '/api/ai-analysis/harvest-schedule', testData);
  
  assert(result.analysis, 'Response should have an analysis field');
  assert(result.analysis.id, 'Analysis should have an ID');
  assert(result.analysis.farm_id === farmId, 'Analysis should have the correct farm ID');
  assert(result.analysis.field_id === fieldId, 'Analysis should have the correct field ID');
  assert(result.analysis.crop_id === cropId, 'Analysis should have the correct crop ID');
  
  console.log(`Generated harvest schedule analysis with ID: ${result.analysis.id}`);
  createdAnalysisId = result.analysis.id;
  
  return result.analysis;
};

const testGetLatestHarvestScheduleAnalysis = async () => {
  console.log(`Testing GET /api/ai-analysis/harvest-schedule/${farmId}`);
  
  const result = await makeRequest('GET', `/api/ai-analysis/harvest-schedule/${farmId}?fieldId=${fieldId}&cropId=${cropId}`);
  
  assert(result.analysis, 'Response should have an analysis field');
  assert(result.analysis.farm_id === farmId, 'Analysis should have the correct farm ID');
  assert(result.analysis.field_id === fieldId, 'Analysis should have the correct field ID');
  assert(result.analysis.crop_id === cropId, 'Analysis should have the correct crop ID');
  
  console.log(`Retrieved latest harvest schedule analysis for field: ${result.analysis.id}`);
  
  return result.analysis;
};

const testGetAllHarvestScheduleAnalyses = async () => {
  console.log(`Testing GET /api/ai-analysis/harvest-schedule/${farmId}/all`);
  
  const result = await makeRequest('GET', `/api/ai-analysis/harvest-schedule/${farmId}/all`);
  
  assert(Array.isArray(result.analyses), 'Response should have an analyses array');
  assert(result.analyses.length > 0, 'There should be at least one analysis');
  
  console.log(`Retrieved ${result.analyses.length} harvest schedule analyses for farm`);
  
  return result.analyses;
};

const testApplyHarvestScheduleAnalysis = async () => {
  console.log(`Testing PUT /api/ai-analysis/harvest-schedule/${createdAnalysisId}`);
  
  const result = await makeRequest('PUT', `/api/ai-analysis/harvest-schedule/${createdAnalysisId}`, {
    isApplied: true
  });
  
  assert(result.analysis, 'Response should have an analysis field');
  assert(result.analysis.id === createdAnalysisId, 'Analysis ID should match');
  assert(result.analysis.is_applied === true, 'Analysis should be marked as applied');
  
  console.log(`Applied harvest schedule analysis: ${result.analysis.id}`);
  
  return result.analysis;
};

const testRunHarvestScheduleAnalysisForFarm = async () => {
  console.log(`Testing POST /api/ai-analysis/harvest-schedule/${farmId}/run`);
  
  const result = await makeRequest('POST', `/api/ai-analysis/harvest-schedule/${farmId}/run`);
  
  assert(result.message, 'Response should have a message field');
  assert(result.analyses !== undefined, 'Response should have an analyses field');
  
  console.log(`Run harvest schedule analysis for farm: ${result.message}`);
  
  return result;
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Harvest Schedule Analysis tests...');
    
    // Setup: Get a farm, field, and crop
    await setupFarmFieldAndCrop();
    
    // Generate a harvest schedule analysis
    await testGenerateHarvestScheduleAnalysis();
    
    // Get the latest harvest schedule analysis
    await testGetLatestHarvestScheduleAnalysis();
    
    // Get all harvest schedule analyses
    await testGetAllHarvestScheduleAnalyses();
    
    // Apply the harvest schedule analysis
    await testApplyHarvestScheduleAnalysis();
    
    // Run harvest schedule analysis for the farm
    await testRunHarvestScheduleAnalysisForFarm();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testGenerateHarvestScheduleAnalysis,
  testGetLatestHarvestScheduleAnalysis,
  testGetAllHarvestScheduleAnalyses,
  testApplyHarvestScheduleAnalysis,
  testRunHarvestScheduleAnalysisForFarm,
  setupFarmFieldAndCrop
};