// AI Model CRUD Operations Test Script

const axios = require('axios');
const assert = require('assert');

// Configuration
const API_URL = process.env.API_URL || 'http://localhost:3000';
const TOKEN = process.env.TOKEN; // Set this to a valid global admin token

// We need a provider ID for testing models
let providerId;
let createdModelId;

// Test data
const testModel = {
  name: 'Test Model',
  model_identifier: 'test-model-v1',
  description: 'A model for testing purposes',
  capabilities: ['text-generation', 'summarization'],
  max_tokens: 4096,
  is_enabled: true
};

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null) => {
  try {
    const config = {
      headers: {
        Authorization: `Bearer ${TOKEN}`
      }
    };

    let response;
    if (method === 'GET') {
      response = await axios.get(`${API_URL}${endpoint}`, config);
    } else if (method === 'POST') {
      response = await axios.post(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'PUT') {
      response = await axios.put(`${API_URL}${endpoint}`, data, config);
    } else if (method === 'DELETE') {
      response = await axios.delete(`${API_URL}${endpoint}`, config);
    }

    return response.data;
  } catch (error) {
    console.error(`Error making ${method} request to ${endpoint}:`, error.response?.data || error.message);
    throw error;
  }
};

// Setup: Get or create a provider for testing
const setupProvider = async () => {
  console.log('Setting up a provider for model tests');
  
  // Try to get existing providers
  const providers = await makeRequest('GET', '/api/ai-configuration/providers');
  
  if (providers.length > 0) {
    // Use the first provider
    providerId = providers[0].id;
    console.log(`Using existing provider with ID: ${providerId}`);
  } else {
    // Create a new provider
    const provider = await makeRequest('POST', '/api/ai-configuration/providers', {
      name: 'Test Provider for Models',
      description: 'A provider for testing model operations',
      api_base_url: 'https://api.testprovider.com',
      auth_type: 'api_key'
    });
    
    providerId = provider.id;
    console.log(`Created new provider with ID: ${providerId}`);
  }
  
  // Update test model with provider ID
  testModel.provider_id = providerId;
};

// Test functions
const testGetModels = async () => {
  console.log('Testing GET /api/ai-configuration/models');
  const models = await makeRequest('GET', '/api/ai-configuration/models');
  assert(Array.isArray(models), 'Response should be an array');
  console.log(`Found ${models.length} models`);
  return models;
};

const testGetModelsByProvider = async () => {
  console.log(`Testing GET /api/ai-configuration/providers/${providerId}/models`);
  const models = await makeRequest('GET', `/api/ai-configuration/providers/${providerId}/models`);
  assert(Array.isArray(models), 'Response should be an array');
  console.log(`Found ${models.length} models for provider ${providerId}`);
  return models;
};

const testCreateModel = async () => {
  console.log('Testing POST /api/ai-configuration/models');
  const model = await makeRequest('POST', '/api/ai-configuration/models', testModel);
  assert(model.id, 'Created model should have an ID');
  assert(model.name === testModel.name, 'Created model should have the correct name');
  assert(model.provider_id === providerId, 'Created model should have the correct provider ID');
  console.log(`Created model with ID: ${model.id}`);
  createdModelId = model.id;
  return model;
};

const testGetModelById = async () => {
  console.log(`Testing GET /api/ai-configuration/models/${createdModelId}`);
  const model = await makeRequest('GET', `/api/ai-configuration/models/${createdModelId}`);
  assert(model.id === createdModelId, 'Model ID should match');
  assert(model.name === testModel.name, 'Model name should match');
  console.log(`Retrieved model: ${model.name}`);
  return model;
};

const testUpdateModel = async () => {
  console.log(`Testing PUT /api/ai-configuration/models/${createdModelId}`);
  const updatedData = {
    ...testModel,
    name: 'Updated Test Model',
    description: 'An updated model for testing purposes',
    max_tokens: 8192
  };
  const model = await makeRequest('PUT', `/api/ai-configuration/models/${createdModelId}`, updatedData);
  assert(model.id === createdModelId, 'Model ID should match');
  assert(model.name === updatedData.name, 'Model name should be updated');
  assert(model.description === updatedData.description, 'Model description should be updated');
  assert(model.max_tokens === updatedData.max_tokens, 'Model max_tokens should be updated');
  console.log(`Updated model: ${model.name}`);
  return model;
};

const testDeleteModel = async () => {
  console.log(`Testing DELETE /api/ai-configuration/models/${createdModelId}`);
  const result = await makeRequest('DELETE', `/api/ai-configuration/models/${createdModelId}`);
  assert(result.message, 'Response should have a message');
  console.log(`Deleted model: ${result.message}`);
  
  // Verify the model is deleted
  try {
    await makeRequest('GET', `/api/ai-configuration/models/${createdModelId}`);
    assert(false, 'Model should not exist after deletion');
  } catch (error) {
    assert(error.response.status === 404, 'Should get a 404 error');
    console.log('Verified model is deleted');
  }
  
  return result;
};

// Cleanup: Delete the test provider if we created one
const cleanupProvider = async () => {
  // Only delete the provider if we created it specifically for this test
  if (providerId && (await makeRequest('GET', `/api/ai-configuration/providers/${providerId}`)).name === 'Test Provider for Models') {
    console.log(`Cleaning up: Deleting test provider with ID: ${providerId}`);
    await makeRequest('DELETE', `/api/ai-configuration/providers/${providerId}`);
    console.log('Test provider deleted');
  }
};

// Run all tests
const runTests = async () => {
  try {
    console.log('Starting AI Model CRUD tests...');
    
    // Setup: Get or create a provider
    await setupProvider();
    
    // Get initial models
    await testGetModels();
    
    // Get models by provider
    await testGetModelsByProvider();
    
    // Create a new model
    await testCreateModel();
    
    // Get the model by ID
    await testGetModelById();
    
    // Update the model
    await testUpdateModel();
    
    // Delete the model
    await testDeleteModel();
    
    // Verify models list again
    await testGetModels();
    
    // Cleanup
    await cleanupProvider();
    
    console.log('All tests passed!');
  } catch (error) {
    console.error('Test failed:', error);
    
    // Try to clean up even if tests fail
    try {
      if (createdModelId) {
        await makeRequest('DELETE', `/api/ai-configuration/models/${createdModelId}`);
      }
      await cleanupProvider();
    } catch (cleanupError) {
      console.error('Error during cleanup:', cleanupError);
    }
    
    process.exit(1);
  }
};

// Run the tests if this file is executed directly
if (require.main === module) {
  if (!TOKEN) {
    console.error('Please set the TOKEN environment variable to a valid global admin token');
    process.exit(1);
  }
  
  runTests();
}

module.exports = {
  runTests,
  testGetModels,
  testGetModelsByProvider,
  testCreateModel,
  testGetModelById,
  testUpdateModel,
  testDeleteModel,
  setupProvider,
  cleanupProvider
};