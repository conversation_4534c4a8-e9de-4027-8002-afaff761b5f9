import InvoiceNotification from '../models/InvoiceNotification.js';
import NotificationPreference from '../models/NotificationPreference.js';
import User from '../models/User.js';
import UserFarm from '../models/UserFarm.js';
import { sendInvoiceEmail } from '../utils/emailUtils.js';
import { sendSMS } from '../utils/smsUtils.js';

/**
 * Service for managing invoice-related notifications
 */
class InvoiceNotificationService {
  /**
   * Create and send a notification
   * @param {Object} params - Notification parameters
   * @param {string} params.invoiceId - Invoice ID
   * @param {string} params.recipientFarmId - Farm to notify
   * @param {string} params.senderFarmId - Farm that triggered the notification
   * @param {string} params.notificationType - Type of notification
   * @param {string} params.title - Notification title
   * @param {string} params.message - Notification message
   * @param {string} params.priority - Priority level
   * @param {Array} params.channels - Notification channels
   * @param {string} params.actionUrl - Action URL
   * @param {Object} params.metadata - Additional metadata
   * @param {string} params.recipientUserId - Specific user to notify (optional)
   * @returns {Promise<Object>} Created notification
   */
  static async createNotification({
    invoiceId,
    recipientFarmId,
    senderFarmId,
    notificationType,
    title,
    message,
    priority = 'medium',
    channels = ['in_app'],
    actionUrl = null,
    metadata = {},
    recipientUserId = null
  }) {
    try {
      // Create the notification record
      const notification = await InvoiceNotification.create({
        invoice_id: invoiceId,
        recipient_user_id: recipientUserId,
        recipient_farm_id: recipientFarmId,
        sender_farm_id: senderFarmId,
        notification_type: notificationType,
        title,
        message,
        priority,
        channels,
        action_url: actionUrl,
        metadata
      });

      // Send notifications through specified channels
      await this.sendNotificationChannels(notification);

      return notification;
    } catch (error) {
      console.error('Error creating invoice notification:', error);
      throw error;
    }
  }

  /**
   * Send notifications through specified channels
   * @param {Object} notification - Notification object
   */
  static async sendNotificationChannels(notification) {
    const channels = notification.channels || ['in_app'];

    // Get recipients
    const recipients = await this.getNotificationRecipients(
      notification.recipient_farm_id,
      notification.recipient_user_id
    );

    for (const channel of channels) {
      switch (channel) {
        case 'email':
          await this.sendEmailNotifications(notification, recipients);
          break;
        case 'sms':
          await this.sendSMSNotifications(notification, recipients);
          break;
        case 'push':
          await this.sendPushNotifications(notification, recipients);
          break;
        case 'in_app':
          // In-app notifications are handled by the database record itself
          break;
      }
    }
  }

  /**
   * Get notification recipients for a farm
   * @param {string} farmId - Farm ID
   * @param {string} specificUserId - Specific user ID (optional)
   * @returns {Promise<Array>} Array of users to notify
   */
  static async getNotificationRecipients(farmId, specificUserId = null) {
    if (specificUserId) {
      // Notify specific user
      const user = await User.findByPk(specificUserId, {
        include: [
          {
            model: NotificationPreference,
            as: 'notificationPreferences'
          }
        ]
      });
      return user ? [user] : [];
    }

    // Notify all farm users with appropriate roles
    const userFarms = await UserFarm.findAll({
      where: {
        farm_id: farmId,
        is_approved: true
      },
      include: [
        {
          model: User,
          as: 'user',
          include: [
            {
              model: NotificationPreference,
              as: 'notificationPreferences'
            }
          ]
        }
      ]
    });

    return userFarms
      .map(uf => uf.user)
      .filter(user => user && user.email); // Only users with email addresses
  }

  /**
   * Send email notifications
   * @param {Object} notification - Notification object
   * @param {Array} recipients - Array of recipient users
   */
  static async sendEmailNotifications(notification, recipients) {
    for (const recipient of recipients) {
      try {
        // Check if user has email notifications enabled
        const prefs = recipient.notificationPreferences;
        if (!prefs || !prefs.enable_email) {
          continue;
        }

        const emailData = {
          to: recipient.email,
          subject: notification.title,
          html: this.generateEmailHTML(notification, recipient),
          text: notification.message
        };

        // For now, just log the email that would be sent
        console.log('Email notification would be sent:', emailData);

        // Update notification record
        await notification.update({
          email_sent: true,
          email_sent_at: new Date()
        });

      } catch (error) {
        console.error(`Error sending email notification to ${recipient.email}:`, error);
      }
    }
  }

  /**
   * Send SMS notifications
   * @param {Object} notification - Notification object
   * @param {Array} recipients - Array of recipient users
   */
  static async sendSMSNotifications(notification, recipients) {
    for (const recipient of recipients) {
      try {
        if (!recipient.phone) continue;

        // Check if user has SMS notifications enabled (assuming this preference exists)
        const prefs = recipient.notificationPreferences;
        if (!prefs || !prefs.enable_sms) {
          continue;
        }

        await sendSMS({
          to: recipient.phone,
          message: `${notification.title}: ${notification.message}`
        });

        // Update notification record
        await notification.update({
          sms_sent: true,
          sms_sent_at: new Date()
        });

      } catch (error) {
        console.error(`Error sending SMS notification to ${recipient.phone}:`, error);
      }
    }
  }

  /**
   * Send push notifications
   * @param {Object} notification - Notification object
   * @param {Array} recipients - Array of recipient users
   */
  static async sendPushNotifications(notification, recipients) {
    // Implementation would depend on your push notification service
    // This is a placeholder for push notification logic
    try {
      // Update notification record
      await notification.update({
        push_sent: true,
        push_sent_at: new Date()
      });
    } catch (error) {
      console.error('Error sending push notifications:', error);
    }
  }

  /**
   * Generate HTML content for email notifications
   * @param {Object} notification - Notification object
   * @param {Object} recipient - Recipient user
   * @returns {string} HTML content
   */
  static generateEmailHTML(notification, recipient) {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">${notification.title}</h2>
        <p>Hello ${recipient.first_name},</p>
        <p>${notification.message}</p>
        ${notification.action_url ? `
          <div style="margin: 20px 0;">
            <a href="${notification.action_url}" 
               style="background-color: #2563eb; color: white; padding: 10px 20px; 
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              View Invoice
            </a>
          </div>
        ` : ''}
        <p style="color: #666; font-size: 12px; margin-top: 30px;">
          This is an automated notification from your farm management system.
        </p>
      </div>
    `;
  }

  /**
   * Notify when invoice is received
   */
  static async notifyInvoiceReceived(invoice, senderFarm, recipientFarm) {
    // Create the standard notification for in-app display
    const notification = await this.createNotification({
      invoiceId: invoice.id,
      recipientFarmId: recipientFarm.id,
      senderFarmId: senderFarm.id,
      notificationType: 'invoice_received',
      title: `New Invoice Received from ${senderFarm.name}`,
      message: `You have received invoice #${invoice.invoice_number} for $${invoice.total_amount} from ${senderFarm.name}.`,
      priority: 'medium',
      channels: ['in_app'], // Only in-app notification for users
      actionUrl: `/invoices/${invoice.id}`,
      metadata: {
        invoice_number: invoice.invoice_number,
        amount: invoice.total_amount,
        sender_farm_name: senderFarm.name
      }
    });

    // If the recipient farm has a billing email, send the invoice notification to that email
    if (recipientFarm.billing_email) {
      try {
        const emailData = {
          to: recipientFarm.billing_email,
          subject: `New Invoice Received from ${senderFarm.name}`,
          html: this.generateEmailHTML({
            title: `New Invoice Received from ${senderFarm.name}`,
            message: `You have received invoice #${invoice.invoice_number} for $${invoice.total_amount} from ${senderFarm.name}.`,
            action_url: `/invoices/${invoice.id}`
          }, { first_name: recipientFarm.name }), // Use farm name as recipient name
          text: `You have received invoice #${invoice.invoice_number} for $${invoice.total_amount} from ${senderFarm.name}.`
        };

        // Send the email
        await sendInvoiceEmail(emailData);

        // Update notification record to track that email was sent
        await notification.update({
          email_sent: true,
          email_sent_at: new Date(),
          metadata: {
            ...notification.metadata,
            billing_email_used: recipientFarm.billing_email
          }
        });

        console.log(`Invoice notification email sent to farm billing email: ${recipientFarm.billing_email}`);
      } catch (error) {
        console.error(`Error sending invoice notification to farm billing email ${recipientFarm.billing_email}:`, error);
      }
    } else {
      // If no billing email, fall back to sending to farm users
      const recipients = await this.getNotificationRecipients(recipientFarm.id);
      await this.sendEmailNotifications(notification, recipients);
    }

    return notification;
  }

  /**
   * Notify when invoice is viewed
   */
  static async notifyInvoiceViewed(invoice, viewerFarm, senderFarm) {
    return this.createNotification({
      invoiceId: invoice.id,
      recipientFarmId: senderFarm.id,
      senderFarmId: viewerFarm.id,
      notificationType: 'invoice_viewed',
      title: `Invoice #${invoice.invoice_number} Viewed`,
      message: `${viewerFarm.name} has viewed your invoice #${invoice.invoice_number}.`,
      priority: 'low',
      channels: ['in_app'],
      actionUrl: `/invoices/${invoice.id}`,
      metadata: {
        invoice_number: invoice.invoice_number,
        viewer_farm_name: viewerFarm.name
      }
    });
  }

  /**
   * Notify when invoice is paid
   */
  static async notifyInvoicePaid(invoice, payerFarm, senderFarm) {
    return this.createNotification({
      invoiceId: invoice.id,
      recipientFarmId: senderFarm.id,
      senderFarmId: payerFarm.id,
      notificationType: 'invoice_paid',
      title: `Payment Received for Invoice #${invoice.invoice_number}`,
      message: `${payerFarm.name} has paid invoice #${invoice.invoice_number} for $${invoice.total_amount}.`,
      priority: 'high',
      channels: ['in_app', 'email'],
      actionUrl: `/invoices/${invoice.id}`,
      metadata: {
        invoice_number: invoice.invoice_number,
        amount: invoice.total_amount,
        payer_farm_name: payerFarm.name
      }
    });
  }

  /**
   * Notify when invoice is overdue
   */
  static async notifyInvoiceOverdue(invoice, recipientFarm, senderFarm) {
    return this.createNotification({
      invoiceId: invoice.id,
      recipientFarmId: recipientFarm.id,
      senderFarmId: senderFarm.id,
      notificationType: 'invoice_overdue',
      title: `Invoice #${invoice.invoice_number} is Overdue`,
      message: `Invoice #${invoice.invoice_number} for $${invoice.total_amount} is now overdue. Please make payment as soon as possible.`,
      priority: 'high',
      channels: ['in_app', 'email'],
      actionUrl: `/invoices/${invoice.id}`,
      metadata: {
        invoice_number: invoice.invoice_number,
        amount: invoice.total_amount,
        due_date: invoice.due_date
      }
    });
  }

  /**
   * Mark notification as read
   * @param {string} notificationId - Notification ID
   * @param {string} userId - User ID
   */
  static async markAsRead(notificationId, userId) {
    const notification = await InvoiceNotification.findByPk(notificationId);
    if (notification && (notification.recipient_user_id === userId || !notification.recipient_user_id)) {
      await notification.update({
        read: true,
        read_at: new Date()
      });
    }
    return notification;
  }

  /**
   * Get notifications for a user/farm
   * @param {string} farmId - Farm ID
   * @param {string} userId - User ID (optional)
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Notifications
   */
  static async getNotifications(farmId, userId = null, options = {}) {
    const {
      limit = 20,
      offset = 0,
      unreadOnly = false,
      notificationType = null
    } = options;

    const where = {
      recipient_farm_id: farmId
    };

    if (userId) {
      where[Op.or] = [
        { recipient_user_id: userId },
        { recipient_user_id: null }
      ];
    }

    if (unreadOnly) {
      where.read = false;
    }

    if (notificationType) {
      where.notification_type = notificationType;
    }

    return await InvoiceNotification.findAll({
      where,
      include: [
        {
          model: Invoice,
          as: 'invoice',
          attributes: ['id', 'invoice_number', 'total_amount', 'status']
        },
        {
          model: Farm,
          as: 'senderFarm',
          attributes: ['id', 'name']
        }
      ],
      order: [['created_at', 'DESC']],
      limit,
      offset
    });
  }
}

export default InvoiceNotificationService;
