// Import from our wrapper module to ensure proper type resolution
import { WebSocket, WebSocketServer } from './wsWrapper.js';
import jwt from 'jsonwebtoken';
import UserOnlineStatus from '../models/UserOnlineStatus.js';
import ChatMessage from '../models/ChatMessage.js';
import ChatConversation from '../models/ChatConversation.js';
import { isServerShuttingDown } from '../index.js';

/**
 * Initialize WebSocket server for chat
 * @param {Object} server - HTTP server instance
 */
function initChatWebSocketServer(server) {
  const wss = new WebSocketServer({
    server,
    path: '/ws/chat'
  });

  // Store connected clients with their user IDs
  const clients = new Map();

  // Flag to track if the server is closing
  let isClosing = false;

  wss.on('connection', async (ws, req) => {
    let userId = null;

    // Extract token from query parameters
    const url = new URL(req.url, 'http://localhost');
    const token = url.searchParams.get('token');

    if (!token) {
      ws.close(4001, 'Authentication token is required');
      return;
    }

    try {
      // Verify JWT token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      userId = decoded.userId;

      // Add client to the map
      clients.set(userId, ws);

      // Set user as online using SafeUserOnlineStatus
      await UserOnlineStatus.setOnline(userId);

      // Send initial connection success message
      ws.send(JSON.stringify({
        type: 'connection_established',
        userId
      }));

      console.log(`User ${userId} connected to chat WebSocket`);

      // Handle incoming messages
      ws.on('message', async (message) => {
        // Don't process messages if the server is closing
        if (isClosing) {
          console.warn('Cannot process WebSocket message: server is closing');
          return;
        }

        try {
          const data = JSON.parse(message);

          switch (data.type) {
            case 'ping':
              // Update last active timestamp using SafeUserOnlineStatus
              await UserOnlineStatus.updateLastActive(userId);

              // Check if server is closing before sending response
              if (!isClosing && !isServerShuttingDown() && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ type: 'pong' }));
              }
              break;

            case 'new_message':
              await handleNewMessage(data, userId);
              break;

            case 'typing':
              await handleTypingIndicator(data, userId);
              break;

            case 'read_messages':
              await handleReadMessages(data, userId);
              break;

            default:
              console.warn(`Unknown message type: ${data.type}`);
          }
        } catch (error) {
          console.error('Error handling WebSocket message:', error);

          // Check if server is closing before sending error response
          if (!isClosing && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Failed to process message'
            }));
          }
        }
      });

      // Handle disconnection
      ws.on('close', async () => {
        // Remove client from the map
        clients.delete(userId);

        // Set user as offline only if server is not shutting down
        if (!isClosing && !isServerShuttingDown()) {
          await UserOnlineStatus.setOffline(userId);
        }

        console.log(`User ${userId} disconnected from chat WebSocket`);
      });

    } catch (error) {
      console.error('WebSocket authentication error:', error);
      ws.close(4003, 'Authentication failed');
    }
  });

  /**
   * Handle new message
   * @param {Object} data - Message data
   * @param {string} senderId - Sender user ID
   */
  async function handleNewMessage(data, senderId) {
    // Don't process messages if the server is closing
    if (isClosing) {
      console.warn('Cannot handle new message: WebSocket server is closing');
      return;
    }

    try {
      const { conversation_id, content, message_type, parent_message_id } = data;

      // Check if user is a participant in the conversation
      const isParticipant = await ChatConversation.isParticipant(conversation_id, senderId);

      if (!isParticipant) {
        throw new Error('User is not a participant in this conversation');
      }

      // Create the message
      const message = await ChatMessage.create({
        conversation_id,
        sender_id: senderId,
        message_type: message_type || 'text',
        content,
        parent_message_id
      });

      // Get the message with sender info
      const messageWithSender = await ChatMessage.getById(message.id);

      // Update conversation's updated_at timestamp
      await ChatConversation.update(conversation_id, {});

      // Check again if server is closing before sending messages
      if (isClosing) {
        console.warn('Cannot send new message: WebSocket server is closing');
        return;
      }

      // Get all participants in the conversation
      const participants = await ChatConversation.getParticipants(conversation_id);

      // Send the message to all online participants
      for (const participant of participants) {
        const recipientId = participant.user_id;
        const recipientWs = clients.get(recipientId);

        if (recipientWs && recipientWs.readyState === WebSocket.OPEN) {
          recipientWs.send(JSON.stringify({
            type: 'new_message',
            message: messageWithSender
          }));
        }
      }
    } catch (error) {
      console.error('Error handling new message:', error);
      throw error;
    }
  }

  /**
   * Handle typing indicator
   * @param {Object} data - Typing data
   * @param {string} userId - User ID
   */
  async function handleTypingIndicator(data, userId) {
    // Don't process typing indicators if the server is closing
    if (isClosing) {
      console.warn('Cannot handle typing indicator: WebSocket server is closing');
      return;
    }

    try {
      const { conversation_id, is_typing } = data;

      // Check if user is a participant in the conversation
      const isParticipant = await ChatConversation.isParticipant(conversation_id, userId);

      if (!isParticipant) {
        throw new Error('User is not a participant in this conversation');
      }

      // Check again if server is closing before sending typing indicators
      if (isClosing) {
        console.warn('Cannot send typing indicator: WebSocket server is closing');
        return;
      }

      // Get all participants in the conversation
      const participants = await ChatConversation.getParticipants(conversation_id);

      // Send typing indicator to all online participants except the sender
      for (const participant of participants) {
        const recipientId = participant.user_id;

        if (recipientId !== userId) {
          const recipientWs = clients.get(recipientId);

          if (recipientWs && recipientWs.readyState === WebSocket.OPEN) {
            recipientWs.send(JSON.stringify({
              type: 'typing',
              conversation_id,
              user_id: userId,
              is_typing
            }));
          }
        }
      }
    } catch (error) {
      console.error('Error handling typing indicator:', error);
      throw error;
    }
  }

  /**
   * Handle read messages
   * @param {Object} data - Read messages data
   * @param {string} userId - User ID
   */
  async function handleReadMessages(data, userId) {
    // Don't process read messages if the server is closing
    if (isClosing) {
      console.warn('Cannot handle read messages: WebSocket server is closing');
      return;
    }

    try {
      const { conversation_id, message_ids } = data;

      // Check if user is a participant in the conversation
      const isParticipant = await ChatConversation.isParticipant(conversation_id, userId);

      if (!isParticipant) {
        throw new Error('User is not a participant in this conversation');
      }

      // Mark messages as read
      for (const messageId of message_ids) {
        await ChatMessage.markAsRead(messageId, userId);
      }

      // Update last_read_at for the user
      await ChatConversation.updateParticipant(conversation_id, userId, {
        last_read_at: new Date()
      });

      // Check again if server is closing before sending read receipts
      if (isClosing) {
        console.warn('Cannot send read receipts: WebSocket server is closing');
        return;
      }

      // Get all participants in the conversation
      const participants = await ChatConversation.getParticipants(conversation_id);

      // Send read receipt to all online participants
      for (const participant of participants) {
        const recipientId = participant.user_id;
        const recipientWs = clients.get(recipientId);

        if (recipientWs && recipientWs.readyState === WebSocket.OPEN) {
          recipientWs.send(JSON.stringify({
            type: 'read_receipt',
            conversation_id,
            user_id: userId,
            message_ids
          }));
        }
      }
    } catch (error) {
      console.error('Error handling read messages:', error);
      throw error;
    }
  }

  /**
   * Send a message to a specific user
   * @param {string} userId - User ID
   * @param {Object} message - Message to send
   */
  function sendToUser(userId, message) {
    // Don't attempt to send messages if the server is closing
    if (isClosing) {
      console.warn('Cannot send message: WebSocket server is closing');
      return false;
    }

    const ws = clients.get(userId);

    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message));
      return true;
    }

    return false;
  }

  /**
   * Broadcast a message to all connected users
   * @param {Object} message - Message to broadcast
   */
  function broadcast(message) {
    // Don't attempt to broadcast if the server is closing
    if (isClosing) {
      console.warn('Cannot broadcast message: WebSocket server is closing');
      return;
    }

    const messageStr = JSON.stringify(message);

    for (const ws of clients.values()) {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(messageStr);
      }
    }
  }

  /**
   * Close the WebSocket server gracefully
   */
  function close() {
    if (isClosing) {
      return; // Already closing
    }

    isClosing = true;
    console.log('Closing chat WebSocket server...');

    // Clear the inactivity check interval immediately
    if (inactivityCheckInterval) {
      clearInterval(inactivityCheckInterval);
      console.log('Cleared inactivity check interval');
    }

    // Close all client connections
    for (const [userId, ws] of clients.entries()) {
      try {
        if (ws.readyState === WebSocket.OPEN) {
          ws.close(1000, 'Server shutting down');
        }
      } catch (error) {
        console.error(`Error closing WebSocket connection for user ${userId}:`, error);
      }
    }

    // Clear the clients map
    clients.clear();

    // Close the server
    wss.close((err) => {
      if (err) {
        console.error('Error closing WebSocket server:', err);
      } else {
        console.log('Chat WebSocket server closed successfully');
      }
    });
  }

  // Set up a periodic task to mark inactive users as offline
  const inactivityCheckInterval = setInterval(async () => {
    // Skip if the server is closing or shutting down
    if (isClosing || isServerShuttingDown()) {
      return;
    }

    try {
      // Use SafeUserOnlineStatus to handle database operations safely
      const count = await UserOnlineStatus.markInactiveUsersOffline(300); // 5 minutes

      if (count > 0) {
        console.log(`Marked ${count} inactive users as offline`);
      }
    } catch (error) {
      console.error('Error marking inactive users as offline:', error);
    }
  }, 60000); // Run every minute

  // Update the close function to clear the interval
  const originalClose = close;
  close = function() {
    // Clear the interval first
    clearInterval(inactivityCheckInterval);

    // Call the original close function
    return originalClose.apply(this, arguments);
  };

  // Return the WebSocket server and utility functions
  return {
    wss,
    sendToUser,
    broadcast,
    close
  };
}

export default initChatWebSocketServer;
