import axios from 'axios';
import { API_URL } from '../config';
import cache from '../utils/browserCache';

// Types for marketplace listing data
export interface MarketplaceListing {
  id: string;
  title: string;
  description: string;
  listing_type: string;
  category: string;
  subcategory?: string;
  price?: number;
  price_type?: string;
  currency?: string;
  quantity?: number;
  unit?: string;
  location?: string;
  images?: string[];
  tags?: string[];
  status: string;
  visibility: string;
  views: number;
  favorites: number;
  is_featured: boolean;
  is_active: boolean;
  farm_id: string;
  farm_name?: string;
  created_by: string;
  created_at: string;
  isFavorited?: boolean;
}

export interface Category {
  id: string;
  name: string;
  subcategories: Array<{
    id: string;
    name: string;
  }>;
}

// Create a proxy endpoint on your server to avoid CORS issues and hide API keys
const MARKETPLACE_API_BASE = `${API_URL}/market`;

/**
 * Get all marketplace listings with filtering options
 * @param category Optional category to filter listings
 * @param subcategory Optional subcategory to filter listings
 * @param listingType Optional listing type to filter listings
 * @param minPrice Optional minimum price to filter listings
 * @param maxPrice Optional maximum price to filter listings
 * @param location Optional location to filter listings
 * @param searchTerm Optional search term to filter listings
 * @param visibility Optional visibility to filter listings
 * @returns Promise<MarketplaceListing[]> Marketplace listings for the specified filters
 */
export const getMarketplaceListings = async (
  category?: string,
  subcategory?: string,
  listingType?: string,
  minPrice?: number,
  maxPrice?: number,
  location?: string,
  searchTerm?: string,
  visibility?: string
): Promise<MarketplaceListing[]> => {
  try {
    // Create a cache key based on the query parameters
    const cacheKey = `marketplace_listings_${category || 'all'}_${subcategory || 'all'}_${listingType || 'all'}_${minPrice || 'all'}_${maxPrice || 'all'}_${location || 'all'}_${searchTerm || 'all'}_${visibility || 'all'}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached marketplace listings for key ${cacheKey}`);
      return cachedData as MarketplaceListing[];
    }

    // Build query parameters
    const params: Record<string, string> = {};
    if (category) params.category = category;
    if (subcategory) params.subcategory = subcategory;
    if (listingType) params.listingType = listingType;
    if (minPrice !== undefined) params.minPrice = minPrice.toString();
    if (maxPrice !== undefined) params.maxPrice = maxPrice.toString();
    if (location) params.location = location;
    if (searchTerm) params.searchTerm = searchTerm;
    if (visibility) params.visibility = visibility;

    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(`${MARKETPLACE_API_BASE}/listings`, { params });

    // Transform the data to match the expected format
    const listings: MarketplaceListing[] = response.data.map((listing: any) => ({
      id: listing.id,
      title: listing.title,
      description: listing.description,
      listing_type: listing.listing_type,
      category: listing.category,
      subcategory: listing.subcategory,
      price: listing.price ? parseFloat(listing.price) : undefined,
      price_type: listing.price_type,
      currency: listing.currency,
      quantity: listing.quantity ? parseFloat(listing.quantity) : undefined,
      unit: listing.unit,
      location: listing.location,
      images: listing.images,
      tags: listing.tags,
      status: listing.status,
      visibility: listing.visibility,
      views: listing.views,
      favorites: listing.favorites,
      is_featured: listing.is_featured,
      is_active: listing.is_active,
      farm_id: listing.farm_id,
      farm_name: listing.marketplaceListingFarm?.name,
      created_by: listing.created_by,
      created_at: listing.created_at,
      isFavorited: false // Default value, will be updated based on user's favorites
    }));

    // Store in cache
    cache.set(cacheKey, listings);
    console.log(`Cached marketplace listings for key ${cacheKey}`);

    return listings;
  } catch (error) {
    console.error('Error fetching marketplace listings:', error);

    // Return empty array as fallback
    return [];
  }
};

/**
 * Get all marketplace listings for a farm
 * @param farmId Farm ID to filter listings
 * @returns Promise<MarketplaceListing[]> Marketplace listings for the specified farm
 */
export const getFarmListings = async (farmId: string): Promise<MarketplaceListing[]> => {
  try {
    // Create a cache key based on the farm ID
    const cacheKey = `farm_listings_${farmId}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached farm listings for farm ${farmId}`);
      return cachedData as MarketplaceListing[];
    }

    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(`${MARKETPLACE_API_BASE}/listings/farm`, {
      params: { farmId }
    });

    // Transform the data to match the expected format
    const listings: MarketplaceListing[] = response.data.map((listing: any) => ({
      id: listing.id,
      title: listing.title,
      description: listing.description,
      listing_type: listing.listing_type,
      category: listing.category,
      subcategory: listing.subcategory,
      price: listing.price ? parseFloat(listing.price) : undefined,
      price_type: listing.price_type,
      currency: listing.currency,
      quantity: listing.quantity ? parseFloat(listing.quantity) : undefined,
      unit: listing.unit,
      location: listing.location,
      images: listing.images,
      tags: listing.tags,
      status: listing.status,
      visibility: listing.visibility,
      views: listing.views,
      favorites: listing.favorites,
      is_featured: listing.is_featured,
      is_active: listing.is_active,
      farm_id: listing.farm_id,
      created_by: listing.created_by,
      created_at: listing.created_at,
      isFavorited: false // Default value, will be updated based on user's favorites
    }));

    // Store in cache
    cache.set(cacheKey, listings);
    console.log(`Cached farm listings for farm ${farmId}`);

    return listings;
  } catch (error) {
    console.error(`Error fetching farm listings for farm ${farmId}:`, error);

    // Return empty array as fallback
    return [];
  }
};

/**
 * Get a single marketplace listing by ID
 * @param id Marketplace listing ID
 * @returns Promise<MarketplaceListing | null> Marketplace listing for the specified ID
 */
export const getMarketplaceListingById = async (id: string): Promise<MarketplaceListing | null> => {
  try {
    // Create a cache key based on the ID
    const cacheKey = `marketplace_listing_${id}`;

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log(`Using cached marketplace listing for ID ${id}`);
      return cachedData as MarketplaceListing;
    }

    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(`${MARKETPLACE_API_BASE}/listings/${id}`);

    // Transform the data to match the expected format
    const listing: MarketplaceListing = {
      id: response.data.id,
      title: response.data.title,
      description: response.data.description,
      listing_type: response.data.listing_type,
      category: response.data.category,
      subcategory: response.data.subcategory,
      price: response.data.price ? parseFloat(response.data.price) : undefined,
      price_type: response.data.price_type,
      currency: response.data.currency,
      quantity: response.data.quantity ? parseFloat(response.data.quantity) : undefined,
      unit: response.data.unit,
      location: response.data.location,
      images: response.data.images,
      tags: response.data.tags,
      status: response.data.status,
      visibility: response.data.visibility,
      views: response.data.views,
      favorites: response.data.favorites,
      is_featured: response.data.is_featured,
      is_active: response.data.is_active,
      farm_id: response.data.farm_id,
      farm_name: response.data.marketplaceListingFarm?.name,
      created_by: response.data.created_by,
      created_at: response.data.created_at,
      isFavorited: false // Default value, will be updated based on user's favorites
    };

    // Store in cache
    cache.set(cacheKey, listing);
    console.log(`Cached marketplace listing for ID ${id}`);

    return listing;
  } catch (error) {
    console.error(`Error fetching marketplace listing with ID ${id}:`, error);

    // Return null as fallback
    return null;
  }
};

/**
 * Get marketplace categories and subcategories
 * @returns Promise<Category[]> Marketplace categories and subcategories
 */
export const getMarketplaceCategories = async (): Promise<Category[]> => {
  try {
    // Create a cache key
    const cacheKey = 'marketplace_categories';

    // Check if data exists in cache
    const cachedData = cache.get(cacheKey);
    if (cachedData) {
      console.log('Using cached marketplace categories');
      return cachedData as Category[];
    }

    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.get(`${MARKETPLACE_API_BASE}/listings/categories`);

    // Store in cache
    cache.set(cacheKey, response.data);
    console.log('Cached marketplace categories');

    return response.data;
  } catch (error) {
    console.error('Error fetching marketplace categories:', error);

    // Return empty array as fallback
    return [];
  }
};

/**
 * Toggle favorite status for a listing
 * @param id Marketplace listing ID
 * @param userId User ID
 * @param isFavorite Whether to favorite or unfavorite the listing
 * @returns Promise<{ success: boolean, favorites: number }> Result of toggling favorite status
 */
export const toggleFavorite = async (
  id: string,
  userId: string,
  isFavorite: boolean
): Promise<{ success: boolean; favorites: number }> => {
  try {
    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.post(`${MARKETPLACE_API_BASE}/listings/${id}/favorite`, {
      userId,
      isFavorite
    });

    // Invalidate caches that might contain this listing
    cache.del(`marketplace_listing_${id}`);

    // We don't know which cache keys might contain this listing, so we can't invalidate them all
    // Instead, we'll let them expire naturally after the TTL

    return {
      success: true,
      favorites: response.data.favorites
    };
  } catch (error) {
    console.error(`Error toggling favorite status for listing ${id}:`, error);

    // Return failure as fallback
    return {
      success: false,
      favorites: 0
    };
  }
};

/**
 * Create a new marketplace listing
 * @param listing Marketplace listing data
 * @returns Promise<MarketplaceListing | null> Created marketplace listing
 */
export const createMarketplaceListing = async (
  listing: Omit<MarketplaceListing, 'id' | 'views' | 'favorites' | 'created_at' | 'isFavorited'>
): Promise<MarketplaceListing | null> => {
  try {
    // Use your backend as a proxy to avoid CORS issues and hide API keys
    const response = await axios.post(`${MARKETPLACE_API_BASE}/listings`, listing);

    // Invalidate farm listings cache
    cache.del(`farm_listings_${listing.farm_id}`);

    // Transform the data to match the expected format
    const createdListing: MarketplaceListing = {
      id: response.data.id,
      title: response.data.title,
      description: response.data.description,
      listing_type: response.data.listing_type,
      category: response.data.category,
      subcategory: response.data.subcategory,
      price: response.data.price ? parseFloat(response.data.price) : undefined,
      price_type: response.data.price_type,
      currency: response.data.currency,
      quantity: response.data.quantity ? parseFloat(response.data.quantity) : undefined,
      unit: response.data.unit,
      location: response.data.location,
      images: response.data.images,
      tags: response.data.tags,
      status: response.data.status,
      visibility: response.data.visibility,
      views: response.data.views,
      favorites: response.data.favorites,
      is_featured: response.data.is_featured,
      is_active: response.data.is_active,
      farm_id: response.data.farm_id,
      farm_name: response.data.marketplaceListingFarm?.name,
      created_by: response.data.created_by,
      created_at: response.data.created_at,
      isFavorited: false
    };

    return createdListing;
  } catch (error) {
    console.error('Error creating marketplace listing:', error);

    // Return null as fallback
    return null;
  }
};
