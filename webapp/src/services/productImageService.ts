import axios from 'axios';
import { API_URL } from '../config';

export interface ProductImage {
  id: string;
  product_id: string;
  file_path: string;
  display_order: number;
  is_primary: boolean;
  created_at: string;
  updated_at: string;
  url?: string; // Full URL for display
}

/**
 * Upload a new product image
 * @param productId The ID of the product
 * @param imageFile The image file to upload
 * @returns Promise<ProductImage> The uploaded image
 */
export const uploadProductImage = async (
  productId: string,
  imageFile: File
): Promise<ProductImage> => {
  try {
    const formData = new FormData();
    formData.append('image', imageFile);

    const response = await axios.post(
      `${API_URL}/products/${productId}/images`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  } catch (error) {
    console.error('Error uploading product image:', error);
    throw error;
  }
};

/**
 * Get all images for a product
 * @param productId The ID of the product
 * @returns Promise<ProductImage[]> Array of product images
 */
export const getProductImages = async (
  productId: string
): Promise<ProductImage[]> => {
  try {
    const response = await axios.get(`${API_URL}/products/${productId}/images`);
    return response.data;
  } catch (error) {
    console.error('Error fetching product images:', error);
    return [];
  }
};

/**
 * Reorder product images
 * @param productId The ID of the product
 * @param imageIds Array of image IDs in the desired order
 * @returns Promise<ProductImage[]> Updated array of product images
 */
export const reorderProductImages = async (
  productId: string,
  imageIds: string[]
): Promise<ProductImage[]> => {
  try {
    const response = await axios.put(
      `${API_URL}/products/${productId}/images/reorder`,
      { imageIds }
    );
    return response.data;
  } catch (error) {
    console.error('Error reordering product images:', error);
    throw error;
  }
};

/**
 * Delete a product image
 * @param productId The ID of the product
 * @param imageId The ID of the image to delete
 * @returns Promise<{ message: string }> Success message
 */
export const deleteProductImage = async (
  productId: string,
  imageId: string
): Promise<{ message: string }> => {
  try {
    const response = await axios.delete(
      `${API_URL}/products/${productId}/images/${imageId}`
    );
    return response.data;
  } catch (error) {
    console.error('Error deleting product image:', error);
    throw error;
  }
};

/**
 * Set a product image as primary
 * @param productId The ID of the product
 * @param imageId The ID of the image to set as primary
 * @returns Promise<ProductImage> Updated image
 */
export const setPrimaryImage = async (
  productId: string,
  imageId: string
): Promise<ProductImage> => {
  try {
    const response = await axios.put(
      `${API_URL}/products/${productId}/images/${imageId}/primary`
    );
    return response.data;
  } catch (error) {
    console.error('Error setting primary image:', error);
    throw error;
  }
};