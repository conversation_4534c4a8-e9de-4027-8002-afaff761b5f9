import React, { useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../../context/CartContext';
import { X, Plus, Minus, ShoppingCart, Save, Trash2, ShoppingBag } from 'lucide-react';
import { Button } from '../ui/Button';
import { formatCurrency } from '../../utils/formatters';

interface CartSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  farmId?: string; // Make farmId optional
}

const CartSidebar: React.FC<CartSidebarProps> = ({ isOpen, onClose, farmId }) => {
  const { 
    cart, 
    carts, 
    loading, 
    error, 
    fetchCart, 
    fetchAllCarts, 
    updateItemQuantity, 
    removeItem, 
    saveCart,
    checkoutAllCarts,
    getTotalItemCount,
    getTotalPrice
  } = useCart();
  const navigate = useNavigate();

  useEffect(() => {
    if (isOpen) {
      if (farmId) {
        // If farmId is provided, fetch specific cart
        fetchCart(farmId);
      } else {
        // Otherwise fetch all carts
        fetchAllCarts();
      }
    }
  }, [isOpen, farmId, fetchCart, fetchAllCarts]);

  const handleQuantityChange = async (itemId: string, currentQuantity: number, change: number) => {
    const newQuantity = Math.max(1, currentQuantity + change);
    await updateItemQuantity(itemId, newQuantity);
  };

  const handleRemoveItem = async (itemId: string) => {
    await removeItem(itemId);
  };

  const handleSaveCart = async (cartId: string) => {
    await saveCart(cartId);
  };

  const handleCheckout = (cartId: string) => {
    onClose();
    navigate(`/marketplace/checkout?cartId=${cartId}`);
  };

  const handleCheckoutAll = async () => {
    try {
      await checkoutAllCarts();
      onClose();
      navigate('/marketplace/checkout-success');
    } catch (err) {
      console.error('Error checking out all carts:', err);
    }
  };

  if (!isOpen) return null;

  // Determine if we're showing a single cart or all carts
  const showingSingleCart = !!farmId && !!cart;
  const cartsToShow = showingSingleCart ? [cart] : carts;
  const totalItemCount = showingSingleCart ? cart?.itemCount || 0 : getTotalItemCount();
  const totalPrice = showingSingleCart ? cart?.totalPrice || 0 : getTotalPrice();

  return (
    <div className="fixed inset-0 z-50 flex justify-end">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50" 
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Sidebar */}
      <div className="relative w-full max-w-md bg-white shadow-xl flex flex-col h-full">
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold flex items-center">
            <ShoppingCart className="mr-2 h-5 w-5" />
            Your Cart
          </h2>
          <button 
            onClick={onClose}
            className="p-1 rounded-full hover:bg-gray-100"
            aria-label="Close cart"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {loading ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        ) : error ? (
          <div className="flex-1 p-4 text-center text-red-500">
            {error}
            <Button 
              variant="outline" 
              className="mt-2" 
              onClick={() => farmId ? fetchCart(farmId) : fetchAllCarts()}
            >
              Try Again
            </Button>
          </div>
        ) : cartsToShow.length === 0 || cartsToShow.every(c => !c || c.items.length === 0) ? (
          <div className="flex-1 flex flex-col items-center justify-center p-4">
            <ShoppingCart className="h-16 w-16 text-gray-300 mb-4" />
            <p className="text-gray-500 text-center mb-4">Your cart is empty</p>
            <Button variant="outline" onClick={onClose}>
              Continue Shopping
            </Button>
          </div>
        ) : (
          <>
            <div className="flex-1 overflow-auto p-4">
              <div className="space-y-6">
                {cartsToShow.map((cart) => (
                  cart && cart.items.length > 0 && (
                    <div key={cart.id} className="space-y-4">
                      {/* Farm header - only show in multi-farm view */}
                      {!showingSingleCart && (
                        <div className="flex items-center pb-2 border-b">
                          {cart.farm.logo_url ? (
                            <img 
                              src={cart.farm.logo_url} 
                              alt={cart.farm.name} 
                              className="w-8 h-8 rounded-full mr-2"
                            />
                          ) : (
                            <ShoppingBag className="w-8 h-8 text-gray-400 mr-2" />
                          )}
                          <h3 className="font-semibold">{cart.farm.name}</h3>
                          <div className="ml-auto text-sm text-gray-500">
                            {formatCurrency(cart.totalPrice)}
                          </div>
                        </div>
                      )}

                      {/* Cart items */}
                      {cart.items.map((item) => (
                        <div key={item.id} className="flex border rounded-lg p-3 relative">
                          <div className="w-20 h-20 bg-gray-100 rounded-md overflow-hidden mr-3 flex-shrink-0">
                            {item.product.images && item.product.images.length > 0 ? (
                              <img 
                                src={item.product.images[0].file_path} 
                                alt={item.product.name} 
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                <span className="text-gray-400 text-xs">No image</span>
                              </div>
                            )}
                          </div>

                          <div className="flex-1">
                            <h3 className="font-medium">{item.product.name}</h3>
                            <p className="text-sm text-gray-500 mb-2">
                              {formatCurrency(item.product.price)}
                            </p>

                            <div className="flex items-center">
                              <button 
                                onClick={() => handleQuantityChange(item.id, item.quantity, -1)}
                                className="p-1 rounded-full hover:bg-gray-100"
                                aria-label="Decrease quantity"
                                disabled={item.quantity <= 1}
                              >
                                <Minus className="h-4 w-4" />
                              </button>
                              <span className="mx-2 min-w-[2rem] text-center">{item.quantity}</span>
                              <button 
                                onClick={() => handleQuantityChange(item.id, item.quantity, 1)}
                                className="p-1 rounded-full hover:bg-gray-100"
                                aria-label="Increase quantity"
                              >
                                <Plus className="h-4 w-4" />
                              </button>

                              <button 
                                onClick={() => handleRemoveItem(item.id)}
                                className="ml-auto p-1 rounded-full hover:bg-gray-100 text-red-500"
                                aria-label="Remove item"
                              >
                                <Trash2 className="h-4 w-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}

                      {/* Per-farm actions - only show in multi-farm view */}
                      {!showingSingleCart && (
                        <div className="flex justify-end space-x-2 pb-4 border-b">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleSaveCart(cart.id)}
                          >
                            <Save className="mr-1 h-3 w-3" />
                            Save
                          </Button>
                          <Button 
                            size="sm"
                            onClick={() => handleCheckout(cart.id)}
                          >
                            Checkout
                          </Button>
                        </div>
                      )}
                    </div>
                  )
                ))}
              </div>
            </div>

            <div className="border-t p-4 space-y-4">
              <div className="flex justify-between text-sm">
                <span>Subtotal ({totalItemCount} items)</span>
                <span>{formatCurrency(totalPrice)}</span>
              </div>

              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>{formatCurrency(totalPrice)}</span>
              </div>

              {showingSingleCart ? (
                // Single cart actions
                <div className="grid grid-cols-2 gap-2">
                  <Button 
                    variant="outline" 
                    className="w-full flex items-center justify-center"
                    onClick={() => cart && handleSaveCart(cart.id)}
                  >
                    <Save className="mr-2 h-4 w-4" />
                    Save for Later
                  </Button>
                  <Button 
                    className="w-full"
                    onClick={() => cart && handleCheckout(cart.id)}
                  >
                    Checkout
                  </Button>
                </div>
              ) : (
                // Multi-cart actions
                <div className="grid grid-cols-1 gap-2">
                  <Button 
                    className="w-full"
                    onClick={handleCheckoutAll}
                    disabled={carts.length === 0}
                  >
                    Checkout All Items
                  </Button>
                </div>
              )}

              <div className="text-center">
                <button 
                  onClick={onClose}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Continue Shopping
                </button>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default CartSidebar;
