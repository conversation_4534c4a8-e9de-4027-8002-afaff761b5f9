import React from 'react';

interface SwitchProps {
  id?: string;
  checked?: boolean;
  onCheckedChange?: (checked: boolean) => void;
  disabled?: boolean;
  className?: string;
}

export const Switch: React.FC<SwitchProps> = ({
  id,
  checked = false,
  onCheckedChange,
  disabled = false,
  className = '',
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onCheckedChange) {
      onCheckedChange(e.target.checked);
    }
  };

  return (
    <div className={`relative inline-flex items-center ${className}`}>
      <input
        id={id}
        type="checkbox"
        className="sr-only"
        checked={checked}
        disabled={disabled}
        onChange={handleChange}
      />
      <div
        className={`
          w-11 h-6 rounded-full transition-colors
          ${checked ? 'bg-blue-600' : 'bg-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        `}
        onClick={() => !disabled && onCheckedChange && onCheckedChange(!checked)}
      >
        <div
          className={`
            transform transition-transform duration-200 ease-in-out
            h-5 w-5 rounded-full bg-white shadow-md
            ${checked ? 'translate-x-6' : 'translate-x-1'}
            flex items-center justify-center
            mt-0.5
          `}
        />
      </div>
    </div>
  );
};