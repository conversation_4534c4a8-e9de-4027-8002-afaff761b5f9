import React, { useState, useEffect } from 'react';
import { getAlerts, markAlertAsRead, markAllAlertsAsRead, Alert } from '../services/alertService';
import { useFarm } from '../context/FarmContext';
import ErrorDisplay from './ui/ErrorDisplay';

interface AlertListProps {
  limit?: number;
  showMarkAllAsRead?: boolean;
  onlyUnread?: boolean;
  type?: string;
  className?: string;
}

const AlertList: React.FC<AlertListProps> = ({
  limit = 5,
  showMarkAllAsRead = true,
  onlyUnread = false,
  type,
  className = ''
}) => {
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [totalAlerts, setTotalAlerts] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const { selectedFarm } = useFarm();

  useEffect(() => {
    if (selectedFarm) {
      fetchAlerts();
    }
  }, [selectedFarm, onlyUnread, type]);

  const fetchAlerts = async () => {
    if (!selectedFarm) return;

    try {
      setIsLoading(true);
      setError(null);

      const options: { limit: number; offset: number; read?: boolean; type?: string } = {
        limit,
        offset: 0
      };

      if (onlyUnread) {
        options.read = false;
      }

      if (type) {
        options.type = type;
      }

      const response = await getAlerts(selectedFarm.id, options);
      setAlerts(response.alerts);
      setTotalAlerts(response.total);
    } catch (err: unknown) {
      console.error('Error fetching alerts:', err);

      // Extract structured error information if available
      if (err instanceof Error && (err as any).structuredError) {
        setError((err as any).structuredError);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to load alerts. Please try again later.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleMarkAsRead = async (alertId: string) => {
    try {
      await markAlertAsRead(alertId);
      // Update the local state to mark the alert as read
      setAlerts(alerts.map(alert => 
        alert.id === alertId ? { ...alert, read: true } : alert
      ));
    } catch (err: unknown) {
      console.error('Error marking alert as read:', err);

      // Extract structured error information if available
      if (err instanceof Error && (err as any).structuredError) {
        setError((err as any).structuredError);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to mark alert as read. Please try again later.');
      }
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!selectedFarm) return;

    try {
      await markAllAlertsAsRead(selectedFarm.id);
      // Update the local state to mark all alerts as read
      setAlerts(alerts.map(alert => ({ ...alert, read: true })));
    } catch (err: unknown) {
      console.error('Error marking all alerts as read:', err);

      // Extract structured error information if available
      if (err instanceof Error && (err as any).structuredError) {
        setError((err as any).structuredError);
      } else if (err instanceof Error) {
        setError(err.message);
      } else {
        setError('Failed to mark all alerts as read. Please try again later.');
      }
    }
  };

  const getAlertTypeStyles = (type: string) => {
    switch (type) {
      case 'info':
        return 'bg-blue-100 text-blue-800 border-blue-300';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-300';
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-300';
      case 'success':
        return 'bg-green-100 text-green-800 border-green-300';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-300';
    }
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'weather':
        return '🌤️';
      case 'equipment':
        return '🚜';
      case 'inventory':
        return '📦';
      case 'financial':
        return '💰';
      case 'task':
        return '📋';
      case 'crop':
        return '🌱';
      default:
        return '🔔';
    }
  };

  if (isLoading) {
    return (
      <div className={`flex justify-center items-center h-24 ${className}`}>
        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <ErrorDisplay 
        error={error} 
        className={className}
        onRetry={fetchAlerts}
        onDismiss={() => setError(null)}
      />
    );
  }

  if (alerts.length === 0) {
    return (
      <div className={`text-center py-4 ${className}`}>
        <p className="text-gray-500">No alerts to display.</p>
      </div>
    );
  }

  return (
    <div className={`${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Alerts ({totalAlerts})</h3>
        {showMarkAllAsRead && alerts.some(alert => !alert.read) && (
          <button
            onClick={handleMarkAllAsRead}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            Mark all as read
          </button>
        )}
      </div>

      <div className="space-y-3">
        {alerts.map(alert => (
          <div
            key={alert.id}
            className={`border rounded-lg p-3 ${getAlertTypeStyles(alert.type)} ${
              !alert.read ? 'font-semibold' : 'opacity-75'
            }`}
          >
            <div className="flex justify-between items-start">
              <div className="flex items-start space-x-2">
                <span className="text-xl">{getSourceIcon(alert.source)}</span>
                <div>
                  <h4 className="font-medium">{alert.title}</h4>
                  <p className="text-sm mt-1">{alert.message}</p>
                </div>
              </div>
              {!alert.read && (
                <button
                  onClick={() => handleMarkAsRead(alert.id)}
                  className="text-xs bg-white bg-opacity-50 hover:bg-opacity-75 px-2 py-1 rounded"
                >
                  Mark as read
                </button>
              )}
            </div>
            <div className="flex justify-between items-center mt-2 text-xs">
              <span>{new Date(alert.createdAt).toLocaleString()}</span>
              {alert.actionUrl && (
                <a
                  href={alert.actionUrl}
                  className="text-blue-600 hover:text-blue-800 underline"
                >
                  View details
                </a>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AlertList;
