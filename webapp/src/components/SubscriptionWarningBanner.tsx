import React, { useContext, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { FarmContext } from '../context/FarmContext';
import { AuthContext } from '../context/AuthContext';
import axios from 'axios';

interface SubscriptionWarning {
  isExpiringSoon: boolean;
  isTrialEndingSoon: boolean;
  subscriptionEndDate: string;
}

interface SubscriptionStatus {
  isValid: boolean;
  isExpired: boolean;
  isExpiringSoon: boolean;
  isTrial: boolean;
  isTrialEndingSoon: boolean;
  hasAutoPayEnabled: boolean;
  subscriptionEndDate: string;
  subscriptionStatus: string;
}

const SubscriptionWarningBanner: React.FC = () => {
  const { currentFarm } = useContext(FarmContext);
  const { user } = useContext(AuthContext);
  const [subscriptionWarning, setSubscriptionWarning] = useState<SubscriptionWarning | null>(null);
  const [subscriptionExpired, setSubscriptionExpired] = useState<boolean>(false);
  const [hasBillingPermissions, setHasBillingPermissions] = useState<boolean>(false);

  useEffect(() => {
    // Only check subscription status if we have a current farm and user
    if (currentFarm?.id && user?.id) {
      // Check if user has billing permissions for this farm
      const checkBillingPermissions = async () => {
        try {
          const response = await axios.get(`/api/farms/${currentFarm.id}/user-permissions`);
          const hasPermissions = response.data.hasBillingPermissions || 
                                response.data.role === 'farm_owner' || 
                                response.data.role === 'farm_admin';
          setHasBillingPermissions(hasPermissions);
        } catch (error) {
          console.error('Error checking billing permissions:', error);
        }
      };

      // Check subscription status
      const checkSubscriptionStatus = async () => {
        try {
          const response = await axios.get(`/api/farms/${currentFarm.id}/subscription-status`);
          const status: SubscriptionStatus = response.data;
          
          if (status.isExpired) {
            setSubscriptionExpired(true);
          } else if ((status.isExpiringSoon && !status.hasAutoPayEnabled) || status.isTrialEndingSoon) {
            setSubscriptionWarning({
              isExpiringSoon: status.isExpiringSoon && !status.hasAutoPayEnabled,
              isTrialEndingSoon: status.isTrialEndingSoon,
              subscriptionEndDate: status.subscriptionEndDate
            });
          }
        } catch (error) {
          console.error('Error checking subscription status:', error);
        }
      };

      checkBillingPermissions();
      checkSubscriptionStatus();
    }
  }, [currentFarm, user]);

  // If no warnings or not a billing admin, don't render anything
  if ((!subscriptionWarning && !subscriptionExpired) || !hasBillingPermissions) {
    return null;
  }

  // Format the date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    });
  };

  // Render expired banner
  if (subscriptionExpired) {
    return (
      <div className="bg-red-600 text-white px-4 py-3 shadow-md">
        <div className="container mx-auto flex flex-col md:flex-row items-center justify-between">
          <div className="flex items-center">
            <svg className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <span className="font-semibold">Your subscription has expired!</span>
          </div>
          <Link 
            to={`/farms/${currentFarm?.id}/billing`} 
            className="mt-2 md:mt-0 bg-white text-red-600 hover:bg-gray-100 font-semibold py-2 px-4 rounded-md transition-colors"
          >
            Renew Subscription
          </Link>
        </div>
      </div>
    );
  }

  // Render warning banner
  return (
    <div className="bg-yellow-500 text-white px-4 py-3 shadow-md">
      <div className="container mx-auto flex flex-col md:flex-row items-center justify-between">
        <div className="flex items-center">
          <svg className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <span className="font-semibold">
            {subscriptionWarning?.isTrialEndingSoon 
              ? `Your trial period is ending soon on ${formatDate(subscriptionWarning.subscriptionEndDate)}!` 
              : `Your subscription is expiring soon on ${formatDate(subscriptionWarning.subscriptionEndDate)} and auto-pay is not enabled!`}
          </span>
        </div>
        <Link 
          to={`/farms/${currentFarm?.id}/billing`} 
          className="mt-2 md:mt-0 bg-white text-yellow-600 hover:bg-gray-100 font-semibold py-2 px-4 rounded-md transition-colors"
        >
          {subscriptionWarning?.isTrialEndingSoon ? 'Upgrade Now' : 'Manage Subscription'}
        </Link>
      </div>
    </div>
  );
};

export default SubscriptionWarningBanner;