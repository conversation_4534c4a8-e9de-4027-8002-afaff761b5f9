import React, { useRef, useCallback, useEffect } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { FixedSizeList } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import ContextMenu from '../ContextMenu';
import { FileItem, ItemTypes, FileManagerListProps } from './FileManagerTypes';
import { API_URL } from '../../config';
import LazyImage from './LazyImage';

interface DragItem {
  id: string;
  type: string;
}

const FileManagerList: React.FC<FileManagerListProps> = ({
  items,
  selectedItems,
  focusedItemIndex,
  onSelect,
  onDoubleClick,
  onSort,
  sortBy,
  sortDirection,
  onMove,
  currentFolder,
  onManagePermissions,
  onShare,
  onCopy,
  onCut,
  onPaste,
  canPaste,
  clipboardItems,
  clipboardOperation,
  registerItemRef
}) => {
  // Format file size for display
  const formatFileSize = useCallback((bytes?: number) => {
    if (!bytes || bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Format date for display
  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  }, []);

  // Handle item selection
  const handleItemClick = useCallback((e: React.MouseEvent, itemId: string) => {
    const isMultiSelect = e.ctrlKey || e.metaKey;
    // Note: isShiftSelect is not used as onSelect only accepts 2 arguments
    onSelect(itemId, isMultiSelect);
  }, [onSelect]);

  // Handle item double click
  const handleItemDoubleClick = useCallback((item: FileItem) => {
    onDoubleClick(item);
  }, [onDoubleClick]);

  // Render sort indicator
  const renderSortIndicator = useCallback((field: string) => {
    if (sortBy !== field) return null;

    return (
      <span className="ml-1">
        {sortDirection === 'asc' ? (
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
          </svg>
        ) : (
          <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </span>
    );
  }, [sortBy, sortDirection]);

  // Row renderer for virtualized list
  const Row = useCallback(
    ({ index, style }: any) => {
      const item = items[index];
      return (
        <ListItem
          key={item.id}
          item={item}
          isSelected={selectedItems.includes(item.id)}
          isFocused={index === focusedItemIndex}
          onClick={handleItemClick}
          onDoubleClick={handleItemDoubleClick}
          onMove={onMove}
          formatFileSize={formatFileSize}
          formatDate={formatDate}
          currentFolder={currentFolder}
          onManagePermissions={onManagePermissions}
          onShare={onShare}
          onCopy={onCopy}
          onCut={onCut}
          onPaste={onPaste}
          canPaste={canPaste}
          clipboardItems={clipboardItems}
          clipboardOperation={clipboardOperation}
          registerItemRef={registerItemRef}
          style={style}
        />
      );
    },
    [items, selectedItems, focusedItemIndex, handleItemClick, handleItemDoubleClick, onMove, formatFileSize, formatDate, currentFolder, onManagePermissions, onShare, onCopy, onCut, onPaste, canPaste, clipboardItems, clipboardOperation, registerItemRef]
  );

  return (
    <div className="overflow-x-auto" style={{ height: 'calc(100vh - 200px)', minHeight: '400px' }}>
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('name')}
            >
              <div className="flex items-center">
                Name {renderSortIndicator('name')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('size')}
            >
              <div className="flex items-center">
                Size {renderSortIndicator('size')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('type')}
            >
              <div className="flex items-center">
                Type {renderSortIndicator('type')}
              </div>
            </th>
            <th
              scope="col"
              className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
              onClick={() => onSort('date')}
            >
              <div className="flex items-center">
                Date Modified {renderSortIndicator('date')}
              </div>
            </th>
          </tr>
        </thead>

        {/* Virtualized list for table body */}
        <tbody className="bg-white">
          {items.length > 0 && (
            <AutoSizer disableHeight>
              {({ width }) => (
                <FixedSizeList
                  height={Math.min(items.length * 64, window.innerHeight - 200)}
                  width={width}
                  itemCount={items.length}
                  itemSize={64} // Row height
                >
                  {Row}
                </FixedSizeList>
              )}
            </AutoSizer>
          )}
        </tbody>
      </table>
    </div>
  );
};

interface ListItemProps {
  item: FileItem;
  isSelected: boolean;
  isFocused: boolean;
  onClick: (e: React.MouseEvent, id: string) => void;
  onDoubleClick: (item: FileItem) => void;
  onMove: (itemIds: string[], targetFolderId: string | null) => void;
  formatFileSize: (bytes?: number) => string;
  formatDate: (dateString: string) => string;
  currentFolder: string | null;
  onManagePermissions?: (item: FileItem) => void;
  onShare?: (item: FileItem) => void;
  onCopy?: (items: FileItem[]) => void;
  onCut?: (items: FileItem[]) => void;
  onPaste?: (targetFolderId: string | null) => void;
  canPaste?: boolean;
  clipboardItems?: FileItem[];
  clipboardOperation?: 'copy' | 'cut' | null;
  registerItemRef: (itemId: string, element: HTMLElement | null) => void;
  style?: React.CSSProperties;
}

const ListItem: React.FC<ListItemProps> = ({
  item,
  isSelected,
  isFocused,
  onClick,
  onDoubleClick,
  onMove,
  formatFileSize,
  formatDate,
  currentFolder,
  onManagePermissions,
  onShare,
  onCopy,
  onCut,
  onPaste,
  canPaste,
  clipboardItems,
  clipboardOperation,
  registerItemRef,
  style
}) => {
  const ref = useRef<HTMLTableRowElement>(null);

  // Register ref for drag selection
  useEffect(() => {
    registerItemRef(item.id, ref.current);
    return () => {
      registerItemRef(item.id, null);
    };
  }, [item.id, registerItemRef]);

  // Set up drag source
  const [{ isDragging }, drag] = useDrag({
    type: item.type === 'folder' ? ItemTypes.FOLDER : ItemTypes.FILE,
    item: { id: item.id, type: item.type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  // Set up drop target (only folders can be drop targets)
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [ItemTypes.FILE, ItemTypes.FOLDER],
    canDrop: (draggedItem: DragItem) => {
      // Can't drop on itself
      if (draggedItem.id === item.id) return false;
      // Can only drop on folders
      return item.type === 'folder';
    },
    drop: (draggedItem: DragItem) => {
      onMove([draggedItem.id], item.id);
      return { dropEffect: 'move' };
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  // Apply drag and drop refs if applicable
  if (item.type === 'folder') {
    drop(ref);
  }
  drag(ref);

  // Check if this item is cut (in clipboard with cut operation)
  const isCut = clipboardOperation === 'cut' && clipboardItems?.some(clipItem => clipItem.id === item.id);

  // Determine row style based on state
  const rowStyle = `
    ${isSelected ? 'bg-primary-50' : isCut ? 'bg-gray-50 hover:bg-gray-100' : 'bg-white hover:bg-gray-50'}
    ${isDragging ? 'opacity-50' : isCut ? 'opacity-60' : 'opacity-100'}
    ${isOver && canDrop ? 'bg-green-50' : ''}
  `;

  // Get appropriate icon based on item type and file type
  const getItemIcon = () => {
    if (item.type === 'folder') {
      return (
        <svg className="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
          />
        </svg>
      );
    }

    // File icon based on mime type
    const mimeType = item.mime_type || '';

    if (mimeType.startsWith('image/')) {
      // For image files, try to load a thumbnail
      if (item.file_path) {
        const thumbnailUrl = `${API_URL}/documents/${item.id}/thumbnail`;
        return (
          <div className="h-5 w-5 overflow-hidden rounded">
            <LazyImage
              src={thumbnailUrl}
              alt={item.name}
              className="h-full w-full object-cover"
              placeholderClassName="h-5 w-5"
            />
          </div>
        );
      } else {
        // Fallback to icon if no file path
        return (
          <svg className="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
        );
      }
    }

    if (mimeType.startsWith('video/')) {
      return (
        <svg className="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
          />
        </svg>
      );
    }

    if (mimeType.startsWith('audio/')) {
      return (
        <svg className="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"
          />
        </svg>
      );
    }

    if (mimeType.startsWith('application/pdf')) {
      return (
        <svg className="h-5 w-5 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={1.5}
            d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z"
          />
        </svg>
      );
    }

    // Default file icon
    return (
      <svg className="h-5 w-5 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={1.5}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      </svg>
    );
  };

  return (
    <ContextMenu
      items={[
        {
          label: item.type === 'folder' ? 'Open Folder' : 'View File',
          icon: (
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
          ),
          onClick: () => onDoubleClick(item)
        },
        ...(item.type === 'file' ? [
          {
            label: 'Edit File',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            ),
            onClick: () => onDoubleClick(item)
          },
          {
            label: 'Download',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"
                />
              </svg>
            ),
            onClick: () => window.open(`${API_URL}/documents/${item.id}/download`, '_blank')
          }
        ] : []),
        {
          label: 'Move to Root',
          icon: (
            <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
          ),
          onClick: () => onMove([item.id], null),
          disabled: currentFolder === null
        },
        ...(onManagePermissions ? [
          {
            label: 'Manage Permissions',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            ),
            onClick: () => onManagePermissions(item)
          }
        ] : []),
        ...(onShare ? [
          {
            label: 'Share',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"
                />
              </svg>
            ),
            onClick: () => onShare(item)
          }
        ] : []),
        ...(onCopy ? [
          {
            label: 'Copy',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7v8a2 2 0 002 2h6M8 7V5a2 2 0 012-2h4.586a1 1 0 01.707.293l4.414 4.414a1 1 0 01.293.707V15a2 2 0 01-2 2h-2M8 7H6a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2v-2" />
              </svg>
            ),
            onClick: () => onCopy([item])
          }
        ] : []),
        ...(onCut ? [
          {
            label: 'Cut',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.121 14.121L19 19m-7-7l7-7m-7 7l-2.879 2.879M12 12L9.121 9.121m0 5.758a3 3 0 10-4.243 4.243 3 3 0 004.243-4.243zm0-5.758a3 3 0 10-4.243-4.243 3 3 0 004.243 4.243z" />
              </svg>
            ),
            onClick: () => onCut([item])
          }
        ] : []),
        ...(item.type === 'folder' && onPaste && canPaste ? [
          {
            label: 'Paste',
            icon: (
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            ),
            onClick: () => onPaste(item.id)
          }
        ] : [])
      ]}
    >
      <tr
        ref={ref}
        className={`file-item border-b border-gray-200 ${rowStyle}`}
        onClick={(e) => onClick(e, item.id)}
        onDoubleClick={() => onDoubleClick(item)}
      >
        {/* Name column */}
        <td className="px-6 py-4 whitespace-nowrap min-w-0">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {getItemIcon()}
            </div>
            <div className="ml-4 min-w-0">
              <div className="text-sm font-medium text-gray-900 truncate">{item.name}</div>
              <div className="text-sm text-gray-500 truncate">{item.description || 'No description'}</div>
            </div>
          </div>
        </td>

        {/* Size column */}
        <td className="px-6 py-4 whitespace-nowrap w-24">
          <div className="text-sm text-gray-500">
            {item.type === 'file' ? formatFileSize(item.file_size) : '-'}
          </div>
        </td>

        {/* Type column */}
        <td className="px-6 py-4 whitespace-nowrap w-24">
          <div className="text-sm text-gray-500">
            {item.type === 'folder' ? 'Folder' : (item.file_type || 'Unknown')}
          </div>
        </td>

        {/* Date column */}
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 w-48">
          {formatDate(item.updated_at || item.created_at)}
        </td>
      </tr>
    </ContextMenu>
  );
};

export default FileManagerList;
