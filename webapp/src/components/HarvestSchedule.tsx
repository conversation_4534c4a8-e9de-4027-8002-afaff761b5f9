import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import HarvestPlanForm from './HarvestPlanForm';

interface Field {
  id: string;
  name: string;
  crop_type: string;
  status: string;
  size: number;
  size_unit: string;
}

interface HarvestPlan {
  id?: string;
  field_id: string;
  planned_start_date: string;
  planned_end_date: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
  notes: string;
  created_at?: string;
  updated_at?: string;
}

interface WeatherForecast {
  date: string;
  condition: string;
  temperature_high: number;
  temperature_low: number;
  precipitation_chance: number;
  wind_speed: number;
}

interface HarvestRecommendation {
  date: string;
  rating: 'optimal' | 'good' | 'fair' | 'poor';
  reason: string;
}

interface HarvestScheduleProps {
  farmId: string;
  onScheduleChange?: (plans: HarvestPlan[]) => void;
}

const HarvestSchedule: React.FC<HarvestScheduleProps> = ({ farmId, onScheduleChange }) => {
  const [fields, setFields] = useState<Field[]>([]);
  const [harvestPlans, setHarvestPlans] = useState<HarvestPlan[]>([]);
  const [selectedField, setSelectedField] = useState<string>('');
  const [weatherForecast, setWeatherForecast] = useState<WeatherForecast[]>([]);
  const [recommendations, setRecommendations] = useState<Record<string, HarvestRecommendation[]>>({});
  const [_loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPlanForm, setShowPlanForm] = useState(false);
  const [editingPlanId, setEditingPlanId] = useState<string | null>(null);

  // New plan form state
  const [_newPlan, setNewPlan] = useState<HarvestPlan>({
    field_id: '',
    planned_start_date: '',
    planned_end_date: '',
    status: 'scheduled',
    notes: ''
  });

  // Fetch fields for the farm
  useEffect(() => {
    const fetchFields = async () => {
      if (!farmId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/fields/farm/${farmId}`);
        // Filter fields that have crop types and are in active or planted status
        const harvestableFields = response.data.filter(
          (field: Field) => field.crop_type && (field.status === 'active' || field.status === 'planted')
        );
        setFields(harvestableFields);

        if (harvestableFields.length > 0 && !selectedField) {
          setSelectedField(harvestableFields[0].id);
          setNewPlan(prev => ({ ...prev, field_id: harvestableFields[0].id }));
        }
      } catch (err) {
        console.error('Error fetching fields:', err);
        setError('Failed to load fields. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFields();
  }, [farmId, selectedField]);

  // Fetch real harvest plans and weather data
  useEffect(() => {
    if (!fields.length || !farmId) return;

    const fetchHarvestPlans = async () => {
      setLoading(true);
      setError(null);

      try {
        // Fetch harvest schedules for the farm
        const response = await axios.get(`${API_URL}/harvest-schedules/farm/${farmId}`);

        // Map the backend model to the frontend interface
        const plans: HarvestPlan[] = response.data.harvestSchedules.map((schedule: any) => ({
          id: schedule.id,
          field_id: schedule.field_id,
          planned_start_date: schedule.scheduled_date,
          planned_end_date: schedule.actual_end_date || new Date(new Date(schedule.scheduled_date).getTime() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: mapStatusToFrontend(schedule.status),
          notes: schedule.notes || '',
          created_at: schedule.created_at,
          updated_at: schedule.updated_at
        }));

        setHarvestPlans(plans);

        if (onScheduleChange) {
          onScheduleChange(plans);
        }
      } catch (err) {
        console.error('Error fetching harvest plans:', err);

        // Fallback to empty plans if API fails
        setHarvestPlans([]);

        // Don't set error here to avoid showing error message when there are no plans yet
      } finally {
        setLoading(false);
      }
    };

    const fetchWeatherData = async () => {
      if (!selectedField) return;

      try {
        // Fetch weather data for the selected field
        const response = await axios.get(`${API_URL}/weather/forecast/field/${selectedField}?days=10`);

        // Map the weather data to the frontend interface
        const forecast: WeatherForecast[] = response.data.forecast.map((day: any) => ({
          date: day.date,
          condition: day.condition,
          temperature_high: day.high,
          temperature_low: day.low,
          precipitation_chance: day.precipitation_chance || 0,
          wind_speed: typeof day.wind_speed === 'string' ? 
            parseInt(day.wind_speed.replace(/[^0-9]/g, '')) : 
            day.wind_speed || 0
        }));

        setWeatherForecast(forecast);

        // Generate harvest recommendations based on real weather data
        generateHarvestRecommendations(forecast);
      } catch (err) {
        console.error('Error fetching weather data:', err);

        // Fallback to empty forecast if API fails
        setWeatherForecast([]);
      }
    };

    fetchHarvestPlans();
    fetchWeatherData();
  }, [fields, farmId, selectedField, onScheduleChange]);

  // Helper function to map backend status to frontend status
  const mapStatusToFrontend = (backendStatus: string): 'scheduled' | 'in_progress' | 'completed' | 'cancelled' => {
    switch (backendStatus) {
      case 'planned':
        return 'scheduled';
      case 'in_progress':
        return 'in_progress';
      case 'completed':
        return 'completed';
      case 'cancelled':
        return 'cancelled';
      default:
        return 'scheduled';
    }
  };

  // Helper function to map frontend status to backend status
  const mapStatusToBackend = (frontendStatus: string): string => {
    switch (frontendStatus) {
      case 'scheduled':
        return 'planned';
      case 'in_progress':
        return 'in_progress';
      case 'completed':
        return 'completed';
      case 'cancelled':
        return 'cancelled';
      default:
        return 'planned';
    }
  };

  // Generate harvest recommendations based on weather forecast
  const generateHarvestRecommendations = async (forecast: WeatherForecast[]) => {
    if (!selectedField || !forecast.length) return;

    const field = fields.find(f => f.id === selectedField);
    if (!field) return;

    try {
      // Try to get recommendations from the backend API
      const response = await axios.get(`${API_URL}/ai-assistant/harvest-recommendations/${farmId}?fieldId=${selectedField}`);

      if (response.data && response.data.recommendations) {
        // Map backend recommendations to frontend format
        const apiRecommendations: Record<string, HarvestRecommendation[]> = {};

        response.data.recommendations.forEach((rec: any) => {
          if (rec.date) {
            apiRecommendations[rec.date] = [{
              date: rec.date,
              rating: rec.rating.toLowerCase(),
              reason: rec.reason
            }];
          }
        });

        if (Object.keys(apiRecommendations).length > 0) {
          setRecommendations(apiRecommendations);
          return;
        }
      }

      // Fallback to local recommendation generation if API fails or returns empty data
      const cropType = field.crop_type;
      const recommendations: Record<string, HarvestRecommendation[]> = {};

      // Define optimal conditions for different crop types
      const optimalConditions: Record<string, {
        minTemp: number;
        maxTemp: number;
        maxPrecipChance: number;
        maxWindSpeed: number;
      }> = {
        corn: { minTemp: 15, maxTemp: 30, maxPrecipChance: 20, maxWindSpeed: 15 },
        wheat: { minTemp: 15, maxTemp: 28, maxPrecipChance: 10, maxWindSpeed: 12 },
        soybeans: { minTemp: 18, maxTemp: 32, maxPrecipChance: 20, maxWindSpeed: 15 },
        barley: { minTemp: 15, maxTemp: 28, maxPrecipChance: 10, maxWindSpeed: 12 },
        oats: { minTemp: 15, maxTemp: 28, maxPrecipChance: 10, maxWindSpeed: 12 },
        // Add more crop types as needed
        default: { minTemp: 15, maxTemp: 30, maxPrecipChance: 20, maxWindSpeed: 15 }
      };

      const conditions = optimalConditions[cropType] || optimalConditions.default;

      // Generate recommendations for each day in the forecast
      forecast.forEach(day => {
        const dayRecommendations: HarvestRecommendation[] = [];

        // Check temperature
        const tempInRange = day.temperature_high >= conditions.minTemp && day.temperature_high <= conditions.maxTemp;

        // Check precipitation
        const lowPrecip = day.precipitation_chance <= conditions.maxPrecipChance;

        // Check wind speed
        const lowWind = day.wind_speed <= conditions.maxWindSpeed;

        // Determine overall rating
        let rating: 'optimal' | 'good' | 'fair' | 'poor' = 'poor';
        let reason = '';

        if (tempInRange && lowPrecip && lowWind) {
          rating = 'optimal';
          reason = 'Optimal conditions for harvesting.';
        } else if (tempInRange && (lowPrecip || lowWind)) {
          rating = 'good';
          reason = lowPrecip 
            ? 'Good temperature and low precipitation, but wind may be a factor.' 
            : 'Good temperature and low wind, but precipitation may be a factor.';
        } else if (tempInRange || (lowPrecip && lowWind)) {
          rating = 'fair';
          reason = tempInRange 
            ? 'Temperature is good, but weather conditions are not ideal.' 
            : 'Low precipitation and wind, but temperature is not ideal.';
        } else {
          reason = 'Poor conditions for harvesting due to temperature, precipitation, and/or wind.';
        }

        dayRecommendations.push({
          date: day.date,
          rating,
          reason
        });

        recommendations[day.date] = dayRecommendations;
      });

      setRecommendations(recommendations);
    } catch (err) {
      console.error('Error generating harvest recommendations:', err);

      // Fallback to empty recommendations if all methods fail
      setRecommendations({});
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { weekday: 'long', month: 'short', day: 'numeric' });
  };

  // Get color class for recommendation rating
  const getRatingColorClass = (rating: string) => {
    switch (rating) {
      case 'optimal':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'good':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'fair':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Handle creating a new harvest plan
  const handleCreatePlan = () => {
    setNewPlan({
      field_id: selectedField,
      planned_start_date: '',
      planned_end_date: '',
      status: 'scheduled',
      notes: ''
    });
    setEditingPlanId(null);
    setShowPlanForm(true);
  };

  // Handle editing an existing harvest plan
  const handleEditPlan = (planId: string) => {
    const planToEdit = harvestPlans.find(plan => plan.id === planId);
    if (planToEdit) {
      setNewPlan({
        field_id: planToEdit.field_id,
        planned_start_date: planToEdit.planned_start_date,
        planned_end_date: planToEdit.planned_end_date,
        status: planToEdit.status,
        notes: planToEdit.notes
      });
      setEditingPlanId(planId);
      setShowPlanForm(true);
    }
  };

  // Handle saving a harvest plan (create or update)
  const handleSavePlan = async (plan: HarvestPlan) => {
    try {
      if (editingPlanId) {
        // Update existing plan
        await axios.put(`${API_URL}/harvest-schedules/${editingPlanId}`, {
          scheduledDate: plan.planned_start_date,
          status: mapStatusToBackend(plan.status),
          notes: plan.notes,
          // If plan has an end date, include it
          ...(plan.planned_end_date && { actualEndDate: plan.planned_end_date })
        });

        // Refresh harvest plans
        const response = await axios.get(`${API_URL}/harvest-schedules/farm/${farmId}`);

        // Map the backend model to the frontend interface
        const updatedPlans: HarvestPlan[] = response.data.harvestSchedules.map((schedule: any) => ({
          id: schedule.id,
          field_id: schedule.field_id,
          planned_start_date: schedule.scheduled_date,
          planned_end_date: schedule.actual_end_date || new Date(new Date(schedule.scheduled_date).getTime() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: mapStatusToFrontend(schedule.status),
          notes: schedule.notes || '',
          created_at: schedule.created_at,
          updated_at: schedule.updated_at
        }));

        setHarvestPlans(updatedPlans);

        if (onScheduleChange) {
          onScheduleChange(updatedPlans);
        }
      } else {
        // Create new plan
        await axios.post(`${API_URL}/harvest-schedules`, {
          farmId,
          fieldId: plan.field_id,
          cropId: fields.find(f => f.id === plan.field_id)?.crop_type || '',
          scheduledDate: plan.planned_start_date,
          status: mapStatusToBackend(plan.status),
          notes: plan.notes,
          weatherDependent: true
        });

        // Refresh harvest plans
        const response = await axios.get(`${API_URL}/harvest-schedules/farm/${farmId}`);

        // Map the backend model to the frontend interface
        const updatedPlans: HarvestPlan[] = response.data.harvestSchedules.map((schedule: any) => ({
          id: schedule.id,
          field_id: schedule.field_id,
          planned_start_date: schedule.scheduled_date,
          planned_end_date: schedule.actual_end_date || new Date(new Date(schedule.scheduled_date).getTime() + 3 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          status: mapStatusToFrontend(schedule.status),
          notes: schedule.notes || '',
          created_at: schedule.created_at,
          updated_at: schedule.updated_at
        }));

        setHarvestPlans(updatedPlans);

        if (onScheduleChange) {
          onScheduleChange(updatedPlans);
        }
      }
    } catch (err) {
      console.error('Error saving harvest plan:', err);
      setError('Failed to save harvest plan. Please try again later.');
    }

    // Close the form
    setShowPlanForm(false);
    setEditingPlanId(null);
  };

  // Handle canceling the form
  const handleCancelPlanForm = () => {
    setShowPlanForm(false);
    setEditingPlanId(null);
  };

  // Handle deleting a harvest plan
  const handleDeletePlan = async (planId: string) => {
    if (window.confirm('Are you sure you want to delete this harvest plan?')) {
      try {
        // Delete the harvest plan from the backend
        await axios.delete(`${API_URL}/harvest-schedules/${planId}`);

        // Update the local state
        const updatedPlans = harvestPlans.filter(p => p.id !== planId);
        setHarvestPlans(updatedPlans);

        if (onScheduleChange) {
          onScheduleChange(updatedPlans);
        }
      } catch (err) {
        console.error('Error deleting harvest plan:', err);
        setError('Failed to delete harvest plan. Please try again later.');
      }
    }
  };

  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900">Harvest Schedule</h3>
        <p className="mt-1 max-w-2xl text-sm text-gray-500">
          Plan and track your harvesting schedule based on weather forecasts.
        </p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mx-6 mb-4" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div className="mb-6">
          <label htmlFor="field-select" className="block text-sm font-medium text-gray-700 mb-1">
            Select Field
          </label>
          <select
            id="field-select"
            value={selectedField}
            onChange={(e) => setSelectedField(e.target.value)}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            {fields.length === 0 ? (
              <option value="">No harvestable fields available</option>
            ) : (
              fields.map(field => (
                <option key={field.id} value={field.id}>
                  {field.name} - {field.crop_type} ({field.size} {field.size_unit})
                </option>
              ))
            )}
          </select>
        </div>

        {selectedField && (
          <>
            {/* Weather Forecast */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-3">10-Day Weather Forecast</h4>
              <div className="overflow-x-auto">
                <div className="inline-block min-w-full align-middle">
                  <div className="overflow-hidden border border-gray-200 rounded-lg">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Condition
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Temperature
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Precipitation
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Wind
                          </th>
                          <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Harvest Rating
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {weatherForecast.map((day, index) => {
                          const dayRecommendations = recommendations[day.date] || [];
                          const bestRecommendation = dayRecommendations[0]; // Assume first recommendation is the best

                          return (
                            <tr key={index}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {formatDate(day.date)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {day.condition}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {day.temperature_high}°C / {day.temperature_low}°C
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {day.precipitation_chance}%
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {day.wind_speed} km/h
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm">
                                {bestRecommendation ? (
                                  <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getRatingColorClass(bestRecommendation.rating)}`}>
                                    {bestRecommendation.rating.charAt(0).toUpperCase() + bestRecommendation.rating.slice(1)}
                                  </span>
                                ) : (
                                  <span className="text-gray-500">N/A</span>
                                )}
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>

            {/* Harvest Recommendations */}
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-3">Harvest Recommendations</h4>
              <div className="space-y-4">
                {Object.entries(recommendations).map(([date, recs]) => {
                  if (!recs.length) return null;

                  const bestRec = recs[0]; // Assume first recommendation is the best

                  return (
                    <div key={date} className={`p-4 border rounded-md ${getRatingColorClass(bestRec.rating)}`}>
                      <div className="flex justify-between items-start">
                        <h5 className="text-sm font-medium">{formatDate(date)}</h5>
                        <span className="text-xs font-medium uppercase">{bestRec.rating}</span>
                      </div>
                      <p className="mt-2 text-sm">{bestRec.reason}</p>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Harvest Plans */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-3">
                <h4 className="text-md font-medium text-gray-900">Harvest Plans</h4>
                <button
                  type="button"
                  onClick={handleCreatePlan}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                >
                  Create Plan
                </button>
              </div>

              {harvestPlans.length > 0 ? (
                <div className="space-y-4">
                  {harvestPlans
                    .filter(plan => plan.field_id === selectedField)
                    .map(plan => {
                      const field = fields.find(f => f.id === plan.field_id);

                      return (
                        <div key={plan.id} className="p-4 border rounded-md bg-white">
                          <div className="flex justify-between items-start">
                            <div>
                              <h5 className="text-sm font-medium text-gray-900">
                                {field?.name} Harvest
                              </h5>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatDate(plan.planned_start_date)} to {formatDate(plan.planned_end_date)}
                              </p>
                            </div>
                            <div className="flex space-x-2">
                              <button
                                type="button"
                                onClick={() => handleEditPlan(plan.id!)}
                                className="text-xs text-primary-600 hover:text-primary-900"
                              >
                                Edit
                              </button>
                              <button
                                type="button"
                                onClick={() => handleDeletePlan(plan.id!)}
                                className="text-xs text-red-600 hover:text-red-900"
                              >
                                Delete
                              </button>
                            </div>
                          </div>
                          <div className="mt-2">
                            <span className={`px-2 py-0.5 text-xs rounded-full ${
                              plan.status === 'scheduled' ? 'bg-blue-100 text-blue-800' :
                              plan.status === 'in_progress' ? 'bg-yellow-100 text-yellow-800' :
                              plan.status === 'completed' ? 'bg-green-100 text-green-800' :
                              'bg-red-100 text-red-800'
                            }`}>
                              {plan.status.replace('_', ' ').charAt(0).toUpperCase() + plan.status.replace('_', ' ').slice(1)}
                            </span>
                          </div>
                          {plan.notes && (
                            <p className="mt-2 text-sm text-gray-600">{plan.notes}</p>
                          )}
                        </div>
                      );
                    })}
                </div>
              ) : (
                <div className="text-center py-4 bg-gray-50 rounded-md">
                  <p className="text-sm text-gray-500">No harvest plans yet. Click "Create Plan" to schedule a harvest.</p>
                </div>
              )}
            </div>
          </>
        )}

        {/* Harvest Plan Form */}
        {showPlanForm && (
          <div className="mt-6">
            <HarvestPlanForm
              farmId={farmId}
              planId={editingPlanId || undefined}
              recommendations={recommendations}
              onSave={handleSavePlan}
              onCancel={handleCancelPlanForm}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default HarvestSchedule;
