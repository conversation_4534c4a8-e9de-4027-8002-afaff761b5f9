import { useState, useEffect } from 'react';
import { customDomainService } from '../services/customDomainService';

interface CustomDomainManagerProps {
  farmId: string;
  featureEnabled: boolean;
}

const CustomDomainManager: React.FC<CustomDomainManagerProps> = ({ farmId, featureEnabled }) => {
  const [customDomain, setCustomDomain] = useState<string>('');
  const [currentDomain, setCurrentDomain] = useState<string | null>(null);
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [processing, setProcessing] = useState<boolean>(false);

  useEffect(() => {
    const fetchCustomDomain = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await customDomainService.getCustomDomain(farmId);
        
        setCurrentDomain(response.customDomain);
        setIsVerified(response.verified);
      } catch (err: any) {
        console.error('Error fetching custom domain:', err);
        setError(err.response?.data?.error || 'Failed to fetch custom domain information');
      } finally {
        setLoading(false);
      }
    };

    fetchCustomDomain();
  }, [farmId]);

  const handleSetupDomain = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!customDomain.trim()) {
      setError('Please enter a domain name');
      return;
    }

    try {
      setProcessing(true);
      setError(null);
      setSuccess(null);
      
      await customDomainService.setupCustomDomain(farmId, customDomain);
      
      setCurrentDomain(customDomain);
      setIsVerified(false);
      setCustomDomain('');
      setSuccess('Custom domain set up successfully. Please verify it to complete the setup.');
    } catch (err: any) {
      console.error('Error setting up custom domain:', err);
      setError(err.response?.data?.error || 'Failed to set up custom domain');
    } finally {
      setProcessing(false);
    }
  };

  const handleVerifyDomain = async () => {
    try {
      setProcessing(true);
      setError(null);
      setSuccess(null);
      
      const response = await customDomainService.verifyCustomDomain(farmId);
      
      setIsVerified(response.verified);
      
      if (response.verified) {
        setSuccess('Custom domain verified successfully!');
      } else {
        setError('Domain verification failed. Please check your DNS settings and try again.');
      }
    } catch (err: any) {
      console.error('Error verifying custom domain:', err);
      setError(err.response?.data?.error || 'Failed to verify custom domain');
    } finally {
      setProcessing(false);
    }
  };

  const handleRemoveDomain = async () => {
    try {
      // Confirm with the user
      const confirmed = window.confirm('Are you sure you want to remove this custom domain?');
      if (!confirmed) return;
      
      setProcessing(true);
      setError(null);
      setSuccess(null);
      
      await customDomainService.removeCustomDomain(farmId);
      
      setCurrentDomain(null);
      setIsVerified(false);
      setSuccess('Custom domain removed successfully.');
    } catch (err: any) {
      console.error('Error removing custom domain:', err);
      setError(err.response?.data?.error || 'Failed to remove custom domain');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
        <span className="ml-2 text-sm text-gray-500">Loading custom domain information...</span>
      </div>
    );
  }

  if (!featureEnabled) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              Custom domain feature is not available with your current subscription plan.
              <a href="#" className="font-medium underline text-yellow-700 hover:text-yellow-600 ml-1">
                Upgrade your plan to access this feature.
              </a>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {currentDomain ? (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Current Custom Domain</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>Your farm is currently using the following custom domain:</p>
            </div>
            <div className="mt-3 flex items-center">
              <span className="text-lg font-semibold text-gray-900">{currentDomain}</span>
              <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                isVerified ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {isVerified ? 'Verified' : 'Pending Verification'}
              </span>
            </div>
            <div className="mt-5 flex space-x-3">
              {!isVerified && (
                <button
                  type="button"
                  onClick={handleVerifyDomain}
                  disabled={processing}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                    processing ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {processing ? 'Verifying...' : 'Verify Domain'}
                </button>
              )}
              <button
                type="button"
                onClick={handleRemoveDomain}
                disabled={processing}
                className={`inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-red-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 ${
                  processing ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {processing ? 'Removing...' : 'Remove Domain'}
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Set Up Custom Domain</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>Enter your custom domain to use with your farm. This will allow you to access your farm using your own domain name.</p>
            </div>
            <form onSubmit={handleSetupDomain} className="mt-5 sm:flex sm:items-center">
              <div className="w-full sm:max-w-xs">
                <label htmlFor="custom-domain" className="sr-only">Custom Domain</label>
                <input
                  type="text"
                  name="custom-domain"
                  id="custom-domain"
                  value={customDomain}
                  onChange={(e) => setCustomDomain(e.target.value)}
                  placeholder="example.com"
                  className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                />
              </div>
              <button
                type="submit"
                disabled={processing || !customDomain.trim()}
                className={`mt-3 w-full inline-flex items-center justify-center px-4 py-2 border border-transparent shadow-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm ${
                  (processing || !customDomain.trim()) ? 'opacity-70 cursor-not-allowed' : ''
                }`}
              >
                {processing ? 'Setting Up...' : 'Set Up Domain'}
              </button>
            </form>
          </div>
        </div>
      )}

      <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">How to Set Up Your Custom Domain</h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>Follow these steps to set up your custom domain:</p>
            <ol className="list-decimal list-inside mt-2 space-y-1">
              <li>Enter your domain name above and click "Set Up Domain"</li>
              <li>Go to your domain registrar (e.g., GoDaddy, Namecheap)</li>
              <li>Add a CNAME record for your domain pointing to your farm's subdomain</li>
              <li>Wait for DNS propagation (may take up to 24-48 hours)</li>
              <li>Click "Verify Domain" to complete the setup</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomDomainManager;