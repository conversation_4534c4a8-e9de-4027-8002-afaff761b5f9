import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface CameraFormProps {
  deviceType: string;
  configuration: any;
  onConfigurationChange: (config: any) => void;
}

const CameraForm = ({ deviceType, configuration, onConfigurationChange }: CameraFormProps) => {
  const [cameraConfig, setCameraConfig] = useState({
    cameraType: 'amcrest',
    serialNumber: '',
    username: '',
    password: '',
    p2pEnabled: true,
    streamUrl: '',
    ...configuration
  });

  // Update parent component when configuration changes
  useEffect(() => {
    onConfigurationChange(cameraConfig);
  }, [cameraConfig, onConfigurationChange]);

  // Only show this form for camera device types
  if (deviceType !== 'camera') {
    return null;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    setCameraConfig(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));

    // If serial number changes, update the stream URL
    if (name === 'serialNumber') {
      setCameraConfig(prev => ({
        ...prev,
        streamUrl: `p2p://${value}`
      }));
    }
  };

  return (
    <div className="mt-6 border-t border-gray-200 pt-6">
      <h3 className="text-lg font-medium text-gray-900">Camera Configuration</h3>
      <p className="mt-1 text-sm text-gray-500">
        Configure your Amcrest camera for P2P connectivity.
      </p>

      <div className="mt-6 grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-2">
        <div>
          <label htmlFor="cameraType" className="block text-sm font-medium text-gray-700">
            Camera Type
          </label>
          <select
            id="cameraType"
            name="cameraType"
            value={cameraConfig.cameraType}
            onChange={handleChange}
            className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md"
          >
            <option value="amcrest">Amcrest</option>
            <option value="other">Other</option>
          </select>
        </div>

        <div>
          <label htmlFor="serialNumber" className="block text-sm font-medium text-gray-700">
            Serial Number <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="serialNumber"
            id="serialNumber"
            value={cameraConfig.serialNumber}
            onChange={handleChange}
            required
            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
          />
          <p className="mt-1 text-xs text-gray-500">
            The serial number of your Amcrest camera (found on the camera or in its settings).
          </p>
        </div>

        <div>
          <label htmlFor="username" className="block text-sm font-medium text-gray-700">
            Username <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            name="username"
            id="username"
            value={cameraConfig.username}
            onChange={handleChange}
            required
            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
          />
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-700">
            Password <span className="text-red-500">*</span>
          </label>
          <input
            type="password"
            name="password"
            id="password"
            value={cameraConfig.password}
            onChange={handleChange}
            required
            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md"
          />
          <p className="mt-1 text-xs text-gray-500">
            Your password will be encrypted before being stored.
          </p>
        </div>

        <div className="sm:col-span-2">
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="p2pEnabled"
                name="p2pEnabled"
                type="checkbox"
                checked={cameraConfig.p2pEnabled}
                onChange={handleChange}
                className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="p2pEnabled" className="font-medium text-gray-700">
                Enable P2P Connection
              </label>
              <p className="text-gray-500">
                Connect to the camera using P2P technology (recommended for remote access).
              </p>
            </div>
          </div>
        </div>

        <div className="sm:col-span-2">
          <label htmlFor="streamUrl" className="block text-sm font-medium text-gray-700">
            Stream URL
          </label>
          <input
            type="text"
            name="streamUrl"
            id="streamUrl"
            value={cameraConfig.streamUrl}
            onChange={handleChange}
            disabled={cameraConfig.p2pEnabled}
            className="mt-1 focus:ring-primary-500 focus:border-primary-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md disabled:bg-gray-100 disabled:text-gray-500"
          />
          <p className="mt-1 text-xs text-gray-500">
            {cameraConfig.p2pEnabled 
              ? "Stream URL is automatically generated for P2P connections." 
              : "Enter the RTSP or HTTP stream URL for direct connection."}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CameraForm;