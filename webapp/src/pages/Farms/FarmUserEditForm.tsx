import { useState, useEffect, useContext } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { FarmContext } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

// Define interface for custom role
interface CustomRole {
  id: string;
  farm_id: string | null;
  name: string;
  description: string | null;
  is_system_role: boolean;
}

// Define interface for UserFarm
interface UserFarm {
  id: string;
  user_id: string;
  farm_id: string;
  role: string;
  is_approved: boolean;
  permissions: any;
  User: {
    id: string;
    email: string;
    first_name: string;
    last_name: string;
  };
}

const FarmUserEditForm = () => {
  const { farmId, userId } = useParams<{ farmId: string; userId: string }>();
  const [userFarm, setUserFarm] = useState<UserFarm | null>(null);
  const [role, setRole] = useState('');
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [customRoles, setCustomRoles] = useState<CustomRole[]>([]);

  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  // Fetch user farm data
  useEffect(() => {
    const fetchUserFarm = async () => {
      if (!farmId || !userId) return;

      try {
        setFetchLoading(true);
        setError(null);

        // First, get all user farms for this farm
        const response = await axios.get(`${API_URL}/user-farms/farm/${farmId}`);
        
        // Find the specific user farm for this user
        const userFarmData = response.data.userFarms.find(
          (uf: any) => uf.User.id === userId
        );

        if (!userFarmData) {
          throw new Error('User not found in this farm');
        }

        setUserFarm(userFarmData);
        setRole(userFarmData.role);
      } catch (err: any) {
        console.error('Error fetching user farm data:', err);
        setError(err.message || 'Failed to load user data');
      } finally {
        setFetchLoading(false);
      }
    };

    fetchUserFarm();
  }, [farmId, userId]);

  // Check if user is authenticated
  useEffect(() => {
    if (!user) {
      navigate('/login');
      return;
    }
  }, [user, navigate]);

  // Fetch custom roles for the farm
  useEffect(() => {
    const fetchCustomRoles = async () => {
      if (!farmId) return;

      try {
        const response = await axios.get(`${API_URL}/roles/farm/${farmId}`);
        setCustomRoles(response.data);
      } catch (err) {
        console.error('Error fetching custom farm roles:', err);
        // Don't set error state here to avoid disrupting the form
      }
    };

    fetchCustomRoles();
  }, [farmId]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      if (!farmId || !userId || !userFarm) {
        throw new Error('Missing required information');
      }

      // Update the user farm role
      await axios.put(`${API_URL}/user-farms/${userFarm.id}`, {
        role,
        is_approved: userFarm.is_approved
      });

      setSuccess(true);

      // Redirect back to farm detail after a short delay
      setTimeout(() => {
        navigate(`/farms/${farmId}`);
      }, 2000);
    } catch (err: any) {
      console.error('Error updating user role:', err);
      setError(err.message || 'Failed to update user role');
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <Layout>
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading user data...</p>
        </div>
      </Layout>
    );
  }

  if (!userFarm && !fetchLoading) {
    return (
      <Layout>
        <div className="bg-white shadow rounded-lg p-8 text-center">
          <p className="text-red-500">User not found in this farm.</p>
          <button
            onClick={() => navigate(`/farms/${farmId}`)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Back to Farm
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Edit Farm User</h1>
        <button
          type="button"
          onClick={() => navigate(`/farms/${farmId}`)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Cancel
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
          <button
            type="button"
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setError(null)}
          >
            <svg className="h-6 w-6 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      )}

      {success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">User role updated successfully!</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
          <h2 className="text-lg font-medium text-gray-900">User Details</h2>
        </div>
        <div className="p-6">
          {userFarm && (
            <form onSubmit={handleSubmit}>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  User
                </label>
                <div className="text-gray-900">
                  {userFarm.User.first_name} {userFarm.User.last_name} ({userFarm.User.email})
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
                  Role
                </label>
                <select
                  id="role"
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                >
                  {/* Standard roles */}
                  <optgroup label="Standard Roles">
                    <option value="farm_owner">Farm Owner</option>
                    <option value="farm_admin">Farm Admin</option>
                    <option value="farm_manager">Farm Manager</option>
                    <option value="farm_employee">Farm Employee</option>
                    <option value="accountant">Accountant</option>
                  </optgroup>

                  {/* Custom farm roles */}
                  {customRoles.length > 0 && (
                    <optgroup label="Custom Farm Roles">
                      {customRoles.map(customRole => (
                        <option key={customRole.id} value={customRole.name}>
                          {customRole.name}
                        </option>
                      ))}
                    </optgroup>
                  )}
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  <strong>Farm Owner:</strong> Full access to farm settings and user management<br />
                  <strong>Farm Admin:</strong> Can manage most farm data and some settings<br />
                  <strong>Farm Manager:</strong> Can manage farm operations but cannot change settings<br />
                  <strong>Farm Employee:</strong> Can view farm data with limited edit capabilities<br />
                  <strong>Accountant:</strong> Can view and manage financial data only<br />
                  {customRoles.length > 0 && (
                    <><strong>Custom Roles:</strong> Roles with permissions defined by farm owners</>
                  )}
                </p>
              </div>

              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={loading}
                  className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
                >
                  {loading ? 'Updating...' : 'Update Role'}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default FarmUserEditForm;