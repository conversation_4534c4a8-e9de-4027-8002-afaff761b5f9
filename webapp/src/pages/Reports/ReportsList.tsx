import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import Layout from '../../components/Layout';
import { 
  ChartBarIcon, 
  MapIcon, 
  TruckIcon, 
  CubeIcon, 
  CurrencyDollarIcon, 
  UserGroupIcon,
  ChartPieIcon
} from '@heroicons/react/24/outline';

interface ReportType {
  id: string;
  name: string;
  description: string;
  category: string;
  icon: React.ReactElement;
}

const ReportsList = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const { user } = useContext(AuthContext);

  // Define report types
  const reportTypes: ReportType[] = [
    {
      id: 'farm-performance',
      name: 'Farm Performance',
      description: 'Overall farm performance metrics including revenue, expenses, and profitability.',
      category: 'farm',
      icon: <ChartBarIcon className="h-8 w-8 text-primary-500" />
    },
    {
      id: 'field-production',
      name: 'Field Production',
      description: 'Yields and production metrics by field, crop type, and season.',
      category: 'field',
      icon: <MapIcon className="h-8 w-8 text-green-500" />
    },
    {
      id: 'equipment-utilization',
      name: 'Equipment Utilization',
      description: 'Usage and efficiency metrics for farm equipment.',
      category: 'equipment',
      icon: <TruckIcon className="h-8 w-8 text-blue-500" />
    },
    {
      id: 'inventory-status',
      name: 'Inventory Status',
      description: 'Current inventory levels, valuation, and reorder alerts.',
      category: 'inventory',
      icon: <CubeIcon className="h-8 w-8 text-yellow-500" />
    },
    {
      id: 'financial-summary',
      name: 'Financial Summary',
      description: 'Income, expenses, and profitability by time period and category.',
      category: 'financial',
      icon: <CurrencyDollarIcon className="h-8 w-8 text-red-500" />
    },
    {
      id: 'employee-productivity',
      name: 'Employee Productivity',
      description: 'Work hours, task completion, and productivity metrics by employee.',
      category: 'employee',
      icon: <UserGroupIcon className="h-8 w-8 text-purple-500" />
    }
  ];

  // Filter reports based on category and search term
  const filteredReports = reportTypes.filter(report => {
    const matchesCategory = selectedCategory === 'all' || report.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      report.name.toLowerCase().includes(searchTerm.toLowerCase()) || 
      report.description.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesCategory && matchesSearch;
  });

  // Handle category filter change
  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
      </div>

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2">
        {/* Category Filter */}
        <div>
          <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-1">
            Filter by Category
          </label>
          <select
            id="category-filter"
            value={selectedCategory}
            onChange={handleCategoryChange}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="all">All Categories</option>
            <option value="farm">Farm</option>
            <option value="field">Field</option>
            <option value="equipment">Equipment</option>
            <option value="inventory">Inventory</option>
            <option value="financial">Financial</option>
            <option value="employee">Employee</option>
          </select>
        </div>

        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
            Search Reports
          </label>
          <input
            type="text"
            id="search"
            value={searchTerm}
            onChange={handleSearchChange}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            placeholder="Search by name or description"
          />
        </div>
      </div>

      {/* Reports Grid */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
        {filteredReports.map(report => (
          <Link 
            key={report.id} 
            to={`/reports/${report.id}`}
            className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow duration-300"
          >
            <div className="p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  {report.icon}
                </div>
                <div className="ml-4">
                  <h3 className="text-lg font-medium text-gray-900">{report.name}</h3>
                  <p className="mt-1 text-sm text-gray-500">{report.description}</p>
                </div>
              </div>
              <div className="mt-4">
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                  {report.category.charAt(0).toUpperCase() + report.category.slice(1)}
                </span>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* No Results */}
      {filteredReports.length === 0 && (
        <div className="bg-white shadow rounded-lg p-6 text-center">
          <p className="text-gray-500">No reports found matching your criteria.</p>
        </div>
      )}

      {/* Dashboard Link */}
      <div className="mt-8 bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl font-medium text-gray-900">Farm Dashboard</h2>
            <p className="mt-1 text-sm text-gray-500">View your personalized farm dashboard with key metrics and insights.</p>
          </div>
          <Link
            to="/dashboard"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          >
            Go to Dashboard
          </Link>
        </div>
      </div>
    </Layout>
  );
};

export default ReportsList;
