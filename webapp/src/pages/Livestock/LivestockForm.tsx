import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface Livestock {
  id?: string;
  farm_id: string;
  type: string;
  breed: string;
  quantity: number;
  acquisition_date: string;
  acquisition_cost: number | null;
  status: string;
  notes: string;
}

const LivestockForm = () => {
  const { livestockId } = useParams<{ livestockId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!livestockId;

  const [livestock, setLivestock] = useState<Livestock>({
    farm_id: '',
    type: '',
    breed: '',
    quantity: 1,
    acquisition_date: new Date().toISOString().split('T')[0],
    acquisition_cost: null,
    status: 'active',
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Console log for debugging
  useEffect(() => {
    console.log("LivestockForm rendered", { user, selectedFarm, livestock });
  }, [user, selectedFarm, livestock]);

  // Set the selected farm when component mounts or selectedFarm changes
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      setLivestock(prev => ({ ...prev, farm_id: selectedFarm.id }));
    }
  }, [selectedFarm, isEditMode]);

  // Fetch livestock data if in edit mode
  useEffect(() => {
    const fetchLivestock = async () => {
      if (!livestockId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/livestock/${livestockId}`);

        // Format the date for the input field
        const formattedData = {
          ...response.data,
          acquisition_date: response.data.acquisition_date ? 
            new Date(response.data.acquisition_date).toISOString().split('T')[0] : 
            ''
        };

        setLivestock(formattedData);
      } catch (err: any) {
        console.error('Error fetching livestock:', err);
        setError('Failed to load livestock data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchLivestock();
    }
  }, [livestockId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setLivestock(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!selectedFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!livestock.type || livestock.quantity <= 0) {
        setError('Type and a positive quantity are required.');
        setLoading(false);
        return;
      }

      // Ensure farm_id is set from selectedFarm
      const livestockData = {
        ...livestock,
        farm_id: selectedFarm.id
      };

      if (isEditMode) {
        // Update existing livestock
        await axios.put(`${API_URL}/livestock/${livestockId}`, livestockData);
      } else {
        // Create new livestock
        await axios.post(`${API_URL}/livestock`, livestockData);
      }

      // Redirect to livestock list
      navigate('/livestock');
    } catch (err: any) {
      console.error('Error saving livestock:', err);
      setError('Failed to save livestock. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Livestock' : 'Add New Livestock'}
        </h1>
        <Link
          to="/livestock"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Livestock
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm Selection */}
            <div>
              <label htmlFor="farm_id" className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="p-2 border rounded bg-gray-50">
                {selectedFarm ? (
                  <span className="text-gray-700">{selectedFarm.name}</span>
                ) : (
                  <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                To change the farm, use the farm selector in the header.
              </p>
              <input
                type="hidden"
                id="farm_id"
                name="farm_id"
                value={selectedFarm?.id || ''}
              />
            </div>

            {/* Livestock Type */}
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
                Type <span className="text-red-500">*</span>
              </label>
              <select
                id="type"
                name="type"
                value={livestock.type}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              >
                <option value="" disabled>Select a type</option>
                <option value="cattle">Cattle</option>
                <option value="sheep">Sheep</option>
                <option value="goats">Goats</option>
                <option value="pigs">Pigs</option>
                <option value="chickens">Chickens</option>
                <option value="ducks">Ducks</option>
                <option value="horses">Horses</option>
                <option value="other">Other</option>
              </select>
            </div>

            {/* Breed */}
            <div>
              <label htmlFor="breed" className="block text-sm font-medium text-gray-700 mb-1">
                Breed
              </label>
              <input
                type="text"
                id="breed"
                name="breed"
                value={livestock.breed}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Quantity */}
            <div>
              <label htmlFor="quantity" className="block text-sm font-medium text-gray-700 mb-1">
                Quantity <span className="text-red-500">*</span>
              </label>
              <input
                type="number"
                id="quantity"
                name="quantity"
                value={livestock.quantity}
                onChange={handleChange}
                min="1"
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Acquisition Date */}
            <div>
              <label htmlFor="acquisition_date" className="block text-sm font-medium text-gray-700 mb-1">
                Acquisition Date
              </label>
              <input
                type="date"
                id="acquisition_date"
                name="acquisition_date"
                value={livestock.acquisition_date}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            {/* Acquisition Cost */}
            <div>
              <label htmlFor="acquisition_cost" className="block text-sm font-medium text-gray-700 mb-1">
                Acquisition Cost
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="acquisition_cost"
                  name="acquisition_cost"
                  value={livestock.acquisition_cost === null ? '' : livestock.acquisition_cost}
                  onChange={handleChange}
                  step="0.01"
                  min="0"
                  className="block w-full pl-7 pr-12 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                  placeholder="0.00"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="text-gray-500 sm:text-sm">USD</span>
                </div>
              </div>
            </div>

            {/* Status */}
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                name="status"
                value={livestock.status}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="sold">Sold</option>
                <option value="deceased">Deceased</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          {/* Notes */}
          <div className="mt-6">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              name="notes"
              value={livestock.notes}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/livestock"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Livestock'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default LivestockForm;
