import React, { useState } from 'react';
import { useAuth } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { Link } from 'react-router-dom';

const EquipmentDiagnostics = () => {
  const { user } = useAuth();
  const { currentFarm } = useFarm();
  const [selectedEquipment, setSelectedEquipment] = useState<string | null>(null);
  const [isScanning, setIsScanning] = useState<boolean>(false);
  const [scanProgress, setScanProgress] = useState<number>(0);
  const [diagnosticResults, setDiagnosticResults] = useState<{
    status: 'good' | 'warning' | 'error';
    issues: string[];
    recommendations: string[];
  } | null>(null);

  const equipment = [
    { id: 'tractor1', name: '<PERSON> Tractor', model: '8R 410', year: '2022' },
    { id: 'tractor2', name: 'Case IH Tractor', model: 'Magnum 400', year: '2021' },
    { id: 'harvester1', name: 'New Holland Harvester', model: 'CR10.90', year: '2023' },
    { id: 'sprayer1', name: 'John Deere Sprayer', model: 'R4060', year: '2022' }
  ];

  const handleStartScan = () => {
    if (!selectedEquipment) return;
    
    setIsScanning(true);
    setScanProgress(0);
    setDiagnosticResults(null);
    
    // Simulate scan progress
    const interval = setInterval(() => {
      setScanProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsScanning(false);
          
          // Generate mock diagnostic results
          const results = generateMockDiagnosticResults(selectedEquipment);
          setDiagnosticResults(results);
          
          return 100;
        }
        return prev + 5;
      });
    }, 200);
  };

  const generateMockDiagnosticResults = (equipmentId: string) => {
    // Mock diagnostic results based on equipment
    const resultOptions = [
      {
        status: 'good',
        issues: [],
        recommendations: ['Regular maintenance recommended in 250 hours', 'Check air filter at next service']
      },
      {
        status: 'warning',
        issues: ['Oil pressure slightly below optimal range', 'Battery voltage fluctuation detected'],
        recommendations: ['Schedule oil change within next 50 hours', 'Test battery and charging system']
      },
      {
        status: 'error',
        issues: ['Engine temperature exceeds normal operating range', 'Fuel system pressure irregularity detected', 'Hydraulic system error code E-235'],
        recommendations: ['Shut down engine and allow to cool', 'Inspect cooling system for blockages', 'Contact service technician for hydraulic system diagnosis']
      }
    ];
    
    // Randomly select a result, but weight toward good/warning for demo purposes
    const rand = Math.random();
    if (rand < 0.5) {
      return resultOptions[0] as {
        status: 'good' | 'warning' | 'error';
        issues: string[];
        recommendations: string[];
      };
    } else if (rand < 0.8) {
      return resultOptions[1] as {
        status: 'good' | 'warning' | 'error';
        issues: string[];
        recommendations: string[];
      };
    } else {
      return resultOptions[2] as {
        status: 'good' | 'warning' | 'error';
        issues: string[];
        recommendations: string[];
      };
    }
  };

  const getStatusColor = (status: 'good' | 'warning' | 'error') => {
    switch (status) {
      case 'good':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: 'good' | 'warning' | 'error') => {
    switch (status) {
      case 'good':
        return 'Good Condition';
      case 'warning':
        return 'Maintenance Needed';
      case 'error':
        return 'Service Required';
      default:
        return 'Unknown Status';
    }
  };

  return (
    <Layout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Equipment Diagnostics</h1>
            <p className="mt-2 text-sm text-gray-700">
              Diagnose equipment issues in the field using your mobile device. Connect to equipment via Bluetooth for real-time diagnostics.
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <Link
              to="/mobile-features"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              All Mobile Features
            </Link>
          </div>
        </div>

        {/* Mobile App Download Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Mobile App Required</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                To use equipment diagnostics features, you need to download the NxtAcre mobile app. The app is available for iOS and Android devices.
              </p>
            </div>
            <div className="mt-5 flex space-x-4">
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for iOS
              </a>
              <a
                href="#"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10s10-4.477 10-10C20 4.477 15.523 0 10 0zm5.5 15.5l-1.414 1.414L10 12.828l-4.086 4.086L4.5 15.5l4.086-4.086L4.5 7.328l1.414-1.414L10 9.172l4.086-4.086L15.5 6.5l-4.086 4.086L15.5 14.672z"/>
                </svg>
                Download for Android
              </a>
            </div>
          </div>
        </div>

        {/* Equipment Selection Section */}
        <div className="mt-8 bg-white shadow sm:rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Select Equipment</h3>
            <div className="mt-2 max-w-xl text-sm text-gray-500">
              <p>
                Choose equipment to diagnose. In the mobile app, you'll be able to connect to equipment via Bluetooth for real-time diagnostics.
              </p>
            </div>
            <div className="mt-5">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                {equipment.map((item) => (
                  <div 
                    key={item.id}
                    onClick={() => setSelectedEquipment(item.id)}
                    className={`relative rounded-lg border ${selectedEquipment === item.id ? 'border-primary-500 ring-2 ring-primary-500' : 'border-gray-300'} bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500 cursor-pointer`}
                  >
                    <div className="flex-shrink-0">
                      <svg className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div className="flex-1 min-w-0">
                      <a href="#" className="focus:outline-none">
                        <span className="absolute inset-0" aria-hidden="true"></span>
                        <p className="text-sm font-medium text-gray-900">{item.name}</p>
                        <p className="text-sm text-gray-500 truncate">{item.model} • {item.year}</p>
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-5">
              <button
                type="button"
                onClick={handleStartScan}
                disabled={!selectedEquipment || isScanning}
                className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white ${!selectedEquipment || isScanning ? 'bg-gray-400 cursor-not-allowed' : 'bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500'}`}
              >
                {isScanning ? 'Scanning...' : 'Start Diagnostic Scan'}
              </button>
            </div>
            
            {isScanning && (
              <div className="mt-6">
                <h4 className="text-sm font-medium text-gray-900">Scanning equipment...</h4>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                  <div className="bg-primary-600 h-2.5 rounded-full" style={{ width: `${scanProgress}%` }}></div>
                </div>
                <p className="mt-2 text-xs text-gray-500">{scanProgress}% complete</p>
              </div>
            )}
          </div>
        </div>

        {/* Diagnostic Results Section */}
        {diagnosticResults && (
          <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
            <div className="px-4 py-5 sm:px-6">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Diagnostic Results</h3>
              <p className="mt-1 max-w-2xl text-sm text-gray-500">
                Results from the diagnostic scan of your equipment.
              </p>
            </div>
            <div className="border-t border-gray-200">
              <dl>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Equipment</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {equipment.find(e => e.id === selectedEquipment)?.name} • {equipment.find(e => e.id === selectedEquipment)?.model}
                  </dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Status</dt>
                  <dd className="mt-1 text-sm sm:mt-0 sm:col-span-2">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(diagnosticResults.status)}`}>
                      {getStatusText(diagnosticResults.status)}
                    </span>
                  </dd>
                </div>
                <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Issues Detected</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    {diagnosticResults.issues.length > 0 ? (
                      <ul className="border border-gray-200 rounded-md divide-y divide-gray-200">
                        {diagnosticResults.issues.map((issue, index) => (
                          <li key={index} className="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                            <div className="w-0 flex-1 flex items-center">
                              <svg className="flex-shrink-0 h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                              </svg>
                              <span className="ml-2 flex-1 w-0 truncate">{issue}</span>
                            </div>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-green-600">No issues detected</p>
                    )}
                  </dd>
                </div>
                <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                  <dt className="text-sm font-medium text-gray-500">Recommendations</dt>
                  <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                    <ul className="border border-gray-200 rounded-md divide-y divide-gray-200">
                      {diagnosticResults.recommendations.map((recommendation, index) => (
                        <li key={index} className="pl-3 pr-4 py-3 flex items-center justify-between text-sm">
                          <div className="w-0 flex-1 flex items-center">
                            <svg className="flex-shrink-0 h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span className="ml-2 flex-1 w-0 truncate">{recommendation}</span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  </dd>
                </div>
              </dl>
            </div>
          </div>
        )}

        {/* Features Section */}
        <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
          <div className="px-4 py-5 sm:px-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">Equipment Diagnostic Features</h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Features available in the mobile app for equipment diagnostics.
            </p>
          </div>
          <div className="border-t border-gray-200 px-4 py-5 sm:p-6">
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-blue-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Real-time Diagnostics</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Connect to equipment via Bluetooth for real-time diagnostic information and error code reading.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-green-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Troubleshooting Guides</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Access step-by-step troubleshooting guides based on diagnostic results to resolve issues in the field.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-yellow-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Maintenance Reminders</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Receive maintenance reminders based on equipment usage and diagnostic data to prevent breakdowns.
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white overflow-hidden shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 bg-purple-100 rounded-md p-3">
                      <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div className="ml-5">
                      <h3 className="text-lg leading-6 font-medium text-gray-900">Service History</h3>
                      <div className="mt-2 text-sm text-gray-500">
                        Track service history and parts replacement for each piece of equipment to optimize maintenance schedules.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default EquipmentDiagnostics;