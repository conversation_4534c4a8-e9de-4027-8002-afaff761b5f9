import { useState, useContext, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';
import { getAuthToken } from '../../utils/storageUtils';

const TemplateForm = () => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [documentType, setDocumentType] = useState('agreement');
  const [file, setFile] = useState<File | null>(null);
  const [template, setTemplate] = useState<any>(null);
  const [isViewMode, setIsViewMode] = useState(false);

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { currentFarm } = useFarm();
  const navigate = useNavigate();
  const { id } = useParams();

  // Fetch template data if ID is provided
  useEffect(() => {
    if (id) {
      setIsViewMode(true);
      fetchTemplateData(id);
    }
  }, [id]);

  // Function to fetch template data
  const fetchTemplateData = async (templateId: string) => {
    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(
        `${API_URL}/document-signing/templates/${templateId}`,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`
          }
        }
      );

      const templateData = response.data;
      setTemplate(templateData);
      setTitle(templateData.title);
      setDescription(templateData.description || '');
      setDocumentType(templateData.document_type);
    } catch (err: any) {
      console.error('Error fetching template:', err);
      setError(err.response?.data?.error || 'Failed to load template. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!currentFarm?.id && !user?.farm_id) {
      setError('No farm selected. Please select a farm to create a template.');
      return;
    }

    if (!file) {
      setError('Please select a file to upload.');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('title', title);
      formData.append('description', description);
      formData.append('documentType', documentType);
      formData.append('file', file);

      // Create new template
      const response = await axios.post(
        `${API_URL}/document-signing/farm/${currentFarm?.id || user?.farm_id}/templates`,
        formData,
        {
          headers: {
            Authorization: `Bearer ${getAuthToken()}`,
            'Content-Type': 'multipart/form-data'
          }
        }
      );

      setSuccess('Template created successfully!');

      // Redirect to templates list after a short delay
      setTimeout(() => {
        navigate('/documents/signing/templates');
      }, 2000);
    } catch (err: any) {
      console.error('Error creating template:', err);
      setError(err.response?.data?.error || 'Failed to create template. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle file change
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            {isViewMode ? 'Template Details' : 'Create New Template'}
          </h1>
          <div>
            <button
              onClick={() => navigate('/documents/signing/templates')}
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Back to Templates
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-green-600">{success}</p>
          </div>
        )}

        {isViewMode ? (
          <div className="bg-white shadow-md rounded-lg p-6">
            {loading ? (
              <div className="flex justify-center items-center h-40">
                <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-primary-500"></div>
              </div>
            ) : (
              <>
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700">Title</h3>
                  <p className="mt-1 text-sm text-gray-900">{title}</p>
                </div>

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700">Description</h3>
                  <p className="mt-1 text-sm text-gray-900">{description || 'No description provided'}</p>
                </div>

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-gray-700">Document Type</h3>
                  <p className="mt-1 text-sm text-gray-900 capitalize">{documentType}</p>
                </div>

                {template && (
                  <>
                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-700">Created By</h3>
                      <p className="mt-1 text-sm text-gray-900">
                        {template.creator ? `${template.creator.first_name} ${template.creator.last_name}` : 'Unknown'}
                      </p>
                    </div>

                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-700">Created At</h3>
                      <p className="mt-1 text-sm text-gray-900">
                        {new Date(template.created_at).toLocaleString()}
                      </p>
                    </div>

                    <div className="mb-4">
                      <h3 className="text-sm font-medium text-gray-700">File Type</h3>
                      <p className="mt-1 text-sm text-gray-900">
                        {template.file_type ? template.file_type.toUpperCase() : 'Unknown'}
                      </p>
                    </div>

                    <div className="mb-6">
                      <h3 className="text-sm font-medium text-gray-700">File Size</h3>
                      <p className="mt-1 text-sm text-gray-900">
                        {template.file_size ? `${Math.round(template.file_size / 1024)} KB` : 'Unknown'}
                      </p>
                    </div>
                  </>
                )}

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => navigate('/documents/signing/templates')}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Back to Templates
                  </button>
                  {template && (
                    <button
                      type="button"
                      onClick={() => navigate(`/documents/signing/from-template/${template.id}`)}
                      className="ml-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                    >
                      Use Template
                    </button>
                  )}
                </div>
              </>
            )}
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="bg-white shadow-md rounded-lg p-6">
            <div className="mb-4">
              <label htmlFor="title" className="block text-sm font-medium text-gray-700">
                Title *
              </label>
              <input
                type="text"
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                Description
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                rows={3}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="documentType" className="block text-sm font-medium text-gray-700">
                Document Type *
              </label>
              <select
                id="documentType"
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                required
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="agreement">Agreement</option>
                <option value="contract">Contract</option>
                <option value="invoice">Invoice</option>
                <option value="receipt">Receipt</option>
                <option value="form">Form</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="mb-6">
              <label htmlFor="file" className="block text-sm font-medium text-gray-700">
                Template File *
              </label>
              <input
                type="file"
                id="file"
                onChange={handleFileChange}
                required
                className="mt-1 block w-full text-sm text-gray-500
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-medium
                  file:bg-primary-50 file:text-primary-700
                  hover:file:bg-primary-100"
              />
              <p className="mt-1 text-sm text-gray-500">
                Supported file types: PDF, DOCX, DOC, ODT
              </p>
            </div>

            <div className="flex justify-end">
              <button
                type="button"
                onClick={() => navigate('/documents/signing/templates')}
                className="mr-3 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Template'}
              </button>
            </div>
          </form>
        )}
      </div>
    </Layout>
  );
};

export default TemplateForm;
