import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../../config';

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unit_price: number;
  total: number;
}

interface Question {
  id: string;
  question: string;
  response: string | null;
  created_at: string;
  responded_at: string | null;
}

interface Invoice {
  id: string;
  invoice_number: string;
  date: string;
  due_date: string;
  total_amount: number;
  subtotal: number;
  tax_amount: number;
  tax_rate: number;
  status: string;
  farm_name: string;
  farm_id: string;
  line_items: LineItem[];
  questions: Question[];
  customer_pays_fees: boolean;
}

const CustomerInvoiceDetail: React.FC = () => {
  const { invoiceId } = useParams<{ invoiceId: string }>();
  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [newQuestion, setNewQuestion] = useState('');
  const [submittingQuestion, setSubmittingQuestion] = useState(false);
  const [questionError, setQuestionError] = useState<string | null>(null);
  const [questionSuccess, setQuestionSuccess] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchInvoiceDetails = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const token = localStorage.getItem('customerToken');
        if (!token) {
          navigate('/customer/login');
          return;
        }
        
        const response = await axios.get(`${API_URL}/customer/invoices/${invoiceId}`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });
        
        setInvoice(response.data.invoice);
      } catch (err: any) {
        console.error('Error fetching invoice details:', err);
        setError(err.response?.data?.error || 'Failed to fetch invoice details. Please try again later.');
        
        // If unauthorized, redirect to login
        if (err.response?.status === 401) {
          localStorage.removeItem('customerToken');
          navigate('/customer/login');
        }
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId) {
      fetchInvoiceDetails();
    }
  }, [invoiceId, navigate]);

  const handleSubmitQuestion = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newQuestion.trim()) {
      setQuestionError('Please enter a question');
      return;
    }

    try {
      setSubmittingQuestion(true);
      setQuestionError(null);
      setQuestionSuccess(false);
      
      const token = localStorage.getItem('customerToken');
      if (!token) {
        navigate('/customer/login');
        return;
      }
      
      await axios.post(
        `${API_URL}/customer/invoices/${invoiceId}/questions`,
        { question: newQuestion },
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );
      
      // Refresh invoice data to get updated questions
      const response = await axios.get(`${API_URL}/customer/invoices/${invoiceId}`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setInvoice(response.data.invoice);
      setNewQuestion('');
      setQuestionSuccess(true);
      
      // Clear success message after 3 seconds
      setTimeout(() => {
        setQuestionSuccess(false);
      }, 3000);
    } catch (err: any) {
      console.error('Error submitting question:', err);
      setQuestionError(err.response?.data?.error || 'Failed to submit question. Please try again.');
      
      // If unauthorized, redirect to login
      if (err.response?.status === 401) {
        localStorage.removeItem('customerToken');
        navigate('/customer/login');
      }
    } finally {
      setSubmittingQuestion(false);
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format datetime
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Get status badge class
  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'unpaid':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-6">
          <Link to="/customer/invoices" className="text-primary-600 hover:text-primary-900 flex items-center">
            <svg className="h-5 w-5 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
            </svg>
            Back to Invoices
          </Link>
        </div>

        {error && (
          <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {loading ? (
          <div className="flex justify-center items-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span className="ml-2 text-sm text-gray-500">Loading invoice details...</span>
          </div>
        ) : invoice ? (
          <div className="bg-white shadow overflow-hidden sm:rounded-lg">
            {/* Invoice Header */}
            <div className="px-4 py-5 sm:px-6 border-b border-gray-200">
              <div className="flex flex-wrap justify-between items-center">
                <div>
                  <h3 className="text-lg leading-6 font-medium text-gray-900">
                    Invoice #{invoice.invoice_number}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm text-gray-500">
                    From {invoice.farm_name}
                  </p>
                </div>
                <div className="mt-2 sm:mt-0">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(invoice.status)}`}>
                    {invoice.status.charAt(0).toUpperCase() + invoice.status.slice(1)}
                  </span>
                </div>
              </div>
            </div>

            {/* Invoice Details */}
            <div className="border-b border-gray-200 px-4 py-5 sm:px-6">
              <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Invoice Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(invoice.date)}</dd>
                </div>
                <div className="sm:col-span-1">
                  <dt className="text-sm font-medium text-gray-500">Due Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(invoice.due_date)}</dd>
                </div>
              </dl>
            </div>

            {/* Line Items */}
            <div className="px-4 py-5 sm:px-6">
              <h4 className="text-md font-medium text-gray-900 mb-4">Line Items</h4>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {invoice.line_items.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {item.description}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                          {item.quantity}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                          {formatCurrency(item.unit_price)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-right">
                          {formatCurrency(item.total)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                  <tfoot className="bg-gray-50">
                    <tr>
                      <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                        Subtotal
                      </th>
                      <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.subtotal)}
                      </td>
                    </tr>
                    <tr>
                      <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-500">
                        Tax ({invoice.tax_rate}%)
                      </th>
                      <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-900 text-right">
                        {formatCurrency(invoice.tax_amount)}
                      </td>
                    </tr>
                    <tr>
                      <th scope="row" colSpan={3} className="px-6 py-3 text-right text-sm font-medium text-gray-900">
                        Total
                      </th>
                      <td className="px-6 py-3 whitespace-nowrap text-sm font-medium text-gray-900 text-right">
                        {formatCurrency(invoice.total_amount)}
                      </td>
                    </tr>
                  </tfoot>
                </table>
              </div>
            </div>

            {/* Payment Button */}
            {invoice.status.toLowerCase() !== 'paid' && invoice.status.toLowerCase() !== 'cancelled' && (
              <div className="px-4 py-5 sm:px-6 border-t border-gray-200 bg-gray-50">
                <div className="flex justify-between items-center">
                  <div>
                    <h4 className="text-md font-medium text-gray-900">Payment</h4>
                    <p className="text-sm text-gray-500 mt-1">
                      Pay this invoice securely online
                      {invoice.customer_pays_fees && (
                        <span> (payment processing fees will apply)</span>
                      )}
                    </p>
                  </div>
                  <Link
                    to={`/customer/invoices/${invoice.id}/pay`}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Pay Now
                  </Link>
                </div>
              </div>
            )}

            {/* Questions Section */}
            <div className="px-4 py-5 sm:px-6 border-t border-gray-200">
              <h4 className="text-md font-medium text-gray-900 mb-4">Questions</h4>
              
              {/* Question Form */}
              <form onSubmit={handleSubmitQuestion} className="mb-6">
                <div>
                  <label htmlFor="question" className="block text-sm font-medium text-gray-700">
                    Ask a question about this invoice
                  </label>
                  <div className="mt-1">
                    <textarea
                      id="question"
                      name="question"
                      rows={3}
                      value={newQuestion}
                      onChange={(e) => setNewQuestion(e.target.value)}
                      className="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      placeholder="Enter your question here..."
                    />
                  </div>
                </div>
                
                {questionError && (
                  <div className="mt-2 text-sm text-red-600">
                    {questionError}
                  </div>
                )}
                
                {questionSuccess && (
                  <div className="mt-2 text-sm text-green-600">
                    Your question has been submitted successfully.
                  </div>
                )}
                
                <div className="mt-2">
                  <button
                    type="submit"
                    disabled={submittingQuestion}
                    className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${
                      submittingQuestion ? 'opacity-70 cursor-not-allowed' : ''
                    }`}
                  >
                    {submittingQuestion ? 'Submitting...' : 'Submit Question'}
                  </button>
                </div>
              </form>
              
              {/* Questions List */}
              {invoice.questions && invoice.questions.length > 0 ? (
                <div className="space-y-4">
                  {invoice.questions.map((q) => (
                    <div key={q.id} className="bg-gray-50 p-4 rounded-md">
                      <div className="mb-2">
                        <p className="text-sm font-medium text-gray-900">Question:</p>
                        <p className="text-sm text-gray-700">{q.question}</p>
                        <p className="text-xs text-gray-500 mt-1">
                          Asked on {formatDateTime(q.created_at)}
                        </p>
                      </div>
                      
                      {q.response && (
                        <div className="mt-3 pl-4 border-l-2 border-gray-300">
                          <p className="text-sm font-medium text-gray-900">Response:</p>
                          <p className="text-sm text-gray-700">{q.response}</p>
                          {q.responded_at && (
                            <p className="text-xs text-gray-500 mt-1">
                              Responded on {formatDateTime(q.responded_at)}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No questions have been asked about this invoice yet.</p>
              )}
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-gray-500">Invoice not found.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerInvoiceDetail;