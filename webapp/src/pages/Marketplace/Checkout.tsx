import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useCart } from '../../context/CartContext';
import { useAuth } from '../../context/AuthContext';
import { ShoppingCart } from '../../services/shoppingCartService';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import { Label } from '../../components/ui/Label';
import { Textarea } from '../../components/ui/Textarea';
import { RadioGroup, RadioGroupItem } from '../../components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/Select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/Tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/Card';
import { formatCurrency } from '../../utils/formatters';
import { ShoppingBag, Check, AlertCircle, Truck, MapPin, Calendar, CreditCard, User } from 'lucide-react';
import { CustomerAddress, getCustomerAddresses } from '../../services/marketplaceCustomerService';
import { getFarmFulfillmentOptions } from '../../services/fulfillmentOptionsService';

const Checkout: React.FC = () => {
  const [searchParams] = useSearchParams();
  const cartId = searchParams.get('cartId');
  const navigate = useNavigate();
  const { user } = useAuth();
  const { 
    cart, 
    carts, 
    loading, 
    error, 
    fetchCart, 
    fetchAllCarts, 
    checkoutCart,
    checkoutAllCarts
  } = useCart();

  // Customer information
  const [customerInfo, setCustomerInfo] = useState({
    name: user ? `${user.firstName} ${user.lastName}` : '',
    email: user?.email || '',
    phone: user?.phoneNumber || ''
  });

  // Fulfillment options
  const [fulfillmentOptions, setFulfillmentOptions] = useState<any>({});
  const [fulfillmentMethod, setFulfillmentMethod] = useState<'delivery' | 'pickup'>('delivery');

  // Address selection
  const [addresses, setAddresses] = useState<CustomerAddress[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string>('');
  const [showAddAddressForm, setShowAddAddressForm] = useState(false);
  const [newAddress, setNewAddress] = useState({
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    deliveryInstructions: ''
  });

  // Pickup date/time
  const [pickupDate, setPickupDate] = useState<string>('');
  const [pickupTime, setPickupTime] = useState<string>('');

  // Payment method
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'cash' | 'invoice'>('card');

  // Other checkout state
  const [checkoutError, setCheckoutError] = useState<string | null>(null);
  const [processingCheckout, setProcessingCheckout] = useState(false);
  const [notes, setNotes] = useState('');
  const [activeStep, setActiveStep] = useState<'customer-info' | 'fulfillment' | 'payment' | 'review'>('customer-info');

  useEffect(() => {
    // If cartId is provided, fetch that specific cart
    if (cartId) {
      fetchCart(cartId);
    } else {
      // Otherwise fetch all carts
      fetchAllCarts();
    }
  }, [cartId, fetchCart, fetchAllCarts]);

  // Fetch customer addresses if user is logged in
  useEffect(() => {
    const fetchAddresses = async () => {
      if (user) {
        try {
          const response = await getCustomerAddresses();
          setAddresses(response.addresses);

          // Set default address if available
          const defaultAddress = response.addresses.find(addr => addr.is_default);
          if (defaultAddress) {
            setSelectedAddressId(defaultAddress.id);
          } else if (response.addresses.length > 0) {
            setSelectedAddressId(response.addresses[0].id);
          }
        } catch (err) {
          console.error('Error fetching addresses:', err);
        }
      }
    };

    fetchAddresses();
  }, [user]);

  // Fetch fulfillment options for each farm
  useEffect(() => {
    const fetchFulfillmentOptions = async () => {
      const cartsToProcess = cartId && cart ? [cart] : carts;
      const options: Record<string, any> = {};

      for (const cart of cartsToProcess) {
        if (cart && cart.farm && cart.farm.id) {
          try {
            const farmOptions = await getFarmFulfillmentOptions(cart.farm.id);
            options[cart.farm.id] = farmOptions;
          } catch (err) {
            console.error(`Error fetching fulfillment options for farm ${cart.farm.id}:`, err);
          }
        }
      }

      setFulfillmentOptions(options);
    };

    if (!loading && (cart || carts.length > 0)) {
      fetchFulfillmentOptions();
    }
  }, [cart, carts, cartId, loading]);

  const handleCustomerInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setCustomerInfo(prev => ({ ...prev, [name]: value }));
  };

  const handleNewAddressChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewAddress(prev => ({ ...prev, [name]: value }));
  };

  const handleAddNewAddress = () => {
    // In a real implementation, this would save the address to the backend
    // For now, we'll just toggle back to the address selection
    setShowAddAddressForm(false);
  };

  const handleNextStep = () => {
    if (activeStep === 'customer-info') {
      setActiveStep('fulfillment');
    } else if (activeStep === 'fulfillment') {
      setActiveStep('payment');
    } else if (activeStep === 'payment') {
      setActiveStep('review');
    }
  };

  const handlePreviousStep = () => {
    if (activeStep === 'fulfillment') {
      setActiveStep('customer-info');
    } else if (activeStep === 'payment') {
      setActiveStep('fulfillment');
    } else if (activeStep === 'review') {
      setActiveStep('payment');
    }
  };

  const handleCheckout = async () => {
    setProcessingCheckout(true);
    setCheckoutError(null);

    try {
      // Prepare checkout data with all the collected information
      const checkoutData = {
        customerInfo,
        fulfillmentMethod,
        addressId: fulfillmentMethod === 'delivery' ? selectedAddressId : undefined,
        pickupDate: fulfillmentMethod === 'pickup' ? `${pickupDate}T${pickupTime}` : undefined,
        paymentMethod,
        notes
      };

      if (cartId && cart) {
        // Checkout a single cart
        await checkoutCart(cartId, notes);
      } else {
        // Checkout all carts
        await checkoutAllCarts(notes);
      }

      // Navigate to success page
      navigate('/marketplace/checkout-success');
    } catch (err) {
      console.error('Checkout error:', err);
      setCheckoutError('There was an error processing your checkout. Please try again.');
    } finally {
      setProcessingCheckout(false);
    }
  };

  // Determine which carts to display
  const cartsToShow: ShoppingCart[] = cartId && cart ? [cart] : carts;

  // Calculate totals
  const totalItems = cartsToShow.reduce((total, cart) => total + (cart?.itemCount || 0), 0);
  const totalPrice = cartsToShow.reduce((total, cart) => total + (cart?.totalPrice || 0), 0);

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-red-700 mb-2">Error Loading Cart</h2>
          <p className="text-red-600 mb-4">{error}</p>
          <Button 
            variant="outline" 
            onClick={() => cartId ? fetchCart(cartId) : fetchAllCarts()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  if (cartsToShow.length === 0 || cartsToShow.every(c => !c || c.items.length === 0)) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
          <ShoppingBag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Your Cart is Empty</h2>
          <p className="text-gray-600 mb-4">There are no items in your cart to checkout.</p>
          <Button 
            onClick={() => navigate('/marketplace')}
          >
            Continue Shopping
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Checkout</h1>

      {checkoutError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-600">{checkoutError}</p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <Tabs value={activeStep} className="mb-6">
            <TabsList className="grid grid-cols-4 mb-6">
              <TabsTrigger 
                value="customer-info" 
                onClick={() => setActiveStep('customer-info')}
                className="flex items-center gap-1"
              >
                <User className="h-4 w-4" />
                <span className="hidden sm:inline">Customer Info</span>
              </TabsTrigger>
              <TabsTrigger 
                value="fulfillment" 
                onClick={() => setActiveStep('fulfillment')}
                className="flex items-center gap-1"
              >
                <Truck className="h-4 w-4" />
                <span className="hidden sm:inline">Fulfillment</span>
              </TabsTrigger>
              <TabsTrigger 
                value="payment" 
                onClick={() => setActiveStep('payment')}
                className="flex items-center gap-1"
              >
                <CreditCard className="h-4 w-4" />
                <span className="hidden sm:inline">Payment</span>
              </TabsTrigger>
              <TabsTrigger 
                value="review" 
                onClick={() => setActiveStep('review')}
                className="flex items-center gap-1"
              >
                <Check className="h-4 w-4" />
                <span className="hidden sm:inline">Review</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="customer-info">
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                  <CardDescription>
                    Please provide your contact information
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="name">Full Name*</Label>
                        <Input
                          id="name"
                          name="name"
                          value={customerInfo.name}
                          onChange={handleCustomerInfoChange}
                          required
                          placeholder="John Doe"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address*</Label>
                        <Input
                          id="email"
                          name="email"
                          type="email"
                          value={customerInfo.email}
                          onChange={handleCustomerInfoChange}
                          required
                          placeholder="<EMAIL>"
                        />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number*</Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={customerInfo.phone}
                        onChange={handleCustomerInfoChange}
                        required
                        placeholder="(*************"
                      />
                    </div>
                  </div>
                </CardContent>
                <div className="p-6 pt-0 flex justify-end">
                  <Button onClick={handleNextStep}>
                    Continue to Fulfillment
                  </Button>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="fulfillment">
              <Card>
                <CardHeader>
                  <CardTitle>Fulfillment Options</CardTitle>
                  <CardDescription>
                    Choose how you'd like to receive your order
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <RadioGroup 
                      value={fulfillmentMethod} 
                      onValueChange={(value) => setFulfillmentMethod(value as 'delivery' | 'pickup')}
                      className="space-y-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="delivery" id="delivery" />
                        <Label htmlFor="delivery" className="flex items-center gap-2">
                          <Truck className="h-4 w-4" />
                          Delivery
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="pickup" id="pickup" />
                        <Label htmlFor="pickup" className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          Pickup
                        </Label>
                      </div>
                    </RadioGroup>

                    {fulfillmentMethod === 'delivery' && (
                      <div className="mt-6 space-y-4">
                        <h3 className="text-lg font-medium">Delivery Address</h3>

                        {!showAddAddressForm && (
                          <>
                            {addresses.length > 0 ? (
                              <div className="space-y-4">
                                <Select 
                                  value={selectedAddressId} 
                                  onValueChange={setSelectedAddressId}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select an address" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {addresses.map(address => (
                                      <SelectItem key={address.id} value={address.id}>
                                        {address.farm_alias ? `${address.farm_alias}: ` : ''}
                                        {address.address}, {address.city}, {address.state} {address.zip_code}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>

                                <Button 
                                  variant="outline" 
                                  onClick={() => setShowAddAddressForm(true)}
                                >
                                  Add New Address
                                </Button>
                              </div>
                            ) : (
                              <div className="text-center py-4">
                                <p className="text-gray-500 mb-4">You don't have any saved addresses.</p>
                                <Button onClick={() => setShowAddAddressForm(true)}>
                                  Add New Address
                                </Button>
                              </div>
                            )}
                          </>
                        )}

                        {showAddAddressForm && (
                          <div className="space-y-4 border p-4 rounded-md">
                            <h4 className="font-medium">New Address</h4>
                            <div className="space-y-2">
                              <Label htmlFor="address">Street Address*</Label>
                              <Input
                                id="address"
                                name="address"
                                value={newAddress.address}
                                onChange={handleNewAddressChange}
                                required
                                placeholder="123 Main St"
                              />
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor="city">City*</Label>
                                <Input
                                  id="city"
                                  name="city"
                                  value={newAddress.city}
                                  onChange={handleNewAddressChange}
                                  required
                                  placeholder="Anytown"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="state">State*</Label>
                                <Input
                                  id="state"
                                  name="state"
                                  value={newAddress.state}
                                  onChange={handleNewAddressChange}
                                  required
                                  placeholder="CA"
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor="zipCode">ZIP Code*</Label>
                                <Input
                                  id="zipCode"
                                  name="zipCode"
                                  value={newAddress.zipCode}
                                  onChange={handleNewAddressChange}
                                  required
                                  placeholder="12345"
                                />
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor="deliveryInstructions">Delivery Instructions (Optional)</Label>
                              <Textarea
                                id="deliveryInstructions"
                                name="deliveryInstructions"
                                value={newAddress.deliveryInstructions}
                                onChange={handleNewAddressChange}
                                placeholder="Special instructions for delivery"
                                rows={3}
                              />
                            </div>

                            <div className="flex justify-end space-x-2">
                              <Button 
                                variant="outline" 
                                onClick={() => setShowAddAddressForm(false)}
                              >
                                Cancel
                              </Button>
                              <Button onClick={handleAddNewAddress}>
                                Save Address
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    )}

                    {fulfillmentMethod === 'pickup' && (
                      <div className="mt-6 space-y-4">
                        <h3 className="text-lg font-medium">Pickup Details</h3>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="pickupDate">Pickup Date*</Label>
                            <Input
                              id="pickupDate"
                              name="pickupDate"
                              type="date"
                              value={pickupDate}
                              onChange={(e) => setPickupDate(e.target.value)}
                              required
                            />
                          </div>
                          <div className="space-y-2">
                            <Label htmlFor="pickupTime">Pickup Time*</Label>
                            <Input
                              id="pickupTime"
                              name="pickupTime"
                              type="time"
                              value={pickupTime}
                              onChange={(e) => setPickupTime(e.target.value)}
                              required
                            />
                          </div>
                        </div>

                        <div className="bg-gray-50 p-4 rounded-md">
                          <h4 className="font-medium mb-2">Pickup Instructions</h4>
                          <p className="text-sm text-gray-600">
                            Please arrive during business hours. Call the farm when you arrive, and someone will bring your order out to you.
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
                <div className="p-6 pt-0 flex justify-between">
                  <Button variant="outline" onClick={handlePreviousStep}>
                    Back
                  </Button>
                  <Button onClick={handleNextStep}>
                    Continue to Payment
                  </Button>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="payment">
              <Card>
                <CardHeader>
                  <CardTitle>Payment Method</CardTitle>
                  <CardDescription>
                    Choose how you'd like to pay for your order
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup 
                    value={paymentMethod} 
                    onValueChange={(value) => setPaymentMethod(value as 'card' | 'cash' | 'invoice')}
                    className="space-y-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="card" id="card" />
                      <Label htmlFor="card" className="flex items-center gap-2">
                        <CreditCard className="h-4 w-4" />
                        Credit/Debit Card
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="cash" id="cash" />
                      <Label htmlFor="cash">Cash on Delivery/Pickup</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="invoice" id="invoice" />
                      <Label htmlFor="invoice">Invoice (for approved customers)</Label>
                    </div>
                  </RadioGroup>

                  {paymentMethod === 'card' && (
                    <div className="mt-6 p-4 bg-gray-50 rounded-md">
                      <p className="text-sm text-gray-600">
                        You'll receive payment instructions from the farm after they approve your order.
                      </p>
                    </div>
                  )}
                </CardContent>
                <div className="p-6 pt-0 flex justify-between">
                  <Button variant="outline" onClick={handlePreviousStep}>
                    Back
                  </Button>
                  <Button onClick={handleNextStep}>
                    Review Order
                  </Button>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="review">
              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-xl font-semibold mb-4">Order Summary</h2>

                {cartsToShow.map((cart, index) => (
                  cart && (
                    <div key={cart.id} className="mb-6">
                      {cartsToShow.length > 1 && (
                        <div className="flex items-center pb-2 mb-4 border-b">
                          {cart.farm.logo_url ? (
                            <img 
                              src={cart.farm.logo_url} 
                              alt={cart.farm.name} 
                              className="w-8 h-8 rounded-full mr-2"
                            />
                          ) : (
                            <ShoppingBag className="w-8 h-8 text-gray-400 mr-2" />
                          )}
                          <h3 className="font-semibold">{cart.farm.name}</h3>
                        </div>
                      )}

                      <div className="space-y-4">
                        {cart.items.map(item => (
                          <div key={item.id} className="flex border-b pb-4">
                            <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden mr-3 flex-shrink-0">
                              {item.product.images && item.product.images.length > 0 ? (
                                <img 
                                  src={item.product.images[0].file_path} 
                                  alt={item.product.name} 
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center bg-gray-200">
                                  <span className="text-gray-400 text-xs">No image</span>
                                </div>
                              )}
                            </div>

                            <div className="flex-1">
                              <h3 className="font-medium">{item.product.name}</h3>
                              <div className="flex justify-between text-sm text-gray-500">
                                <span>Qty: {item.quantity}</span>
                                <span>{formatCurrency(item.product.price * item.quantity)}</span>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>

                      {cartsToShow.length > 1 && index < cartsToShow.length - 1 && (
                        <div className="border-t border-dashed my-6"></div>
                      )}
                    </div>
                  )
                ))}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Customer Information</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p><span className="font-medium">Name:</span> {customerInfo.name}</p>
                      <p><span className="font-medium">Email:</span> {customerInfo.email}</p>
                      <p><span className="font-medium">Phone:</span> {customerInfo.phone}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Fulfillment Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <p><span className="font-medium">Method:</span> {fulfillmentMethod === 'delivery' ? 'Delivery' : 'Pickup'}</p>

                      {fulfillmentMethod === 'delivery' && selectedAddressId && (
                        <div>
                          <p className="font-medium">Delivery Address:</p>
                          {addresses.find(a => a.id === selectedAddressId) && (
                            <div className="text-sm">
                              <p>{addresses.find(a => a.id === selectedAddressId)?.address}</p>
                              <p>
                                {addresses.find(a => a.id === selectedAddressId)?.city}, 
                                {addresses.find(a => a.id === selectedAddressId)?.state} 
                                {addresses.find(a => a.id === selectedAddressId)?.zip_code}
                              </p>
                            </div>
                          )}
                        </div>
                      )}

                      {fulfillmentMethod === 'pickup' && (
                        <p><span className="font-medium">Pickup Time:</span> {pickupDate} at {pickupTime}</p>
                      )}

                      <p><span className="font-medium">Payment Method:</span> {
                        paymentMethod === 'card' ? 'Credit/Debit Card' : 
                        paymentMethod === 'cash' ? 'Cash on Delivery/Pickup' : 
                        'Invoice'
                      }</p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <div className="bg-white rounded-lg shadow-md p-6 mb-6">
                <h2 className="text-xl font-semibold mb-4">Order Notes</h2>
                <textarea
                  className="w-full border rounded-md p-2 h-32"
                  placeholder="Add any special instructions or notes about your order..."
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                ></textarea>
              </div>

              <div className="flex justify-between">
                <Button variant="outline" onClick={handlePreviousStep}>
                  Back
                </Button>
                <Button 
                  onClick={handleCheckout}
                  disabled={processingCheckout}
                >
                  {processingCheckout ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    'Submit Order Request'
                  )}
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="md:col-span-1">
          <div className="bg-white rounded-lg shadow-md p-6 sticky top-6">
            <h2 className="text-xl font-semibold mb-4">Order Total</h2>

            <div className="space-y-2 mb-6">
              <div className="flex justify-between">
                <span>Subtotal ({totalItems} items)</span>
                <span>{formatCurrency(totalPrice)}</span>
              </div>

              {/* Add tax, shipping, etc. here if needed */}

              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>{formatCurrency(totalPrice)}</span>
                </div>
              </div>
            </div>

            {activeStep === 'review' ? (
              <Button 
                className="w-full"
                onClick={handleCheckout}
                disabled={processingCheckout}
              >
                {processingCheckout ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  'Submit Order Request'
                )}
              </Button>
            ) : (
              <Button 
                className="w-full"
                onClick={handleNextStep}
              >
                Continue to {
                  activeStep === 'customer-info' ? 'Fulfillment' :
                  activeStep === 'fulfillment' ? 'Payment' :
                  'Review'
                }
              </Button>
            )}

            <p className="text-sm text-gray-500 mt-4 text-center">
              By submitting your order, you're sending a purchase request to the farm(s).
              They will review your request and contact you to arrange payment and delivery.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
