import React, { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';

// Lazy load components
const AlertRuleList = lazy(() => import('./AlertRuleList'));
const AlertRuleForm = lazy(() => import('./AlertRuleForm'));
const AlertDashboard = lazy(() => import('./AlertDashboard'));

// Loading component
const ComponentLoader = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
    <p className="ml-3 text-gray-500">Loading...</p>
  </div>
);

const AlertsRoutes: React.FC = () => {
  return (
    <Routes>
      <Route path="/" element={
        <Suspense fallback={<ComponentLoader />}>
          <AlertDashboard />
        </Suspense>
      } />
      <Route path="/rules" element={
        <Suspense fallback={<ComponentLoader />}>
          <AlertRuleList />
        </Suspense>
      } />
      <Route path="/rules/new" element={
        <Suspense fallback={<ComponentLoader />}>
          <AlertRuleForm />
        </Suspense>
      } />
      <Route path="/rules/:ruleId/edit" element={
        <Suspense fallback={<ComponentLoader />}>
          <AlertRuleForm />
        </Suspense>
      } />
    </Routes>
  );
};

export default AlertsRoutes;
