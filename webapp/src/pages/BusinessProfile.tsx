import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../context/AuthContext';
import Layout from '../components/Layout';
import LocationAutocomplete from '../components/LocationAutocomplete';
import { loadGoogleMapsApi } from '../utils/googleMapsLoader';
import { API_URL } from '../config';

interface BusinessListing {
  id: string;
  user_id: string;
  name: string;
  contact_name: string;
  email: string;
  phone: string;
  address: string;
  website: string;
  description: string;
  payment_terms: string;
  is_active: boolean;
  product_types: string[];
  api_key: string;
  api_endpoint: string;
}

const BusinessProfile: React.FC = () => {
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [businessListing, setBusinessListing] = useState<BusinessListing | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoadingMapsApi, setIsLoadingMapsApi] = useState(true);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    contact_name: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    country: 'USA',
    website: '',
    description: '',
    payment_terms: '',
    product_types: [] as string[],
    api_key: '',
    api_endpoint: ''
  });
  
  // Product type options
  const productTypeOptions = user?.userType === 'supplier' 
    ? ['Feed', 'Seed', 'Fertilizer', 'Chemicals', 'Equipment', 'Parts', 'Fuel', 'Other']
    : ['Large Animal', 'Small Animal', 'Equine', 'Poultry', 'Exotic', 'Emergency', 'Surgery', 'Other'];

  // Load Google Maps API
  useEffect(() => {
    const loadMapsApi = async () => {
      try {
        await loadGoogleMapsApi(['places']);
        setIsLoadingMapsApi(false);
      } catch (error) {
        console.error('Failed to load Google Maps API:', error);
        setIsLoadingMapsApi(false);
      }
    };

    loadMapsApi();
  }, []);

  // Fetch business listing data
  useEffect(() => {
    const fetchBusinessListing = async () => {
      if (!user) {
        navigate('/login');
        return;
      }
      
      // Check if user is a business owner
      if (!user.isBusinessOwner) {
        navigate('/dashboard');
        return;
      }
      
      setLoading(true);
      setError(null);
      
      try {
        // Determine the endpoint based on user type
        const endpoint = user.userType === 'supplier' 
          ? `${API_URL}/suppliers/user/${user.id}`
          : `${API_URL}/vets/user/${user.id}`;
        
        const response = await axios.get(endpoint);
        
        if (response.data) {
          setBusinessListing(response.data);
          
          // Initialize form data
          setFormData({
            name: response.data.name || '',
            contact_name: response.data.contact_name || '',
            email: response.data.email || '',
            phone: response.data.phone || '',
            address: response.data.address || '',
            city: response.data.city || '',
            state: response.data.state || '',
            zipCode: response.data.zip_code || '',
            country: response.data.country || 'USA',
            website: response.data.website || '',
            description: response.data.description || '',
            payment_terms: response.data.payment_terms || '',
            product_types: response.data.product_types || [],
            api_key: response.data.api_key || '',
            api_endpoint: response.data.api_endpoint || ''
          });
        }
      } catch (err: any) {
        console.error('Error fetching business listing:', err);
        setError('Failed to load business listing. Please try again later.');
      } finally {
        setLoading(false);
      }
    };
    
    fetchBusinessListing();
  }, [user, navigate]);
  
  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };
  
  // Handle product type checkbox changes
  const handleProductTypeChange = (type: string) => {
    setFormData(prev => {
      const updatedTypes = prev.product_types.includes(type)
        ? prev.product_types.filter(t => t !== type)
        : [...prev.product_types, type];

      return { ...prev, product_types: updatedTypes };
    });
  };

  // Handle address selection from Google Places Autocomplete
  const handleAddressSelect = (address: string, coordinates?: { lat: number; lng: number }, addressComponents?: any) => {
    // If we have address components directly from the autocomplete
    if (addressComponents) {
      console.log('Address components received:', addressComponents);

      // Extract address components, with fallbacks
      const streetAddress = addressComponents.streetAddress || '';
      const city = addressComponents.city || '';
      const state = addressComponents.state || '';
      const zipCode = addressComponents.zipCode || '';
      const country = addressComponents.country || 'US';

      // Use streetAddress if available, otherwise use the full address
      const addressToUse = streetAddress || address;

      console.log('Extracted components:', { addressToUse, city, state, zipCode, country });

      // Update form data with extracted components
      setFormData(prev => ({
        ...prev,
        address: addressToUse,
        city: city || prev.city,
        state: state || prev.state,
        zipCode: zipCode || prev.zipCode,
        country: country || prev.country
      }));
    }
    // If we have neither address components nor coordinates, just update the address field
    // This happens when the user types an address manually
    else {
      console.log('Manual address input:', address);
      setFormData(prev => ({
        ...prev,
        address: address
      }));
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);
    
    try {
      // Determine the endpoint based on user type
      const endpoint = user?.userType === 'supplier'
        ? `${API_URL}/suppliers/${businessListing?.id}`
        : `${API_URL}/vets/${businessListing?.id}`;
      
      await axios.put(endpoint, formData);
      
      setSuccess('Business listing updated successfully!');
      setIsEditing(false);
      
      // Refresh business listing data
      const response = await axios.get(endpoint);
      setBusinessListing(response.data);
    } catch (err: any) {
      console.error('Error updating business listing:', err);
      setError('Failed to update business listing. Please try again later.');
    } finally {
      setLoading(false);
    }
  };
  
  if (loading && !businessListing) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-gray-900">
            {user?.userType === 'supplier' ? 'Supplier Profile' : 'Veterinary Practice Profile'}
          </h1>
          {!isEditing && (
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Edit Profile
            </button>
          )}
        </div>
        
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
            <span className="block sm:inline">{error}</span>
          </div>
        )}
        
        {success && (
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-6" role="alert">
            <span className="block sm:inline">{success}</span>
          </div>
        )}
        
        {isEditing ? (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Business Information</h2>
              </div>
              <div className="p-6 grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Business Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.name}
                    onChange={handleChange}
                  />
                </div>
                
                <div>
                  <label htmlFor="contact_name" className="block text-sm font-medium text-gray-700 mb-1">
                    Contact Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="contact_name"
                    name="contact_name"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.contact_name}
                    onChange={handleChange}
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.email}
                    onChange={handleChange}
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    required
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.phone}
                    onChange={handleChange}
                  />
                </div>
                
                <div className="sm:col-span-2">
                  <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                    Street Address
                  </label>
                  {!isLoadingMapsApi ? (
                    <LocationAutocomplete
                      value={formData.address}
                      onChange={handleAddressSelect}
                      placeholder="Enter street address"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    />
                  ) : (
                    <input
                      type="text"
                      id="address"
                      name="address"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      value={formData.address}
                      onChange={handleChange}
                      placeholder="Loading address autocomplete..."
                    />
                  )}
                </div>

                <div>
                  <label htmlFor="city" className="block text-sm font-medium text-gray-700 mb-1">
                    City
                  </label>
                  <input
                    type="text"
                    id="city"
                    name="city"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.city}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="state" className="block text-sm font-medium text-gray-700 mb-1">
                    State
                  </label>
                  <input
                    type="text"
                    id="state"
                    name="state"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.state}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="zipCode" className="block text-sm font-medium text-gray-700 mb-1">
                    ZIP Code
                  </label>
                  <input
                    type="text"
                    id="zipCode"
                    name="zipCode"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.zipCode}
                    onChange={handleChange}
                  />
                </div>

                <div>
                  <label htmlFor="country" className="block text-sm font-medium text-gray-700 mb-1">
                    Country
                  </label>
                  <input
                    type="text"
                    id="country"
                    name="country"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.country}
                    onChange={handleChange}
                  />
                </div>
                
                <div>
                  <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-1">
                    Website
                  </label>
                  <input
                    type="url"
                    id="website"
                    name="website"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.website}
                    onChange={handleChange}
                    placeholder="https://example.com"
                  />
                </div>
                
                <div>
                  <label htmlFor="payment_terms" className="block text-sm font-medium text-gray-700 mb-1">
                    Payment Terms
                  </label>
                  <input
                    type="text"
                    id="payment_terms"
                    name="payment_terms"
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.payment_terms}
                    onChange={handleChange}
                    placeholder="Net 30, COD, etc."
                  />
                </div>
                
                <div className="sm:col-span-2">
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Business Description
                  </label>
                  <textarea
                    id="description"
                    name="description"
                    rows={4}
                    className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                    value={formData.description}
                    onChange={handleChange}
                    placeholder="Describe your business, services, and specialties..."
                  ></textarea>
                </div>
              </div>
            </div>
            
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">
                  {user?.userType === 'supplier' ? 'Product Types' : 'Service Types'}
                </h2>
              </div>
              <div className="p-6">
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  {productTypeOptions.map(type => (
                    <div key={type} className="flex items-start">
                      <div className="flex items-center h-5">
                        <input
                          id={`product-type-${type}`}
                          type="checkbox"
                          className="focus:ring-primary-500 h-4 w-4 text-primary-600 border-gray-300 rounded"
                          checked={formData.product_types.includes(type)}
                          onChange={() => handleProductTypeChange(type)}
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor={`product-type-${type}`} className="font-medium text-gray-700">
                          {type}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">API Integration</h2>
              </div>
              <div className="p-6">
                <p className="text-sm text-gray-500 mb-4">
                  If you have an API for your inventory or services, you can integrate it with our system.
                  This allows farmers to see real-time availability and pricing information.
                </p>
                
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div>
                    <label htmlFor="api_endpoint" className="block text-sm font-medium text-gray-700 mb-1">
                      API Endpoint URL
                    </label>
                    <input
                      type="url"
                      id="api_endpoint"
                      name="api_endpoint"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      value={formData.api_endpoint}
                      onChange={handleChange}
                      placeholder="https://api.yourbusiness.com/v1"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="api_key" className="block text-sm font-medium text-gray-700 mb-1">
                      API Key
                    </label>
                    <input
                      type="text"
                      id="api_key"
                      name="api_key"
                      className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                      value={formData.api_key}
                      onChange={handleChange}
                      placeholder="Your API key"
                    />
                  </div>
                </div>
                
                <p className="text-xs text-gray-500 mt-2">
                  Note: Your API key is stored securely and is only used for integration purposes.
                </p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
              >
                {loading ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </form>
        ) : businessListing ? (
          <div className="space-y-6">
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h2 className="text-lg font-medium text-gray-900">Business Information</h2>
              </div>
              <div className="p-6">
                <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Business Name</dt>
                    <dd className="mt-1 text-sm text-gray-900">{businessListing.name}</dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Contact Name</dt>
                    <dd className="mt-1 text-sm text-gray-900">{businessListing.contact_name}</dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900">{businessListing.email}</dd>
                  </div>
                  
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Phone</dt>
                    <dd className="mt-1 text-sm text-gray-900">{businessListing.phone}</dd>
                  </div>
                  
                  {businessListing.address && (
                    <div className="sm:col-span-2">
                      <dt className="text-sm font-medium text-gray-500">Address</dt>
                      <dd className="mt-1 text-sm text-gray-900 whitespace-pre-line">{businessListing.address}</dd>
                    </div>
                  )}
                  
                  {businessListing.website && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Website</dt>
                      <dd className="mt-1 text-sm text-gray-900">
                        <a href={businessListing.website} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-900">
                          {businessListing.website}
                        </a>
                      </dd>
                    </div>
                  )}
                  
                  {businessListing.payment_terms && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Payment Terms</dt>
                      <dd className="mt-1 text-sm text-gray-900">{businessListing.payment_terms}</dd>
                    </div>
                  )}
                  
                  {businessListing.description && (
                    <div className="sm:col-span-2">
                      <dt className="text-sm font-medium text-gray-500">Business Description</dt>
                      <dd className="mt-1 text-sm text-gray-900 whitespace-pre-line">{businessListing.description}</dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>
            
            {businessListing.product_types && businessListing.product_types.length > 0 && (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">
                    {user?.userType === 'supplier' ? 'Product Types' : 'Service Types'}
                  </h2>
                </div>
                <div className="p-6">
                  <div className="flex flex-wrap gap-2">
                    {businessListing.product_types.map(type => (
                      <span key={type} className="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-primary-100 text-primary-800">
                        {type}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
            
            {(businessListing.api_endpoint || businessListing.api_key) && (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                  <h2 className="text-lg font-medium text-gray-900">API Integration</h2>
                </div>
                <div className="p-6">
                  <dl className="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
                    {businessListing.api_endpoint && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">API Endpoint</dt>
                        <dd className="mt-1 text-sm text-gray-900">{businessListing.api_endpoint}</dd>
                      </div>
                    )}
                    
                    {businessListing.api_key && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">API Key</dt>
                        <dd className="mt-1 text-sm text-gray-900">••••••••••••••••</dd>
                      </div>
                    )}
                  </dl>
                </div>
              </div>
            )}
          </div>
        ) : null}
      </div>
    </Layout>
  );
};

export default BusinessProfile;