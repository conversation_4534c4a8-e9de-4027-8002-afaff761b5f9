import { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { AuthContext } from '../../context/AuthContext';
import { useFarm } from '../../context/FarmContext';
import Layout from '../../components/Layout';
import { API_URL } from '../../config';

interface CropType {
  id?: string;
  farm_id: string;
  name: string;
  description: string;
  growing_season: string;
  days_to_maturity: number | null;
  planting_depth: number | null;
  row_spacing: number | null;
  plant_spacing: number | null;
  ideal_soil_ph: number | null;
  ideal_temperature: number | null;
}

const CropTypeForm = () => {
  const { cropTypeId } = useParams<{ cropTypeId: string }>();
  const navigate = useNavigate();
  const isEditMode = !!cropTypeId;

  const [cropType, setCropType] = useState<CropType>({
    farm_id: '',
    name: '',
    description: '',
    growing_season: '',
    days_to_maturity: null,
    planting_depth: null,
    row_spacing: null,
    plant_spacing: null,
    ideal_soil_ph: null,
    ideal_temperature: null
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { user } = useContext(AuthContext);
  const { selectedFarm } = useFarm();

  // Console log for debugging
  useEffect(() => {
    console.log("CropTypeForm rendered", { user, selectedFarm, cropType });
  }, [user, selectedFarm, cropType]);

  // Set the selected farm when component mounts or selectedFarm changes
  useEffect(() => {
    if (selectedFarm && !isEditMode) {
      setCropType(prev => ({ ...prev, farm_id: selectedFarm.id }));
    }
  }, [selectedFarm, isEditMode]);

  // Fetch crop type data if in edit mode
  useEffect(() => {
    const fetchCropType = async () => {
      if (!cropTypeId) return;

      setLoading(true);
      setError(null);

      try {
        const response = await axios.get(`${API_URL}/crop-types/${cropTypeId}`);
        setCropType(response.data.cropType || response.data);
      } catch (err: any) {
        console.error('Error fetching crop type:', err);
        setError('Failed to load crop type data. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    if (isEditMode) {
      fetchCropType();
    }
  }, [cropTypeId, isEditMode]);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCropType(prev => ({ ...prev, [name]: value === '' ? null : value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setLoading(true);
    setError(null);

    try {
      // Validate required fields
      if (!selectedFarm) {
        setError('Please select a farm from the header dropdown.');
        setLoading(false);
        return;
      }

      if (!cropType.name) {
        setError('Crop type name is required.');
        setLoading(false);
        return;
      }

      // Ensure farm_id is set from selectedFarm
      const cropTypeData = {
        ...cropType,
        farm_id: selectedFarm.id
      };

      if (isEditMode) {
        // Update existing crop type
        await axios.put(`${API_URL}/crop-types/${cropTypeId}`, cropTypeData);
      } else {
        // Create new crop type
        await axios.post(`${API_URL}/crop-types`, cropTypeData);
      }

      // Redirect to crop types list
      navigate('/crop-types');
    } catch (err: any) {
      console.error('Error saving crop type:', err);
      setError('Failed to save crop type. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {isEditMode ? 'Edit Crop Type' : 'Add New Crop Type'}
        </h1>
        <Link
          to="/crop-types"
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Back to Crop Types
        </Link>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <span className="block sm:inline">{error}</span>
        </div>
      )}

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
            {/* Farm Selection */}
            <div>
              <label htmlFor="farm_id" className="block text-sm font-medium text-gray-700 mb-1">
                Farm <span className="text-red-500">*</span>
              </label>
              <div className="p-2 border rounded bg-gray-50">
                {selectedFarm ? (
                  <span className="text-gray-700">{selectedFarm.name}</span>
                ) : (
                  <span className="text-gray-500 italic">Please select a farm from the header dropdown</span>
                )}
              </div>
              <p className="mt-2 text-sm text-gray-500">
                To change the farm, use the farm selector in the header.
              </p>
              <input
                type="hidden"
                id="farm_id"
                name="farm_id"
                value={selectedFarm?.id || ''}
              />
            </div>

            {/* Crop Type Name */}
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Crop Type Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={cropType.name}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                required
              />
            </div>

            {/* Growing Season */}
            <div>
              <label htmlFor="growing_season" className="block text-sm font-medium text-gray-700 mb-1">
                Growing Season
              </label>
              <select
                id="growing_season"
                name="growing_season"
                value={cropType.growing_season || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
              >
                <option value="">Select a season</option>
                <option value="spring">Spring</option>
                <option value="summer">Summer</option>
                <option value="fall">Fall</option>
                <option value="winter">Winter</option>
                <option value="year-round">Year-round</option>
              </select>
            </div>

            {/* Days to Maturity */}
            <div>
              <label htmlFor="days_to_maturity" className="block text-sm font-medium text-gray-700 mb-1">
                Days to Maturity
              </label>
              <input
                type="number"
                id="days_to_maturity"
                name="days_to_maturity"
                value={cropType.days_to_maturity || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                min="1"
                max="365"
              />
            </div>

            {/* Planting Depth */}
            <div>
              <label htmlFor="planting_depth" className="block text-sm font-medium text-gray-700 mb-1">
                Planting Depth (inches)
              </label>
              <input
                type="number"
                id="planting_depth"
                name="planting_depth"
                value={cropType.planting_depth || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                step="0.1"
                min="0"
              />
            </div>

            {/* Row Spacing */}
            <div>
              <label htmlFor="row_spacing" className="block text-sm font-medium text-gray-700 mb-1">
                Row Spacing (inches)
              </label>
              <input
                type="number"
                id="row_spacing"
                name="row_spacing"
                value={cropType.row_spacing || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                step="0.1"
                min="0"
              />
            </div>

            {/* Plant Spacing */}
            <div>
              <label htmlFor="plant_spacing" className="block text-sm font-medium text-gray-700 mb-1">
                Plant Spacing (inches)
              </label>
              <input
                type="number"
                id="plant_spacing"
                name="plant_spacing"
                value={cropType.plant_spacing || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                step="0.1"
                min="0"
              />
            </div>

            {/* Ideal Soil pH */}
            <div>
              <label htmlFor="ideal_soil_ph" className="block text-sm font-medium text-gray-700 mb-1">
                Ideal Soil pH
              </label>
              <input
                type="number"
                id="ideal_soil_ph"
                name="ideal_soil_ph"
                value={cropType.ideal_soil_ph || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                step="0.1"
                min="0"
                max="14"
              />
            </div>

            {/* Ideal Temperature */}
            <div>
              <label htmlFor="ideal_temperature" className="block text-sm font-medium text-gray-700 mb-1">
                Ideal Temperature (°F)
              </label>
              <input
                type="number"
                id="ideal_temperature"
                name="ideal_temperature"
                value={cropType.ideal_temperature || ''}
                onChange={handleChange}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                step="0.1"
                min="-20"
                max="120"
              />
            </div>
          </div>

          {/* Description */}
          <div className="mt-6">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={cropType.description || ''}
              onChange={handleChange}
              rows={4}
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            ></textarea>
          </div>

          {/* Form Actions */}
          <div className="mt-6 flex justify-end space-x-3">
            <Link
              to="/crop-types"
              className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className={`inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 ${loading ? 'opacity-70 cursor-not-allowed' : ''}`}
            >
              {loading ? 'Saving...' : 'Save Crop Type'}
            </button>
          </div>
        </form>
      </div>
    </Layout>
  );
};

export default CropTypeForm;