import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { API_URL } from '../../config';

interface ServerStats {
  cpu_average: number;
  database_engine: string;
  database_server_version: string;
  event_cache_size: number;
  homeserver: string;
  memory_usage: number;
  python_version: string;
  server_context: string;
  uptime_seconds: number;
  total_users: number;
  total_nonbridged_users: number;
  total_room_count: number;
  daily_active_users: number;
  daily_active_rooms: number;
  daily_messages: number;
  daily_sent_messages: number;
  daily_active_e2ee_rooms: number;
  daily_e2ee_messages: number;
  monthly_active_users: number;
  r30_users_all: number;
  r30_users_android: number;
  r30_users_ios: number;
  r30_users_electron: number;
  r30_users_web: number;
}

interface ServerVersion {
  server_version: string;
  python_version: string;
}

const MatrixServerStats: React.FC = () => {
  const [stats, setStats] = useState<ServerStats | null>(null);
  const [version, setVersion] = useState<ServerVersion | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchServerInfo();
  }, []);

  const fetchServerInfo = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch server stats
      const statsResponse = await axios.get(`${API_URL}/admin/matrix/stats`);
      if (statsResponse.data) {
        setStats(statsResponse.data);
      }

      // Fetch server version
      const versionResponse = await axios.get(`${API_URL}/admin/matrix/version`);
      if (versionResponse.data) {
        setVersion(versionResponse.data);
      }

      setLoading(false);
    } catch (err: any) {
      console.error('Error fetching Matrix server info:', err);
      // Display detailed error message if available
      const errorMessage = err.response?.data?.error || 'Failed to load Matrix server information';
      const errorDetails = err.response?.data?.details;
      setError(errorDetails ? `${errorMessage}: ${errorDetails}` : errorMessage);
      setLoading(false);
    }
  };

  // Format uptime in a human-readable format
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = Math.floor(seconds % 60);

    return `${days}d ${hours}h ${minutes}m ${remainingSeconds}s`;
  };

  // Format memory usage in a human-readable format
  const formatMemory = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} KB`;
    if (bytes < 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
    return `${(bytes / (1024 * 1024 * 1024)).toFixed(2)} GB`;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Error!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div>
      <h2 className="text-xl font-semibold mb-6">Matrix Server Statistics</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Server Information */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Server Information</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Server Version:</span>
              <span className="font-medium">{version?.server_version || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Python Version:</span>
              <span className="font-medium">{version?.python_version || stats?.python_version || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Homeserver:</span>
              <span className="font-medium">{stats?.homeserver || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Database Engine:</span>
              <span className="font-medium">{stats?.database_engine || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Database Version:</span>
              <span className="font-medium">{stats?.database_server_version || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Uptime:</span>
              <span className="font-medium">{stats?.uptime_seconds ? formatUptime(stats.uptime_seconds) : 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">CPU Usage:</span>
              <span className="font-medium">{stats?.cpu_average ? `${stats.cpu_average.toFixed(2)}%` : 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Memory Usage:</span>
              <span className="font-medium">{stats?.memory_usage ? formatMemory(stats.memory_usage) : 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Event Cache Size:</span>
              <span className="font-medium">{stats?.event_cache_size || 'Unknown'}</span>
            </div>
          </div>
        </div>

        {/* User Statistics */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">User Statistics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Users:</span>
              <span className="font-medium">{stats?.total_users || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Non-bridged Users:</span>
              <span className="font-medium">{stats?.total_nonbridged_users || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Daily Active Users:</span>
              <span className="font-medium">{stats?.daily_active_users || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Monthly Active Users:</span>
              <span className="font-medium">{stats?.monthly_active_users || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">R30 Users (All):</span>
              <span className="font-medium">{stats?.r30_users_all || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">R30 Users (Android):</span>
              <span className="font-medium">{stats?.r30_users_android || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">R30 Users (iOS):</span>
              <span className="font-medium">{stats?.r30_users_ios || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">R30 Users (Desktop):</span>
              <span className="font-medium">{stats?.r30_users_electron || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">R30 Users (Web):</span>
              <span className="font-medium">{stats?.r30_users_web || 'Unknown'}</span>
            </div>
          </div>
        </div>

        {/* Room Statistics */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Room Statistics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Total Rooms:</span>
              <span className="font-medium">{stats?.total_room_count || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Daily Active Rooms:</span>
              <span className="font-medium">{stats?.daily_active_rooms || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Daily Active E2EE Rooms:</span>
              <span className="font-medium">{stats?.daily_active_e2ee_rooms || 'Unknown'}</span>
            </div>
          </div>
        </div>

        {/* Message Statistics */}
        <div className="bg-white shadow-md rounded-lg p-6">
          <h3 className="text-lg font-semibold mb-4">Message Statistics</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">Daily Messages:</span>
              <span className="font-medium">{stats?.daily_messages || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Daily Sent Messages:</span>
              <span className="font-medium">{stats?.daily_sent_messages || 'Unknown'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Daily E2EE Messages:</span>
              <span className="font-medium">{stats?.daily_e2ee_messages || 'Unknown'}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 flex justify-end">
        <button
          onClick={fetchServerInfo}
          className="bg-primary-600 hover:bg-primary-700 text-white font-bold py-2 px-4 rounded"
        >
          Refresh Statistics
        </button>
      </div>
    </div>
  );
};

export default MatrixServerStats;
