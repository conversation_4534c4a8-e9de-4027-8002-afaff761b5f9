import { useState, useEffect } from 'react';
import { BrowserRouter, Routes, Route, Link, Navigate, useNavigate, useLocation } from 'react-router-dom';
import './App.css';
import LoginPage from './pages/LoginPage';
import FarmDetailsPage from './pages/FarmDetailsPage';
import FieldManagementPage from './pages/FieldManagementPage';
import FieldMapPage from './pages/FieldMapPage';
import TaskListPage from './pages/TaskListPage';
import TaskDetailPage from './pages/TaskDetailPage';
import PasswordManagerPage from './pages/PasswordManagerPage';
import PasswordGroupDetailPage from './pages/PasswordGroupDetailPage';
import authService from './services/authService';
import syncService from './services/syncService';
import { User } from './types/user';

// Define the type for the window.electronAPI object
declare global {
  interface Window {
    electronAPI: {
      getAppVersion: () => Promise<string>;
      getFarms: () => Promise<Array<{ id: number; name: string; location: string }>>;
      showNotification: (title: string, body: string) => void;
      minimizeToTray: () => Promise<void>;
      openFile: () => Promise<string | null>;
      saveFile: (content: string) => Promise<boolean>;
      syncData: () => Promise<boolean>;
      getOfflineStatus: () => Promise<boolean>;
      minimizeWindow: () => Promise<void>;
      maximizeWindow: () => Promise<void>;
      closeWindow: () => Promise<void>;
    };
  }
}

// AppContent component to handle the main application content with routing
const AppContent: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [appVersion, setAppVersion] = useState<string>('');
  const [farms, setFarms] = useState<Array<{ id: number; name: string; location: string }>>([]);
  const [isOffline, setIsOffline] = useState<boolean>(false);
  const [selectedFarm, setSelectedFarm] = useState<number | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [syncStatus, setSyncStatus] = useState<{ isSyncing: boolean; lastSyncTime: Date | null; pendingOperations: number }>({
    isSyncing: false,
    lastSyncTime: null,
    pendingOperations: 0
  });

  useEffect(() => {
    // Get app version when component mounts
    window.electronAPI.getAppVersion().then(version => {
      setAppVersion(version);
    }).catch(err => {
      console.error('Failed to get app version:', err);
    });

    // Check if user is already logged in
    const checkAuthStatus = () => {
      const isLoggedIn = authService.isLoggedIn();
      setIsAuthenticated(isLoggedIn);
      if (isLoggedIn) {
        setCurrentUser(authService.getCurrentUser());

        // Get farms when authenticated
        window.electronAPI.getFarms().then(farmData => {
          setFarms(farmData);
          if (farmData.length > 0) {
            setSelectedFarm(farmData[0].id);
          }
        }).catch(err => {
          console.error('Failed to get farms:', err);
        });
      }
    };

    checkAuthStatus();

    // Check offline status
    window.electronAPI.getOfflineStatus().then(status => {
      setIsOffline(status);
    }).catch(err => {
      console.error('Failed to get offline status:', err);
    });

    // Get sync status
    const updateSyncStatus = () => {
      const status = syncService.getSyncStatus();
      setSyncStatus({
        isSyncing: status.isSyncing,
        lastSyncTime: status.lastSyncTime,
        pendingOperations: status.pendingOperations
      });
    };

    updateSyncStatus();

    // Set up interval to update sync status
    const syncStatusInterval = setInterval(updateSyncStatus, 10000);

    return () => {
      clearInterval(syncStatusInterval);
    };
  }, []);

  const handleSyncData = () => {
    syncService.syncData().then(success => {
      if (success) {
        window.electronAPI.showNotification('Sync Complete', 'Your data has been synchronized successfully.');
      } else {
        window.electronAPI.showNotification('Sync Failed', 'There was an error synchronizing your data.');
      }
    }).catch(err => {
      console.error('Failed to sync data:', err);
      window.electronAPI.showNotification('Sync Error', 'An unexpected error occurred during synchronization.');
    });
  };

  const handleMinimizeToTray = () => {
    window.electronAPI.minimizeToTray().catch(err => {
      console.error('Failed to minimize to tray:', err);
    });
  };

  const handleFarmSelect = (farmId: number) => {
    setSelectedFarm(farmId);
    navigate(`/farms/${farmId}`);
  };

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
    setCurrentUser(authService.getCurrentUser());

    // Get farms after login
    window.electronAPI.getFarms().then(farmData => {
      setFarms(farmData);
      if (farmData.length > 0) {
        setSelectedFarm(farmData[0].id);
      }
    }).catch(err => {
      console.error('Failed to get farms:', err);
    });
  };

  const handleLogout = async () => {
    try {
      await authService.logout();
      setIsAuthenticated(false);
      setCurrentUser(null);
      setFarms([]);
      setSelectedFarm(null);
    } catch (err) {
      console.error('Failed to logout:', err);
      window.electronAPI.showNotification('Logout Error', 'An error occurred during logout.');
    }
  };

  // Render the authenticated app content
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <header className="bg-green-700 text-white p-4 flex justify-between items-center">
        <div className="flex items-center">
          <h1 className="text-xl font-bold">NxtAcre Farm Management</h1>
          <span className="ml-4 text-sm bg-green-600 px-2 py-1 rounded">v{appVersion}</span>
        </div>
        <div className="flex items-center">
          {currentUser && (
            <div className="mr-4 flex items-center">
              <span className="text-sm mr-2">
                {currentUser.firstName} {currentUser.lastName}
              </span>
              <button
                onClick={handleLogout}
                className="text-xs bg-red-600 hover:bg-red-500 text-white px-2 py-1 rounded"
              >
                Logout
              </button>
            </div>
          )}
          {isOffline && (
            <span className="mr-4 text-sm bg-yellow-500 text-black px-2 py-1 rounded flex items-center">
              Offline Mode
            </span>
          )}
          <button
            onClick={handleSyncData}
            className={`bg-green-600 hover:bg-green-500 text-white px-3 py-1 rounded mr-2 flex items-center ${
              syncStatus.isSyncing ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            disabled={syncStatus.isSyncing}
          >
            {syncStatus.isSyncing ? 'Syncing...' : `Sync Data ${syncStatus.pendingOperations > 0 ? `(${syncStatus.pendingOperations})` : ''}`}
          </button>
          <button
            onClick={handleMinimizeToTray}
            className="bg-green-600 hover:bg-green-500 text-white px-3 py-1 rounded"
          >
            Minimize to Tray
          </button>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        <aside className="w-64 bg-gray-800 text-white p-4 overflow-y-auto">
          <h2 className="text-lg font-semibold mb-4">Your Farms</h2>
          <ul className="space-y-2">
            {farms.map(farm => (
              <li key={farm.id}>
                <button
                  onClick={() => handleFarmSelect(farm.id)}
                  className={`w-full text-left px-3 py-2 rounded ${
                    selectedFarm === farm.id ? 'bg-green-600' : 'hover:bg-gray-700'
                  }`}
                >
                  {farm.name}
                  <div className="text-xs text-gray-400">{farm.location}</div>
                </button>
              </li>
            ))}
          </ul>

          <h2 className="text-lg font-semibold mt-8 mb-4">Features</h2>
          <ul className="space-y-2">
            <li>
              <Link 
                to="/"
                className={`block w-full text-left px-3 py-2 rounded hover:bg-gray-700 ${location.pathname === '/' ? 'bg-green-600' : ''}`}
              >
                Dashboard
              </Link>
            </li>
            <li>
              <Link 
                to="/tasks" 
                className={`block w-full text-left px-3 py-2 rounded hover:bg-gray-700 ${location.pathname.includes('/tasks') ? 'bg-green-600' : ''}`}
              >
                Tasks
              </Link>
            </li>
            <li>
              <Link 
                to={selectedFarm ? `/farms/${selectedFarm}/fields` : '#'} 
                className={`block w-full text-left px-3 py-2 rounded hover:bg-gray-700 ${location.pathname.includes('/fields') ? 'bg-green-600' : ''}`}
                onClick={(e) => !selectedFarm && e.preventDefault()}
              >
                Fields
                {!selectedFarm && <span className="ml-2 text-xs text-gray-400">(Select a farm first)</span>}
              </Link>
            </li>
            <li>
              <button className="w-full text-left px-3 py-2 rounded hover:bg-gray-700">
                Equipment
              </button>
            </li>
            <li>
              <button className="w-full text-left px-3 py-2 rounded hover:bg-gray-700">
                Inventory
              </button>
            </li>
            <li>
              <button className="w-full text-left px-3 py-2 rounded hover:bg-gray-700">
                Finances
              </button>
            </li>
            <li>
              <button className="w-full text-left px-3 py-2 rounded hover:bg-gray-700">
                Weather
              </button>
            </li>
            <li>
              <button className="w-full text-left px-3 py-2 rounded hover:bg-gray-700">
                Chat
              </button>
            </li>
            <li>
              <Link 
                to="/password-manager" 
                className={`block w-full text-left px-3 py-2 rounded hover:bg-gray-700 ${location.pathname.includes('/password-manager') ? 'bg-green-600' : ''}`}
              >
                Password Manager
              </Link>
            </li>
          </ul>
        </aside>

        {/* Content Area */}
        <div className="flex-1 bg-gray-100 dark:bg-gray-900 overflow-y-auto">
          <Routes>
            <Route path="/" element={
              <div className="p-6">
                <h2 className="text-2xl font-bold mb-6">Welcome to NxtAcre Desktop</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Dashboard Cards */}
                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
                    <div className="space-y-4">
                      <button 
                        onClick={() => navigate('/tasks/new')} 
                        className="w-full bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded"
                      >
                        Create New Task
                      </button>
                      <button className="w-full bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded">
                        Record Inventory
                      </button>
                      <button className="w-full bg-purple-600 hover:bg-purple-500 text-white px-4 py-2 rounded">
                        Log Equipment Usage
                      </button>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold mb-4">Recent Tasks</h3>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                        <div>
                          <div className="font-medium">Inspect North Field Irrigation</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">Due: {new Date(new Date().getTime() + 86400000).toLocaleDateString()}</div>
                        </div>
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">TODO</span>
                      </div>
                      <div className="flex justify-between items-center p-2 bg-gray-50 dark:bg-gray-700 rounded">
                        <div>
                          <div className="font-medium">Order Fertilizer</div>
                          <div className="text-sm text-gray-500 dark:text-gray-400">Due: {new Date(new Date().getTime() + 172800000).toLocaleDateString()}</div>
                        </div>
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">IN PROGRESS</span>
                      </div>
                      <div className="text-right mt-2">
                        <Link to="/tasks" className="text-green-600 hover:text-green-500 text-sm font-medium">
                          View All Tasks →
                        </Link>
                      </div>
                    </div>
                  </div>

                  <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 className="text-lg font-semibold mb-4">Weather</h3>
                    <p className="text-gray-500 dark:text-gray-400">Weather data not available in offline mode.</p>
                  </div>
                </div>
              </div>
            } />

            {/* Farm Management Routes */}
            <Route path="/farms/:farmId" element={<FarmDetailsPage />} />
            <Route path="/farms/:farmId/fields" element={<FieldManagementPage />} />
            <Route path="/farms/:farmId/fields/:fieldId/map" element={<FieldMapPage />} />

            {/* Task Management Routes */}
            <Route path="/tasks" element={<TaskListPage />} />
            <Route path="/tasks/:taskId" element={<TaskDetailPage />} />
            <Route path="/farms/:farmId/tasks" element={<TaskListPage />} />

            {/* Password Manager Routes */}
            <Route path="/password-manager" element={<PasswordManagerPage />} />
            <Route path="/password-manager/group/:groupId" element={<PasswordGroupDetailPage />} />

            {/* Redirect to home if no route matches */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-gray-800 text-white p-2 text-center text-sm">
        <p>NxtAcre Farm Management Platform &copy; {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
};

// Main App component
function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);

  useEffect(() => {
    // Check if user is already logged in
    const isLoggedIn = authService.isLoggedIn();
    setIsAuthenticated(isLoggedIn);
  }, []);

  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
  };

  // If not authenticated, show login page
  if (!isAuthenticated) {
    return (
      <BrowserRouter>
        <LoginPage onLoginSuccess={handleLoginSuccess} />
      </BrowserRouter>
    );
  }

  // If authenticated, show the main app with routing
  return (
    <BrowserRouter>
      <AppContent />
    </BrowserRouter>
  );
}

export default App;
