/*
 * NxtAcre Weather Station - Sensor Handler
 * 
 * This file contains functions for handling sensors and reading data.
 */

#ifndef SENSOR_HANDLER_H
#define SENSOR_HANDLER_H

#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_BME280.h>
#include "config.h"

// Define the weather data structure
struct WeatherData {
  float temperature;     // Temperature in Celsius
  float humidity;        // Relative humidity in percentage
  float pressure;        // Atmospheric pressure in hPa
  float rainfall;        // Rainfall in mm
  float windSpeed;       // Wind speed in m/s
  float windDirection;   // Wind direction in degrees
  int batteryLevel;      // Battery level in percentage
  float solarVoltage;    // Solar panel voltage in V
};

// Global variables
Adafruit_BME280 bme;     // BME280 sensor
volatile unsigned long rainGaugeCount = 0;
volatile unsigned long lastRainGaugeTime = 0;
volatile unsigned long anemometerCount = 0;
volatile unsigned long lastAnemometerTime = 0;
unsigned long lastWindMeasurement = 0;

// Interrupt handlers
void IRAM_ATTR rainGaugeInterrupt() {
  // Debounce
  if (millis() - lastRainGaugeTime > 100) {
    rainGaugeCount++;
    lastRainGaugeTime = millis();
  }
}

void IRAM_ATTR anemometerInterrupt() {
  // Debounce
  if (millis() - lastAnemometerTime > 10) {
    anemometerCount++;
    lastAnemometerTime = millis();
  }
}

// Initialize sensors
bool initializeSensors() {
  bool success = true;
  
  // Initialize I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  
  // Initialize BME280
  if (!bme.begin(0x76)) {
    Serial.println("Could not find a valid BME280 sensor, check wiring!");
    success = false;
  } else {
    Serial.println("BME280 sensor initialized");
  }
  
  // Initialize rain gauge
  pinMode(RAIN_GAUGE_PIN, INPUT_PULLUP);
  attachInterrupt(digitalPinToInterrupt(RAIN_GAUGE_PIN), rainGaugeInterrupt, FALLING);
  Serial.println("Rain gauge initialized");
  
  // Initialize anemometer
  pinMode(ANEMOMETER_PIN, INPUT_PULLUP);
  attachInterrupt(digitalPinToInterrupt(ANEMOMETER_PIN), anemometerInterrupt, FALLING);
  Serial.println("Anemometer initialized");
  
  // Initialize wind vane (analog input)
  pinMode(WIND_VANE_PIN, INPUT);
  Serial.println("Wind vane initialized");
  
  // Initialize battery monitoring
  pinMode(BATTERY_LEVEL_PIN, INPUT);
  Serial.println("Battery monitoring initialized");
  
  // Initialize solar panel monitoring
  pinMode(SOLAR_VOLTAGE_PIN, INPUT);
  Serial.println("Solar panel monitoring initialized");
  
  return success;
}

// Read data from all sensors
void readSensors(WeatherData* data) {
  // Read BME280 sensor
  data->temperature = bme.readTemperature();
  data->humidity = bme.readHumidity();
  data->pressure = bme.readPressure() / 100.0F; // Convert Pa to hPa
  
  // Read rainfall
  static unsigned long lastRainCount = 0;
  unsigned long currentRainCount = rainGaugeCount;
  unsigned long rainTips = currentRainCount - lastRainCount;
  data->rainfall = rainTips * RAIN_GAUGE_FACTOR;
  lastRainCount = currentRainCount;
  
  // Read wind speed
  unsigned long currentMillis = millis();
  static unsigned long lastAnemometerCount = 0;
  unsigned long currentAnemometerCount = anemometerCount;
  
  if (currentMillis - lastWindMeasurement >= 5000) { // Measure over 5 seconds
    unsigned long rotations = currentAnemometerCount - lastAnemometerCount;
    float timeSeconds = (currentMillis - lastWindMeasurement) / 1000.0;
    float frequency = rotations / timeSeconds;
    data->windSpeed = frequency * ANEMOMETER_FACTOR;
    
    lastAnemometerCount = currentAnemometerCount;
    lastWindMeasurement = currentMillis;
  }
  
  // Read wind direction
  int windVaneValue = analogRead(WIND_VANE_PIN);
  
  // Map analog reading to degrees (this is a simplified example)
  // In a real implementation, you would use a lookup table or calibration curve
  data->windDirection = map(windVaneValue, 0, 4095, 0, 359);
  
  // Read battery level
  int batteryRaw = analogRead(BATTERY_LEVEL_PIN);
  float batteryVoltage = batteryRaw * (3.3 / 4095.0) * 2; // Assuming voltage divider
  int batteryPercentage = map(batteryVoltage * 100, 
                             BATTERY_MIN_VOLTAGE * 100, 
                             BATTERY_MAX_VOLTAGE * 100, 
                             0, 100);
  data->batteryLevel = constrain(batteryPercentage, 0, 100);
  
  // Read solar panel voltage
  int solarRaw = analogRead(SOLAR_VOLTAGE_PIN);
  data->solarVoltage = solarRaw * (3.3 / 4095.0) * 2; // Assuming voltage divider
}

#endif // SENSOR_HANDLER_H