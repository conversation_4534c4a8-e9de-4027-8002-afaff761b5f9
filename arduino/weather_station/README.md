# NxtAcre Weather Station

This module implements a weather station using an ESP32 board with WiFi capabilities for the NxtAcre Farm Management Platform. It collects data from various sensors and sends the data to the NxtAcre server via WiFi.

## Features

- **Environmental Monitoring**: Measures temperature, humidity, and atmospheric pressure
- **Rainfall Measurement**: Tracks precipitation amounts
- **Wind Monitoring**: Measures wind speed and direction
- **Power Management**: Monitors battery level and solar panel voltage
- **WiFi Connectivity**: Sends data to the NxtAcre server
- **Automatic Reconnection**: Handles WiFi connection issues

## Hardware Requirements

- ESP32 development board
- BME280 sensor (temperature, humidity, pressure)
- Rain gauge sensor (tipping bucket style)
- Anemometer (wind speed sensor)
- Wind vane (wind direction sensor)
- Solar panel and battery for power
- Voltage dividers for battery and solar panel monitoring

## Wiring

Connect the sensors to the ESP32 as follows:

| Sensor | ESP32 Pin |
|--------|-----------|
| BME280 SDA | GPIO21 |
| BME280 SCL | GPIO22 |
| Rain Gauge | GPIO4 |
| Anemometer | GPIO5 |
| Wind Vane | GPIO34 (ADC) |
| Battery Level | GPIO35 (ADC) |
| Solar Voltage | GPIO36 (ADC) |

## Installation

1. Install the Arduino IDE (version 1.8.19 or later)
2. Add ESP32 board support to Arduino IDE:
   - Open Arduino IDE
   - Go to File > Preferences
   - Add `https://raw.githubusercontent.com/espressif/arduino-esp32/gh-pages/package_esp32_index.json` to the "Additional Board Manager URLs" field
   - Go to Tools > Board > Boards Manager
   - Search for "esp32" and install the latest version
3. Install required libraries:
   - Go to Sketch > Include Library > Manage Libraries
   - Install the following libraries:
     - Adafruit BME280 Library by Adafruit
     - Adafruit Unified Sensor by Adafruit
     - ArduinoJson by Benoit Blanchon
4. Clone or download this repository
5. Open the `weather_station.ino` file in Arduino IDE
6. Select the appropriate board (ESP32 Dev Module) from Tools > Board
7. Connect your ESP32 board to your computer
8. Click the Upload button to compile and upload the code

## Configuration

You can customize the behavior of the weather station by modifying the `config.h` file:

- **WiFi Configuration**: Set your WiFi SSID and password
- **Server Configuration**: Configure the server URL and API key
- **Pin Configuration**: Adjust pin assignments if your hardware setup differs
- **Sensor Calibration**: Modify calibration factors for sensors
- **Timing Parameters**: Adjust data collection and transmission intervals
- **Debug Settings**: Enable or disable debug output

## Usage

The weather station will automatically:

1. Connect to the configured WiFi network
2. Initialize all sensors
3. Collect data from sensors at regular intervals
4. Send the collected data to the NxtAcre server

The data is sent to the server in JSON format with the following structure:

```json
{
  "apiKey": "your-api-key",
  "deviceIdentifier": "weather_station_1",
  "iotData": {
    "timestamp": 1234567890,
    "temperature": 25.4,
    "humidity": 65.2,
    "pressure": 1013.25,
    "batteryLevel": 85,
    "customFields": {
      "rainfall": 2.5,
      "windSpeed": 3.7,
      "windDirection": 180,
      "solarVoltage": 5.2
    }
  }
}
```

## Integration with NxtAcre Platform

The weather station integrates with the NxtAcre platform through the IoT API. The data is sent to the `/api/iot/data/external` endpoint, which processes and stores the data in the platform's database.

To view the weather station data in the NxtAcre platform:

1. Register the weather station as an IoT device in the platform
2. Use the same device identifier in the weather station configuration
3. Access the IoT device data through the platform's interface

## Troubleshooting

- **No WiFi Connection**: Check WiFi credentials in `config.h`
- **Sensor Errors**: Verify wiring and connections
- **No Data on Server**: Check server URL and API key
- **Inaccurate Readings**: Calibrate sensors in `config.h`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions to improve the weather station module are welcome. Please feel free to submit a pull request.