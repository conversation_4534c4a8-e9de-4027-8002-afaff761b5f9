#!/bin/bash

# Script to validate mobile platform configuration

echo "🔍 Validating Mobile Platform Configuration..."
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

ERRORS=0
WARNINGS=0

# Function to check if file exists
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✓${NC} $1 exists"
    else
        echo -e "${RED}✗${NC} $1 missing"
        ((ERRORS++))
    fi
}

# Function to check if directory exists
check_dir() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✓${NC} $1 exists"
    else
        echo -e "${RED}✗${NC} $1 missing"
        ((ERRORS++))
    fi
}

# Function to check optional file
check_optional_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✓${NC} $1 exists"
    else
        echo -e "${YELLOW}⚠${NC} $1 missing (optional)"
        ((WARNINGS++))
    fi
}

echo ""
echo "📁 Checking Shared Module Configuration..."
echo "----------------------------------------"

# Check shared module structure
check_dir "shared"
check_file "shared/package.json"
check_file "shared/index.js"
check_file "shared/index.d.ts"
check_file "shared/tsconfig.json"
check_file "shared/babel.config.js"

# Check shared module assets
echo ""
echo "🖼️  Checking Shared Assets..."
echo "----------------------------"
check_dir "shared/assets"
check_file "shared/assets/icon.png"
check_file "shared/assets/logo.png"
check_optional_file "shared/assets/splash.png"
check_optional_file "shared/assets/adaptive-icon.png"
check_optional_file "shared/assets/favicon.png"

# Check shared module components
echo ""
echo "🧩 Checking Shared Components..."
echo "-------------------------------"
check_dir "shared/components"
check_file "shared/components/SplashScreen.tsx"
check_file "shared/components/HelpTips.tsx"
check_file "shared/components/UnifiedNotificationCenter.tsx"
check_file "shared/components/NotificationButton.tsx"

# Check each app
echo ""
echo "📱 Checking Individual Apps..."
echo "-----------------------------"

for app_dir in apps/*/; do
    if [ -d "$app_dir" ]; then
        app_name=$(basename "$app_dir")
        echo ""
        echo "Checking app: $app_name"
        echo "$(printf '%.0s-' {1..20})"
        
        # Check essential files
        check_file "$app_dir/package.json"
        check_file "$app_dir/app.json"
        check_file "$app_dir/tsconfig.json"
        check_file "$app_dir/metro.config.js"
        check_file "$app_dir/App.tsx"
        
        # Check if shared module is installed
        if [ -d "$app_dir/node_modules/nxtacre-shared" ]; then
            echo -e "${GREEN}✓${NC} nxtacre-shared installed in $app_name"
        else
            echo -e "${YELLOW}⚠${NC} nxtacre-shared not installed in $app_name"
            ((WARNINGS++))
        fi
        
        # Check TypeScript configuration
        if [ -f "$app_dir/tsconfig.json" ]; then
            if grep -q "nxtacre-shared" "$app_dir/tsconfig.json"; then
                echo -e "${GREEN}✓${NC} TypeScript paths configured for shared module"
            else
                echo -e "${YELLOW}⚠${NC} TypeScript paths may not be configured for shared module"
                ((WARNINGS++))
            fi
        fi
        
        # Check Metro configuration
        if [ -f "$app_dir/metro.config.js" ]; then
            if grep -q "extraNodeModules" "$app_dir/metro.config.js"; then
                echo -e "${GREEN}✓${NC} Metro extraNodeModules configured"
            else
                echo -e "${YELLOW}⚠${NC} Metro extraNodeModules not configured"
                ((WARNINGS++))
            fi
        fi
    fi
done

echo ""
echo "📊 Configuration Summary"
echo "======================="
echo -e "Errors: ${RED}$ERRORS${NC}"
echo -e "Warnings: ${YELLOW}$WARNINGS${NC}"

if [ $ERRORS -eq 0 ]; then
    echo -e "${GREEN}✅ Configuration validation completed successfully!${NC}"
    exit 0
else
    echo -e "${RED}❌ Configuration validation failed with $ERRORS errors.${NC}"
    exit 1
fi
