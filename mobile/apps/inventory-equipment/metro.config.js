const { getDefaultConfig } = require("expo/metro-config");
const path = require("path");

const config = getDefaultConfig(__dirname);

// Add the shared module to the watchFolders
const sharedPath = path.resolve(__dirname, "../../shared");
config.watchFolders = [sharedPath];

// Configure the resolver to handle the shared module
config.resolver.nodeModulesPaths = [
  path.resolve(__dirname, "node_modules"),
  path.resolve(__dirname, "../../shared/node_modules"),
];

// Add platforms if needed
config.resolver.platforms = ["native", "android", "ios", "web"];

// Add extraNodeModules to resolve the missing DeprecatedViewPropTypes
config.resolver.extraNodeModules = {
  ...config.resolver.extraNodeModules,
  './DeprecatedViewPropTypes': path.resolve(__dirname, '../../shared/DeprecatedViewPropTypes'),
  'deprecated-react-native-prop-types/DeprecatedViewPropTypes': path.resolve(__dirname, '../../shared/DeprecatedViewPropTypes'),
};

// Configure transformer to handle shared module
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

module.exports = config;
