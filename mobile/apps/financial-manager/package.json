{"name": "nxtacre-financial-manager", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.0.5", "@react-navigation/native": "^7.0.15", "@react-navigation/native-stack": "^7.1.8", "expo": "~53.0.12", "expo-asset": "~11.1.5", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linking": "~7.1.5", "expo-splash-screen": "~0.30.9", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.9", "expo-web-browser": "~14.2.0", "nxtacre-shared": "file:../../shared", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "zustand": "^5.0.5", "expo-location": "~18.1.5", "expo-notifications": "~0.31.3", "react-native-maps": "1.20.1"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/react": "~19.0.10", "jest": "^29.7.0", "jest-expo": "~53.0.0", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true, "description": "Financial Manager App for NxtAcre Farm Management Platform", "author": "NxtAcre Team", "license": "UNLICENSED"}