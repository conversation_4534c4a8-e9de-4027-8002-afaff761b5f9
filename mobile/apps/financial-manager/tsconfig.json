{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": false, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": ".", "paths": {"nxtacre-shared": ["../../shared"], "nxtacre-shared/*": ["../../shared/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "../../shared/**/*.ts", "../../shared/**/*.tsx"], "exclude": ["node_modules", "**/*.test.ts", "**/*.test.tsx"], "extends": "expo/tsconfig.base"}