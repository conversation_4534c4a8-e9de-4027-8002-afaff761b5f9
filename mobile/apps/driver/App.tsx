import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text } from 'react-native';

// Minimal test app to debug the prototype error
export default function App() {
  return (
    <SafeAreaProvider>
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <Text>Driver App - Minimal Test</Text>
        <StatusBar style="auto" />
      </View>
    </SafeAreaProvider>
  );
}
