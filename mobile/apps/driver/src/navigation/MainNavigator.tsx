import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import theme from '../../../../shared/utils/theme';

// Import screens (to be created)
import HomeScreen from '../screens/HomeScreen';
import DeliveriesScreen from '../screens/DeliveriesScreen';
import NavigationScreen from '../screens/NavigationScreen';
import ScheduleScreen from '../screens/ScheduleScreen';
import ProfileScreen from '../screens/ProfileScreen';
import DeliveryDetailScreen from '../screens/DeliveryDetailScreen';
import SignatureScreen from '../screens/SignatureScreen';
import PhotoCaptureScreen from '../screens/PhotoCaptureScreen';
import VehicleSelectionScreen from '../screens/VehicleSelectionScreen';
import MessagesScreen from '../screens/MessagesScreen';
import ConversationScreen from '../screens/ConversationScreen';
import SettingsScreen from '../screens/SettingsScreen';
import HelpScreen from '../screens/HelpScreen';
import LocationTrackingScreen from '../screens/LocationTrackingScreen';
import EnhancedNavigationScreen from '../screens/EnhancedNavigationScreen';
import VehicleProfileScreen from '../screens/VehicleProfileScreen';
import RouteOptimizationScreen from '../screens/RouteOptimizationScreen';
import CustomerNotificationsScreen from '../screens/CustomerNotificationsScreen';

export type MainStackParamList = {
  MainTabs: undefined | { screen: keyof MainTabParamList; params?: object };
  DeliveryDetail: { deliveryId: string };
  Signature: { deliveryId: string };
  PhotoCapture: { deliveryId: string };
  VehicleSelection: undefined;
  Messages: undefined;
  Conversation: { conversationId: string; recipientName: string };
  Settings: undefined;
  Help: undefined;
  LocationTracking: undefined;
  EnhancedNavigation: { deliveryId: string };
  VehicleProfile: undefined;
  RouteOptimization: undefined;
  CustomerNotifications: { deliveryId?: string };
};

export type MainTabParamList = {
  Home: undefined;
  Deliveries: undefined;
  Navigation: undefined;
  Schedule: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<MainStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

const MainTabs: React.FC = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'home';

          if (route.name === 'Home') {
            iconName = focused ? 'home' : 'home-outline';
          } else if (route.name === 'Deliveries') {
            iconName = focused ? 'cube' : 'cube-outline';
          } else if (route.name === 'Navigation') {
            iconName = focused ? 'navigate' : 'navigate-outline';
          } else if (route.name === 'Schedule') {
            iconName = focused ? 'calendar' : 'calendar-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.app.tabBarActive,
        tabBarInactiveTintColor: theme.app.tabBarInactive,
        headerShown: true,
        headerStyle: {
          backgroundColor: theme.app.headerBackground,
        },
        headerTintColor: theme.app.headerTint,
        headerTitleStyle: {
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen name="Home" component={HomeScreen} options={{ title: 'Dashboard' }} />
      <Tab.Screen name="Deliveries" component={DeliveriesScreen} options={{ title: 'Deliveries' }} />
      <Tab.Screen name="Navigation" component={NavigationScreen} options={{ title: 'Navigation' }} />
      <Tab.Screen name="Schedule" component={ScheduleScreen} options={{ title: 'Schedule' }} />
      <Tab.Screen name="Profile" component={ProfileScreen} options={{ title: 'Profile' }} />
    </Tab.Navigator>
  );
};

const MainNavigator: React.FC = () => {
  return (
    <Stack.Navigator
      initialRouteName="MainTabs"
      screenOptions={{
        headerShown: false,
      }}
    >
      <Stack.Screen name="MainTabs" component={MainTabs} />
      <Stack.Screen 
        name="DeliveryDetail" 
        component={DeliveryDetailScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Delivery Details'
        }} 
      />
      <Stack.Screen 
        name="Signature" 
        component={SignatureScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Capture Signature'
        }} 
      />
      <Stack.Screen 
        name="PhotoCapture" 
        component={PhotoCaptureScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Capture Photo'
        }} 
      />
      <Stack.Screen 
        name="VehicleSelection" 
        component={VehicleSelectionScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Select Vehicle'
        }} 
      />
      <Stack.Screen 
        name="Messages" 
        component={MessagesScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Messages'
        }} 
      />
      <Stack.Screen 
        name="Conversation" 
        component={ConversationScreen} 
        options={({ route }) => ({ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: route.params.recipientName
        })} 
      />
      <Stack.Screen 
        name="Settings" 
        component={SettingsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Settings'
        }} 
      />
      <Stack.Screen 
        name="Help" 
        component={HelpScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Help & Support'
        }} 
      />
      <Stack.Screen 
        name="LocationTracking" 
        component={LocationTrackingScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Location Tracking'
        }} 
      />
      <Stack.Screen 
        name="EnhancedNavigation" 
        component={EnhancedNavigationScreen} 
        options={{ 
          headerShown: false,
          title: 'Enhanced Navigation'
        }} 
      />
      <Stack.Screen 
        name="VehicleProfile" 
        component={VehicleProfileScreen} 
        options={{ 
          headerShown: false,
          title: 'Vehicle Profile'
        }} 
      />
      <Stack.Screen 
        name="RouteOptimization" 
        component={RouteOptimizationScreen} 
        options={{ 
          headerShown: false,
          title: 'Route Optimization'
        }} 
      />
      <Stack.Screen 
        name="CustomerNotifications" 
        component={CustomerNotificationsScreen} 
        options={{ 
          headerShown: true,
          headerStyle: { backgroundColor: theme.app.headerBackground },
          headerTintColor: theme.app.headerTint,
          title: 'Customer Notifications'
        }} 
      />
    </Stack.Navigator>
  );
};

export default MainNavigator;
