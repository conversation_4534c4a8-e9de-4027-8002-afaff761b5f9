{"name": "nxtacre-shared", "version": "1.0.0", "description": "Shared components and utilities for NxtAcre mobile apps", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/netinfo": "^11.4.1", "@react-native/assets-registry": "^0.79.0", "date-fns": "^4.1.0", "expo": "~53.0.12", "expo-asset": "~10.0.10", "expo-constants": "~16.0.2", "expo-device": "~6.0.2", "expo-modules-core": "~1.12.24", "expo-notifications": "~0.31.3", "expo-speech": "^13.1.7", "react": "19.0.0", "react-native": "0.79.4", "react-native-gesture-handler": "~2.20.2", "react-native-reanimated": "~3.16.1", "sentry-expo": "~7.1.1", "zustand": "^5.0.5"}, "peerDependencies": {"react": "19.0.0", "react-native": "0.79.4"}, "devDependencies": {"@types/react": "~19.1.8", "typescript": "~5.8.3"}, "author": "NxtAcre", "license": "UNLICENSED", "private": true}