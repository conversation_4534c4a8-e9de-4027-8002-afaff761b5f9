// Mock for DeprecatedViewPropTypes
// This file is created to resolve the error: "Unable to resolve "./DeprecatedViewPropTypes" from "../../shared/node_modules/deprecated-react-native-prop-types/index.js"

import { ViewStyle, TextStyle, ColorValue } from 'react-native';
import PropTypes from 'prop-types';

// Create a mock of ViewPropTypes based on common properties
const ViewPropTypes = {
  style: PropTypes.oneOfType([
    PropTypes.shape({}),
    PropTypes.number,
    PropTypes.array,
  ]),
  // Add other common view prop types as needed
};

export default ViewPropTypes;
