declare module 'nxtacre-shared' {
  import React from 'react';
  import { EventSubscription } from 'expo-modules-core';

  // Components
  export const SplashScreen: React.ComponentType<{ onFinish?: () => void }>;
  export const HelpTip: React.ComponentType<{ title: string, content: string }>;
  export const UnifiedNotificationCenter: React.ComponentType;
  export const NotificationButton: React.ComponentType;
  export const NotificationPreferencesScreen: React.ComponentType;

  // Store
  export const AuthProvider: React.ComponentType<{ children: React.ReactNode }>;
  export function useAuth(): any;
  export const useNotificationStore: {
    (): any;
    getState: () => {
      isNotificationsEnabled: boolean;
      notificationSettings: {
        taskAssignments: boolean;
        dueDateAlerts: boolean;
        statusChanges: boolean;
        systemAnnouncements: boolean;
        newDeliveries: boolean;
        deliveryUpdates: boolean;
        scheduleChanges: boolean;
        messages: boolean;
      };
      pushToken: string | null;
      expoPushToken: string | null;
      initializePushNotifications: () => Promise<void>;
      toggleNotifications: (enabled: boolean) => void;
      updateNotificationSetting: (key: string, value: boolean) => void;
      saveNotificationSettings: () => Promise<void>;
    };
  };

  // Utils
  export function initializeNativeEventEmitterFix(): void;

  // Hooks
  export function useSynchronization(): { isInitialized: boolean, isOnline: boolean };
  export function useEnhancedSynchronization(): { isInitialized: boolean, isOnline: boolean };

  // Services
  export function addNotificationReceivedListener(callback: (notification: any) => void): EventSubscription;
  export function addNotificationResponseReceivedListener(callback: (response: any) => void): EventSubscription;

  // Additional service exports
  export const deepLinkingService: any;
  export const emailService: any;
  export const etaUpdateService: any;
  export const expenseService: any;
  export const financialService: any;
  export const navigationHistoryService: any;
  export const navigationService: any;
  export const offlineMapService: any;
  export const smsService: any;
  export const voiceCommandService: any;
}
